#!/usr/bin/env python3
"""
Batch crawl script for Gerry's Diamond Setting Essays Blog
This processes posts in smaller batches and can be resumed.
"""

import sys
import traceback
from pathlib import Path
import time
import sqlite3

# Add current directory to path
sys.path.insert(0, str(Path(__file__).parent))

from crawler import BlogCrawler
import config

def get_crawl_progress():
    """Get current crawl progress from database"""
    try:
        conn = sqlite3.connect(config.DATABASE_PATH)
        cursor = conn.cursor()
        
        cursor.execute("SELECT COUNT(*) FROM posts")
        posts_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM images")
        images_count = cursor.fetchone()[0]
        
        conn.close()
        return posts_count, images_count
    except:
        return 0, 0

def main():
    """Run batch crawl with progress tracking"""
    print("=" * 80)
    print("Gerry's Diamond Setting Essays Blog - BATCH CRAWL")
    print("=" * 80)
    print()
    
    # Check current progress
    posts_done, images_done = get_crawl_progress()
    print(f"📊 Current Progress:")
    print(f"   📄 Posts Downloaded: {posts_done}")
    print(f"   🖼️  Images Downloaded: {images_done}")
    print()
    
    print(f"📍 Base URL: {config.BASE_URL}")
    print(f"📁 Output Directory: {config.OUTPUT_DIR}")
    print(f"🔧 Test Mode: {config.TEST_MODE}")
    print(f"🖼️  Convert to PNG: {config.CONVERT_TO_PNG}")
    print(f"⚡ Request Delay: {config.REQUEST_DELAY}s")
    print()
    
    if config.TEST_MODE:
        print("⚠️  WARNING: Test mode is enabled!")
        print("   This will only download a limited number of posts.")
        print("   Set TEST_MODE = False in config.py for full crawl.")
        print()
    
    # Confirm before starting
    response = input("🤔 Continue with batch crawl? (y/N): ")
    if response.lower() != 'y':
        print("❌ Crawl cancelled.")
        return
    
    print()
    print("🎬 Starting batch crawl...")
    print("💡 Processing posts in batches for better reliability")
    print("📊 You can interrupt and resume at any time")
    print()
    
    start_time = time.time()
    batch_size = 10  # Process 10 posts at a time
    
    try:
        # Initialize crawler
        print("🔧 Initializing crawler...")
        crawler = BlogCrawler()
        
        # Get all archive URLs first
        print("🔍 Discovering archive URLs...")
        archive_urls = crawler.discover_archive_urls()
        print(f"📚 Found {len(archive_urls)} archive pages")
        
        # Process archives in batches
        total_posts_processed = 0
        total_images_processed = 0
        
        for i, archive_url in enumerate(archive_urls, 1):
            print(f"\n📖 Processing archive {i}/{len(archive_urls)}: {archive_url}")
            
            try:
                # Get posts from this archive
                posts = crawler.extract_posts_from_page(archive_url)
                print(f"   Found {len(posts)} posts in this archive")
                
                # Process posts in this archive
                for j, post in enumerate(posts, 1):
                    try:
                        print(f"   📄 Processing post {j}/{len(posts)}: {post['title'][:50]}...")
                        
                        # Check if already processed
                        if crawler.is_post_downloaded(post['url']):
                            print(f"   ⏭️  Skipping (already downloaded)")
                            continue
                        
                        # Download and process the post
                        success = crawler.download_post_content(post)
                        if success:
                            total_posts_processed += 1
                            print(f"   ✅ Success! Total posts: {total_posts_processed}")
                        else:
                            print(f"   ❌ Failed to process post")
                            
                    except KeyboardInterrupt:
                        raise
                    except Exception as e:
                        print(f"   ❌ Error processing post: {e}")
                        continue
                        
                # Small delay between archives
                time.sleep(1)
                
            except KeyboardInterrupt:
                raise
            except Exception as e:
                print(f"❌ Error processing archive {archive_url}: {e}")
                continue
        
        # Generate final summary and index
        print("\n🎯 Generating final index and summary...")
        crawler.generate_summary()
        crawler.generate_index()
        
        end_time = time.time()
        duration = end_time - start_time
        hours = int(duration // 3600)
        minutes = int((duration % 3600) // 60)
        seconds = int(duration % 60)
        
        print()
        print("=" * 80)
        print("🎉 BATCH CRAWL COMPLETED!")
        print("=" * 80)
        print()
        print(f"⏱️  Total Time: {hours:02d}:{minutes:02d}:{seconds:02d}")
        print(f"📄 Posts Downloaded: {crawler.posts_downloaded}")
        print(f"🖼️  Images Downloaded: {crawler.images_downloaded}")
        print()
        print("📁 Results saved to:")
        print(f"   📖 Index: {config.OUTPUT_DIR}/index.html")
        print(f"   📚 Posts: {config.ARCHIVE_DIR}")
        print(f"   🖼️  Images: {config.IMAGES_DIR}")
        print(f"   📊 Metadata: {config.METADATA_DIR}")
        print()
        print("🌐 Open the index.html file in your browser to browse the archive!")
        
    except KeyboardInterrupt:
        print(f"\n⏹️  Crawl interrupted by user.")
        print(f"📊 Progress saved - you can resume later!")
        print(f"📄 Posts processed in this session: {total_posts_processed}")
        
        # Generate index with current progress
        try:
            print("🎯 Generating index with current progress...")
            crawler.generate_index()
            print("✅ Index updated successfully")
        except:
            print("❌ Could not update index")
            
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Error during crawl: {e}")
        print("\n🔍 Full traceback:")
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
