#!/usr/bin/env python3
"""
Archive Structure Crawler

This script extracts the exact archive structure from the blog's sidebar
and uses it to systematically download articles by following the 
date-based directory structure shown in the blog's archive.

From the screenshot, we can see:
- 2024 (227) with monthly breakdowns like 12/24 (46), 11/24 (45), etc.
- 2023 (133) with similar monthly structure
- etc.

This approach should be much more accurate than searching.
"""

import requests
import json
import re
import time
from pathlib import Path
from collections import defaultdict
from typing import Dict, List, Set, Tuple
from datetime import datetime
from bs4 import BeautifulSoup
from urllib.parse import urljoin

import config
from crawler import BlogCrawler

class ArchiveStructureCrawler:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        self.archive_structure = {}  # year -> {month: count, total: count}
        self.discovered_articles = {}  # URL -> article_info
        self.existing_articles = set()  # Already downloaded URLs
        
    def extract_archive_structure(self) -> Dict:
        """Extract the archive structure from the blog's sidebar"""
        print("📊 Extracting archive structure from blog sidebar...")
        
        try:
            response = self.session.get(config.BASE_URL, timeout=30)
            if not response or response.status_code != 200:
                print("❌ Failed to fetch homepage")
                return {}
                
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Look for the blog archive section
            # Try different possible selectors for the archive widget
            archive_selectors = [
                '#BlogArchive1',
                '.BlogArchive',
                '[id*="Archive"]',
                '.widget-content ul',
                '.archive-widget'
            ]
            
            archive_section = None
            for selector in archive_selectors:
                archive_section = soup.select_one(selector)
                if archive_section:
                    print(f"✅ Found archive section using selector: {selector}")
                    break
            
            if not archive_section:
                print("⚠️  Archive section not found, trying text parsing...")
                # Fallback: parse the entire page text for year/count patterns
                return self._parse_archive_from_text(soup.get_text())
            
            # Extract year and monthly data
            archive_structure = {}
            
            # Look for year patterns like "2024 (227)"
            year_pattern = re.compile(r'(\d{4})\s*\((\d+)\)')
            month_pattern = re.compile(r'(\d{1,2})/(\d{2})\s*\((\d+)\)')
            
            # Get all text from archive section
            archive_text = archive_section.get_text()
            print(f"📄 Archive section text preview: {archive_text[:200]}...")
            
            # Find all year matches
            year_matches = year_pattern.findall(archive_text)
            print(f"📅 Found year patterns: {year_matches}")
            
            for year_str, count_str in year_matches:
                year = int(year_str)
                total_count = int(count_str)
                archive_structure[year] = {'total': total_count, 'months': {}}
            
            # Find monthly breakdowns
            month_matches = month_pattern.findall(archive_text)
            print(f"📅 Found month patterns: {month_matches}")
            
            # Group months by year (assuming they appear after the year)
            current_year = None
            for month_str, year_suffix, count_str in month_matches:
                month = int(month_str)
                count = int(count_str)
                
                # Determine year from suffix (e.g., "24" -> 2024)
                if len(year_suffix) == 2:
                    if int(year_suffix) >= 18:  # Assuming blog started around 2018
                        year = 2000 + int(year_suffix)
                    else:
                        year = 2000 + int(year_suffix)
                
                if year in archive_structure:
                    archive_structure[year]['months'][month] = count
            
            print(f"📊 Extracted archive structure: {archive_structure}")
            return archive_structure
            
        except Exception as e:
            print(f"❌ Error extracting archive structure: {e}")
            return {}
    
    def _parse_archive_from_text(self, page_text: str) -> Dict:
        """Fallback method to parse archive from page text"""
        print("📄 Parsing archive structure from page text...")
        
        archive_structure = {}
        
        # Look for year patterns in the entire page
        year_pattern = re.compile(r'(\d{4})\s*\((\d+)\)')
        month_pattern = re.compile(r'(\d{1,2})/(\d{2})\s*\((\d+)\)')
        
        year_matches = year_pattern.findall(page_text)
        month_matches = month_pattern.findall(page_text)
        
        print(f"📅 Text parsing - Years: {year_matches}")
        print(f"📅 Text parsing - Months: {month_matches}")
        
        # Build structure
        for year_str, count_str in year_matches:
            year = int(year_str)
            if 2018 <= year <= 2025:  # Reasonable year range
                archive_structure[year] = {'total': int(count_str), 'months': {}}
        
        return archive_structure
    
    def get_existing_articles_for_year(self, year: int) -> Set[str]:
        """Get existing articles for a specific year"""
        existing = set()
        
        archive_dir = Path(config.OUTPUT_DIR) / "archive" / str(year)
        if archive_dir.exists():
            for html_file in archive_dir.glob("*.html"):
                try:
                    with open(html_file, 'r', encoding='utf-8') as f:
                        content = f.read()
                        
                    soup = BeautifulSoup(content, 'html.parser')
                    original_link = soup.find('a', string='View Original')
                    
                    if original_link and original_link.get('href'):
                        existing.add(original_link['href'])
                        
                except Exception:
                    continue
        
        return existing
    
    def scan_year_using_archive_structure(self, year: int, expected_count: int) -> List[str]:
        """Scan a specific year using multiple methods until we find expected count"""
        print(f"\n🎯 Scanning {year} - Expected: {expected_count} articles")
        
        existing_articles = self.get_existing_articles_for_year(year)
        print(f"📁 Already have: {len(existing_articles)} articles for {year}")
        
        if len(existing_articles) >= expected_count:
            print(f"✅ {year} appears complete ({len(existing_articles)}/{expected_count})")
            return []
        
        missing_count = expected_count - len(existing_articles)
        print(f"🔍 Need to find: {missing_count} missing articles for {year}")
        
        discovered_urls = set()
        
        # Method 1: Year-based search
        print(f"   🔍 Method 1: Year search for {year}")
        search_url = f"https://gerrysdiamondsettingessays.blogspot.com/search?updated-min={year}-01-01T00:00:00-05:00&updated-max={year+1}-01-01T00:00:00-05:00&max-results=500"
        
        try:
            response = self.session.get(search_url, timeout=30)
            if response.status_code == 200:
                soup = BeautifulSoup(response.content, 'html.parser')
                post_containers = soup.select(config.SELECTORS["post_container"])
                
                for container in post_containers:
                    try:
                        title_elem = container.select_one(config.SELECTORS["post_title"])
                        if title_elem and title_elem.get('href'):
                            url = title_elem.get('href')
                            
                            # Verify year and not already existing
                            year_match = re.search(r'/(\d{4})/', url)
                            if (year_match and int(year_match.group(1)) == year and 
                                url not in existing_articles):
                                discovered_urls.add(url)
                    except:
                        continue
                        
                print(f"      📄 Found {len(discovered_urls)} articles via search")
        except Exception as e:
            print(f"      ❌ Search failed: {e}")
        
        # Method 2: Monthly archives with multiple URL formats
        print(f"   📅 Method 2: Monthly archives for {year}")
        for month in range(1, 13):
            # Try multiple archive URL formats
            archive_urls = [
                f"https://gerrysdiamondsettingessays.blogspot.com/{year}_{month:02d}_01_archive.html",
                f"https://gerrysdiamondsettingessays.blogspot.com/{year}_{month:02d}_15_archive.html",
                f"https://gerrysdiamondsettingessays.blogspot.com/{year}/{month:02d}/",
                f"https://gerrysdiamondsettingessays.blogspot.com/search?updated-min={year}-{month:02d}-01T00:00:00-05:00&updated-max={year}-{month:02d}-31T23:59:59-05:00&max-results=100",
            ]

            month_found = 0
            for archive_url in archive_urls:
                try:
                    print(f"      🔗 Trying: {archive_url}")
                    response = self.session.get(archive_url, timeout=20)
                    if response.status_code == 200:
                        soup = BeautifulSoup(response.content, 'html.parser')
                        post_containers = soup.select(config.SELECTORS["post_container"])

                        if post_containers:
                            print(f"         📄 Found {len(post_containers)} containers")

                            for container in post_containers:
                                try:
                                    title_elem = container.select_one(config.SELECTORS["post_title"])
                                    if title_elem and title_elem.get('href'):
                                        url = title_elem.get('href')
                                        title = title_elem.get_text(strip=True)

                                        if url not in existing_articles and url not in discovered_urls:
                                            year_match = re.search(r'/(\d{4})/', url)
                                            if year_match and int(year_match.group(1)) == year:
                                                discovered_urls.add(url)
                                                month_found += 1
                                                print(f"         ✅ Found: {title[:50]}...")
                                except:
                                    continue

                            if month_found > 0:
                                break  # Found articles, no need to try other URLs for this month
                        else:
                            print(f"         📄 No containers found")
                    else:
                        print(f"         ❌ HTTP {response.status_code}")

                except Exception as e:
                    print(f"         ❌ Error: {e}")
                    continue

                time.sleep(0.5)  # Be respectful

            if month_found > 0:
                print(f"      📅 Month {month:02d}: +{month_found} articles")
            else:
                print(f"      📅 Month {month:02d}: No new articles found")
        
        # Method 3: Try direct blog pagination
        print(f"   📄 Method 3: Blog pagination for {year}")
        try:
            base_url = f"https://gerrysdiamondsettingessays.blogspot.com/{year}/"
            print(f"      🔗 Trying: {base_url}")

            response = self.session.get(base_url, timeout=30)
            if response.status_code == 200:
                soup = BeautifulSoup(response.content, 'html.parser')
                post_containers = soup.select(config.SELECTORS["post_container"])

                print(f"         📄 Found {len(post_containers)} containers on year page")

                for container in post_containers:
                    try:
                        title_elem = container.select_one(config.SELECTORS["post_title"])
                        if title_elem and title_elem.get('href'):
                            url = title_elem.get('href')
                            title = title_elem.get_text(strip=True)

                            if url not in existing_articles and url not in discovered_urls:
                                year_match = re.search(r'/(\d{4})/', url)
                                if year_match and int(year_match.group(1)) == year:
                                    discovered_urls.add(url)
                                    print(f"         ✅ Found: {title[:50]}...")
                    except:
                        continue
            else:
                print(f"         ❌ HTTP {response.status_code}")

        except Exception as e:
            print(f"         ❌ Error: {e}")

        total_found = len(discovered_urls)
        print(f"   ✅ Total discovered for {year}: {total_found} articles")
        print(f"   📊 Coverage: {len(existing_articles) + total_found}/{expected_count} articles")

        if len(existing_articles) + total_found < expected_count:
            still_missing = expected_count - len(existing_articles) - total_found
            print(f"   ⚠️  Still missing: {still_missing} articles for {year}")

        return list(discovered_urls)
    
    def download_articles(self, urls: List[str], year: int):
        """Download the discovered articles"""
        if not urls:
            print(f"✅ No articles to download for {year}")
            return 0
            
        print(f"⬇️  Downloading {len(urls)} articles for {year}...")
        
        crawler = BlogCrawler()
        success_count = 0
        
        for i, url in enumerate(urls, 1):
            print(f"📄 {i}/{len(urls)}: {url}")
            
            # Create a basic post object
            post = {
                'url': url,
                'title': f"Article {i}",  # Will be extracted during download
                'date': '',
                'author': 'Gerry Lewy'
            }
            
            success = crawler.download_post_content(post)
            if success:
                success_count += 1
                print(f"   ✅ Downloaded successfully")
                crawler.generate_index()
            else:
                print(f"   ❌ Download failed")
            
            time.sleep(config.REQUEST_DELAY)
        
        print(f"✅ Downloaded {success_count}/{len(urls)} articles for {year}")
        return success_count

def main():
    print("=" * 80)
    print("📊 ARCHIVE STRUCTURE CRAWLER")
    print("=" * 80)
    print("Using blog's sidebar archive structure for systematic crawling")
    print()
    
    crawler = ArchiveStructureCrawler()
    
    # Step 1: Extract archive structure
    archive_structure = crawler.extract_archive_structure()
    
    if not archive_structure:
        print("❌ Could not extract archive structure. Using manual structure...")
        # Fallback to known structure from screenshot
        archive_structure = {
            2025: {'total': 101, 'months': {}},
            2024: {'total': 227, 'months': {}},
            2023: {'total': 133, 'months': {}},
            2022: {'total': 66, 'months': {}},
            2021: {'total': 13, 'months': {}},
            2020: {'total': 1, 'months': {}},
            2019: {'total': 50, 'months': {}},
            2018: {'total': 77, 'months': {}}
        }
    
    print(f"📊 Archive structure extracted:")
    for year in sorted(archive_structure.keys(), reverse=True):
        total = archive_structure[year]['total']
        print(f"   {year}: {total} articles")
    
    # Step 2: Focus on 2024 first (highest priority)
    target_year = 2024
    expected_count = archive_structure.get(target_year, {}).get('total', 227)
    
    print(f"\n🎯 Focusing on {target_year} first")
    print(f"📊 Expected articles: {expected_count}")
    
    # Step 3: Scan and discover missing articles
    missing_urls = crawler.scan_year_using_archive_structure(target_year, expected_count)
    
    # Step 4: Download missing articles
    if missing_urls:
        response = input(f"\n🤔 Found {len(missing_urls)} missing articles for {target_year}. Download them? (y/N): ")
        if response.lower() == 'y':
            success_count = crawler.download_articles(missing_urls, target_year)
            print(f"\n✅ Successfully downloaded {success_count} articles for {target_year}")
        else:
            print("📋 Discovery complete. Run again to download.")
    else:
        print(f"\n✅ No missing articles found for {target_year}")
    
    print(f"\n🎯 Next steps: Repeat for other years (2022: 66, 2023: 133, etc.)")

if __name__ == "__main__":
    main()
