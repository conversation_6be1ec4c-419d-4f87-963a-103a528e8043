#!/usr/bin/env python3
"""
Comprehensive Link Discovery for Specific Year

This script uses multiple scanning methods and runs them multiple times
to ensure we capture ALL article links for a specific year.

Methods used:
1. Year search with date ranges (multiple variations)
2. Monthly archive pages (all possible formats)
3. Main blog pagination scanning
4. Sitemap scanning (if available)
5. RSS feed scanning
6. Archive sidebar link extraction

Each method is run multiple times with different parameters to ensure
maximum coverage.
"""

import requests
import json
import re
import time
from pathlib import Path
from collections import defaultdict, Counter
from typing import Dict, List, Set, Tuple
from datetime import datetime, timedelta
from bs4 import BeautifulSoup
from urllib.parse import urljoin, urlparse, quote
import xml.etree.ElementTree as ET

import config

class ComprehensiveLinkDiscovery:
    def __init__(self, target_year: int = 2024):
        self.target_year = target_year
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        self.all_discovered_links = set()  # All unique URLs found
        self.link_sources = defaultdict(list)  # URL -> list of sources that found it
        self.scan_results = {}  # method_name -> results
        
    def log_discovery(self, url: str, source: str, title: str = ""):
        """Log a discovered link with its source"""
        if url not in self.all_discovered_links:
            self.all_discovered_links.add(url)
            print(f"   🆕 NEW: {title[:50]}...")
        else:
            print(f"   🔄 DUP: {title[:50]}...")
        
        self.link_sources[url].append(source)
    
    def method_1_year_search_variations(self) -> Set[str]:
        """Method 1: Year search with multiple variations"""
        print(f"\n1️⃣ METHOD 1: Year Search Variations for {self.target_year}")
        
        discovered_urls = set()
        
        # Different search URL variations
        search_variations = [
            # Standard date range search
            f"https://gerrysdiamondsettingessays.blogspot.com/search?updated-min={self.target_year}-01-01T00:00:00-05:00&updated-max={self.target_year+1}-01-01T00:00:00-05:00&max-results=500",
            
            # Different max-results values
            f"https://gerrysdiamondsettingessays.blogspot.com/search?updated-min={self.target_year}-01-01T00:00:00-05:00&updated-max={self.target_year+1}-01-01T00:00:00-05:00&max-results=200",
            f"https://gerrysdiamondsettingessays.blogspot.com/search?updated-min={self.target_year}-01-01T00:00:00-05:00&updated-max={self.target_year+1}-01-01T00:00:00-05:00&max-results=100",
            
            # Different timezone variations
            f"https://gerrysdiamondsettingessays.blogspot.com/search?updated-min={self.target_year}-01-01T00:00:00-04:00&updated-max={self.target_year+1}-01-01T00:00:00-04:00&max-results=500",
            f"https://gerrysdiamondsettingessays.blogspot.com/search?updated-min={self.target_year}-01-01T00:00:00Z&updated-max={self.target_year+1}-01-01T00:00:00Z&max-results=500",
        ]
        
        for i, search_url in enumerate(search_variations, 1):
            print(f"   🔍 Variation {i}/{len(search_variations)}: {search_url[:80]}...")
            
            try:
                response = self.session.get(search_url, timeout=30)
                if response.status_code == 200:
                    soup = BeautifulSoup(response.content, 'html.parser')
                    post_containers = soup.select(config.SELECTORS["post_container"])
                    
                    print(f"      📄 Found {len(post_containers)} containers")
                    
                    for container in post_containers:
                        try:
                            title_elem = container.select_one(config.SELECTORS["post_title"])
                            if title_elem and title_elem.get('href'):
                                url = title_elem.get('href')
                                title = title_elem.get_text(strip=True)
                                
                                # Verify year
                                year_match = re.search(r'/(\d{4})/', url)
                                if year_match and int(year_match.group(1)) == self.target_year:
                                    discovered_urls.add(url)
                                    self.log_discovery(url, f"method1_var{i}", title)
                        except:
                            continue
                else:
                    print(f"      ❌ HTTP {response.status_code}")
                    
            except Exception as e:
                print(f"      ❌ Error: {e}")
            
            time.sleep(1)  # Be respectful
        
        print(f"   ✅ Method 1 found {len(discovered_urls)} unique URLs")
        return discovered_urls
    
    def method_2_monthly_archives_comprehensive(self) -> Set[str]:
        """Method 2: Comprehensive monthly archive scanning"""
        print(f"\n2️⃣ METHOD 2: Monthly Archives for {self.target_year}")
        
        discovered_urls = set()
        
        # Try multiple archive URL formats for each month
        for month in range(1, 13):
            print(f"   📅 Month {month:02d}...")
            
            archive_formats = [
                f"https://gerrysdiamondsettingessays.blogspot.com/{self.target_year}_{month:02d}_01_archive.html",
                f"https://gerrysdiamondsettingessays.blogspot.com/{self.target_year}_{month:02d}_15_archive.html",
                f"https://gerrysdiamondsettingessays.blogspot.com/{self.target_year}/{month:02d}/",
                f"https://gerrysdiamondsettingessays.blogspot.com/{self.target_year}_{month:02d}/",
                f"https://gerrysdiamondsettingessays.blogspot.com/search?updated-min={self.target_year}-{month:02d}-01T00:00:00-05:00&updated-max={self.target_year}-{month:02d}-31T23:59:59-05:00&max-results=100",
            ]
            
            for format_idx, archive_url in enumerate(archive_formats):
                try:
                    response = self.session.get(archive_url, timeout=20)
                    if response.status_code == 200:
                        soup = BeautifulSoup(response.content, 'html.parser')
                        post_containers = soup.select(config.SELECTORS["post_container"])
                        
                        if post_containers:
                            print(f"      📄 Format {format_idx+1}: {len(post_containers)} containers")
                            
                            for container in post_containers:
                                try:
                                    title_elem = container.select_one(config.SELECTORS["post_title"])
                                    if title_elem and title_elem.get('href'):
                                        url = title_elem.get('href')
                                        title = title_elem.get_text(strip=True)
                                        
                                        # Verify year
                                        year_match = re.search(r'/(\d{4})/', url)
                                        if year_match and int(year_match.group(1)) == self.target_year:
                                            discovered_urls.add(url)
                                            self.log_discovery(url, f"method2_m{month:02d}_f{format_idx+1}", title)
                                except:
                                    continue
                            break  # Found articles for this month, no need to try other formats
                except:
                    continue
                
                time.sleep(0.5)
        
        print(f"   ✅ Method 2 found {len(discovered_urls)} unique URLs")
        return discovered_urls
    
    def method_3_pagination_deep_scan(self) -> Set[str]:
        """Method 3: Deep pagination scanning"""
        print(f"\n3️⃣ METHOD 3: Deep Pagination Scan for {self.target_year}")
        
        discovered_urls = set()
        
        # Try different pagination approaches
        pagination_urls = [
            f"https://gerrysdiamondsettingessays.blogspot.com/{self.target_year}/",
            f"https://gerrysdiamondsettingessays.blogspot.com/search/label/{self.target_year}",
        ]
        
        for base_url in pagination_urls:
            print(f"   🔗 Base URL: {base_url}")
            
            page = 1
            max_pages = 30  # Increased limit
            consecutive_empty = 0
            
            while page <= max_pages and consecutive_empty < 3:
                if page == 1:
                    url = base_url
                else:
                    url = f"{base_url}?max-results=20&start={((page-1)*20)+1}"
                
                try:
                    response = self.session.get(url, timeout=20)
                    if response.status_code == 200:
                        soup = BeautifulSoup(response.content, 'html.parser')
                        post_containers = soup.select(config.SELECTORS["post_container"])
                        
                        if post_containers:
                            print(f"      📄 Page {page}: {len(post_containers)} containers")
                            consecutive_empty = 0
                            
                            for container in post_containers:
                                try:
                                    title_elem = container.select_one(config.SELECTORS["post_title"])
                                    if title_elem and title_elem.get('href'):
                                        url_found = title_elem.get('href')
                                        title = title_elem.get_text(strip=True)
                                        
                                        # Verify year
                                        year_match = re.search(r'/(\d{4})/', url_found)
                                        if year_match and int(year_match.group(1)) == self.target_year:
                                            discovered_urls.add(url_found)
                                            self.log_discovery(url_found, f"method3_p{page}", title)
                                except:
                                    continue
                        else:
                            consecutive_empty += 1
                            print(f"      📄 Page {page}: No containers (empty #{consecutive_empty})")
                    else:
                        consecutive_empty += 1
                        
                except Exception as e:
                    consecutive_empty += 1
                    print(f"      ❌ Page {page}: Error - {e}")
                
                page += 1
                time.sleep(1)
        
        print(f"   ✅ Method 3 found {len(discovered_urls)} unique URLs")
        return discovered_urls
    
    def method_4_sitemap_and_feeds(self) -> Set[str]:
        """Method 4: Sitemap and RSS feed scanning"""
        print(f"\n4️⃣ METHOD 4: Sitemap and RSS Feeds")
        
        discovered_urls = set()
        
        # Try different sitemap and feed URLs
        feed_urls = [
            "https://gerrysdiamondsettingessays.blogspot.com/sitemap.xml",
            "https://gerrysdiamondsettingessays.blogspot.com/feeds/posts/default",
            "https://gerrysdiamondsettingessays.blogspot.com/feeds/posts/default?max-results=500",
            "https://gerrysdiamondsettingessays.blogspot.com/atom.xml",
            "https://gerrysdiamondsettingessays.blogspot.com/rss.xml",
        ]
        
        for feed_url in feed_urls:
            print(f"   🌐 Trying: {feed_url}")
            
            try:
                response = self.session.get(feed_url, timeout=20)
                if response.status_code == 200:
                    content = response.text
                    
                    # Look for URLs in the content
                    url_pattern = rf'https://gerrysdiamondsettingessays\.blogspot\.com/{self.target_year}/\d+/\d+/[^"\s<>]+'
                    urls_found = re.findall(url_pattern, content)
                    
                    print(f"      📄 Found {len(urls_found)} URLs in feed")
                    
                    for url in urls_found:
                        discovered_urls.add(url)
                        self.log_discovery(url, "method4_feed", "")
                        
            except Exception as e:
                print(f"      ❌ Error: {e}")
        
        print(f"   ✅ Method 4 found {len(discovered_urls)} unique URLs")
        return discovered_urls
    
    def run_comprehensive_discovery(self) -> Dict:
        """Run all discovery methods multiple times"""
        print("=" * 80)
        print(f"🔍 COMPREHENSIVE LINK DISCOVERY FOR {self.target_year}")
        print("=" * 80)
        print(f"🎯 Target: Find all {self.target_year} article links")
        print(f"📊 Expected: ~227 articles (from blog sidebar)")
        print()
        
        # Run each method
        method1_urls = self.method_1_year_search_variations()
        method2_urls = self.method_2_monthly_archives_comprehensive()
        method3_urls = self.method_3_pagination_deep_scan()
        method4_urls = self.method_4_sitemap_and_feeds()
        
        # Combine all results
        all_urls = method1_urls | method2_urls | method3_urls | method4_urls
        
        # Analysis
        print("\n" + "=" * 80)
        print("📊 DISCOVERY ANALYSIS")
        print("=" * 80)
        
        print(f"🔍 Method 1 (Year Search): {len(method1_urls)} URLs")
        print(f"📅 Method 2 (Monthly Archives): {len(method2_urls)} URLs")
        print(f"📄 Method 3 (Deep Pagination): {len(method3_urls)} URLs")
        print(f"🌐 Method 4 (Sitemaps/Feeds): {len(method4_urls)} URLs")
        print(f"🔄 Total Unique URLs: {len(all_urls)} URLs")
        print(f"📊 Expected vs Found: 227 expected, {len(all_urls)} found")
        
        if len(all_urls) < 227:
            print(f"⚠️  MISSING: {227 - len(all_urls)} articles still not found!")
        elif len(all_urls) > 227:
            print(f"➕ EXTRA: {len(all_urls) - 227} more than expected (might include duplicates)")
        else:
            print("✅ PERFECT MATCH!")
        
        # Save detailed results
        results = {
            'discovery_date': datetime.now().isoformat(),
            'target_year': self.target_year,
            'expected_count': 227,
            'found_count': len(all_urls),
            'method_results': {
                'method1_year_search': len(method1_urls),
                'method2_monthly_archives': len(method2_urls),
                'method3_deep_pagination': len(method3_urls),
                'method4_sitemaps_feeds': len(method4_urls),
            },
            'all_urls': sorted(list(all_urls)),
            'url_sources': {url: sources for url, sources in self.link_sources.items()}
        }
        
        # Save to file
        output_path = Path(config.OUTPUT_DIR) / "metadata" / f"link_discovery_{self.target_year}.json"
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2)
        
        print(f"\n📋 Detailed results saved to: {output_path}")
        
        return results

def main():
    # Focus on 2024 first (highest priority)
    target_year = 2024
    
    discovery = ComprehensiveLinkDiscovery(target_year)
    results = discovery.run_comprehensive_discovery()
    
    print(f"\n🎯 NEXT STEPS:")
    print(f"   1. Review the {results['found_count']} discovered URLs")
    print(f"   2. Cross-reference with existing downloads")
    print(f"   3. Download missing articles using the discovered URLs")
    print(f"   4. If still missing articles, investigate other discovery methods")

if __name__ == "__main__":
    main()
