#!/usr/bin/env python3
"""
Re-download Missing Articles with Fixed Image Links

This script will re-download the 156 missing articles with the fixed image link replacement.
The anchor href tags will now point to local PNG files instead of online URLs.
"""

import json
import sys
import time
import concurrent.futures
import threading
import sqlite3
from pathlib import Path
from typing import List, Dict

# Add current directory to path
sys.path.insert(0, str(Path(__file__).parent))

from crawler import BlogCrawler
import config

def main():
    print("=" * 80)
    print("🔒 THREAD-SAFE RE-DOWNLOAD WITH FIXED IMAGE LINKS")
    print("=" * 80)
    print("🔧 This will re-download articles with FIXED image link replacement")
    print("🔗 Anchor href tags will now point to local PNG files instead of online URLs")
    print("📝 Existing articles will be OVERWRITTEN with corrected versions")
    print("=" * 80)
    
    # Load missing articles
    report_file = Path("output/metadata/correct_comparison_report.json")
    
    if not report_file.exists():
        print(f"❌ Missing articles report not found: {report_file}")
        print("   Please run correct_comparison.py first to generate the report")
        return
    
    print("📋 Loading missing articles from comparison report...")
    with open(report_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    missing_articles = data.get('missing_articles', [])
    print(f"📋 Found {len(missing_articles)} missing articles to re-download")
    
    if not missing_articles:
        print("✅ No missing articles found!")
        return
    
    # Show summary by year
    articles_by_year = {}
    for article in missing_articles:
        year = article.get('year', 'unknown')
        articles_by_year[year] = articles_by_year.get(year, 0) + 1
    
    print(f"\n📊 Missing Articles Summary:")
    for year in sorted(articles_by_year.keys(), reverse=True):
        print(f"   📅 {year}: {articles_by_year[year]} articles")
    
    print(f"\n🚀 Configuration:")
    print(f"   🔢 Max Parallel Streams: 5")
    print(f"   🔒 Thread-Safe: Each stream creates its own database connection")
    print(f"   🔧 Image Link Fix: Anchor hrefs will point to local PNG files")
    print(f"   ⏱️  Estimated Time: ~{len(missing_articles)/(5*2):.1f} minutes")
    
    # Ask for confirmation
    print(f"\n🤔 Ready to re-download {len(missing_articles)} articles with FIXED image links.")
    print("   This will OVERWRITE existing articles to fix the image link issue.")
    
    response = input("\nProceed with re-download? (y/N): ")
    
    if response.lower() != 'y':
        print("📋 Re-download cancelled.")
        return
    
    print(f"\n🚀 Starting re-download with fixed image links...")
    print("⏳ This may take several minutes...")
    
    # Simple sequential download for now (can be made parallel later)
    successful = 0
    failed = 0
    
    for i, article in enumerate(missing_articles, 1):
        print(f"\n📄 [{i:3d}/{len(missing_articles)}] {article['title'][:60]}...")
        
        try:
            # Create crawler
            crawler = BlogCrawler()
            
            # Convert to post format
            post = {
                'url': article['url'],
                'title': article['title'],
                'year': article.get('year', 'unknown'),
                'author': 'Gerry Lewy',
                'date': f"{article.get('year', 'unknown')}-01-01",
                'source': 'redownload_fixed_images'
            }
            
            # Download with fixed image links
            success = crawler.download_post_content(post)
            
            if success:
                print(f"   ✅ Downloaded successfully")
                successful += 1
            else:
                print(f"   ❌ Download failed")
                failed += 1
                
        except Exception as e:
            print(f"   ❌ Error: {e}")
            failed += 1
    
    print(f"\n🎉 Re-download Complete!")
    print(f"   ✅ Successful: {successful}")
    print(f"   ❌ Failed: {failed}")
    print(f"   📊 Success Rate: {successful/(successful+failed)*100:.1f}%")
    
    if successful > 0:
        print(f"\n🔗 Image links have been fixed in {successful} articles!")
        print(f"   Anchor href tags now point to local PNG files")

if __name__ == "__main__":
    main()
