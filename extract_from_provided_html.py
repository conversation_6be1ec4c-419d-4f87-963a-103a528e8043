#!/usr/bin/env python3
"""
Extract Articles from User-Provided HTML

The user provided a sample of the expanded sidebar HTML structure.
This script will parse that exact structure to extract article links.

From the user's HTML, we can see each date section contains:
<ul class="posts">
  <li><a href="article_url">Article Title</a></li>
  <li><a href="article_url">Article Title</a></li>
</ul>

We need to extract ALL these links to get the complete 227 articles.
"""

import json
import re
from pathlib import Path
from typing import List, Dict
from datetime import datetime
from bs4 import BeautifulSoup

import config

def extract_from_user_html_sample() -> List[Dict]:
    """Extract articles from the HTML sample provided by the user"""
    
    # This is the exact HTML structure the user provided
    user_html = '''
<li class="archivedate expanded">
<a class="toggle" href="javascript:void(0)">
<span class="zippy toggle-open">▼&nbsp;</span>
</a>
<a class="post-count-link" href="https://gerrysdiamondsettingessays.blogspot.com/2024/">
2024
</a>
<span class="post-count" dir="ltr">(227)</span>
<ul class="hierarchy">
<li class="archivedate expanded">
<a class="toggle" href="javascript:void(0)">
<span class="zippy toggle-open">▼&nbsp;</span>
</a>
<a class="post-count-link" href="https://gerrysdiamondsettingessays.blogspot.com/2024_12_29_archive.html">
12/29
</a>
<span class="post-count" dir="ltr">(2)</span>
<ul class="posts"><li><a href="https://gerrysdiamondsettingessays.blogspot.com/2025/01/what-is-setters-hold-it-19-photos.html">What is a "Setters, Hold-it"? =&gt;  17 photos.</a></li><li><a href="https://gerrysdiamondsettingessays.blogspot.com/2024/12/examples-in-using-powerful-digital.html">Examples in using a powerful "Digital Microscope"?...</a></li></ul></li>
</ul>
<ul class="hierarchy">
<li class="archivedate expanded">
<a class="toggle" href="javascript:void(0)">
<span class="zippy toggle-open">▼&nbsp;</span>
</a>
<a class="post-count-link" href="https://gerrysdiamondsettingessays.blogspot.com/2024_12_22_archive.html">
12/22
</a>
<span class="post-count" dir="ltr">(4)</span>
<ul class="posts"><li><a href="https://gerrysdiamondsettingessays.blogspot.com/2024/12/handpiece-repair-service-3-photos.html">"Handpiece Repair Service" - 3 photos</a></li><li><a href="https://gerrysdiamondsettingessays.blogspot.com/2024/12/my-cad-created-jewellery-6-photos.html">My CAD created jewellery. =&gt; 6 photos</a></li><li><a href="https://gerrysdiamondsettingessays.blogspot.com/2024/12/how-to-channel-set-diamonds-in-bracelet.html">How to Channel Set diamonds in a bracelet? - 5 photos</a></li><li><a href="https://gerrysdiamondsettingessays.blogspot.com/2024/12/repaired-video-on-wax-carving-to-silver.html">(Repaired video) "Wax Carving to Silver Pendant" t...</a></li></ul></li>
</ul>
<ul class="hierarchy">
<li class="archivedate expanded">
<a class="toggle" href="javascript:void(0)">
<span class="zippy toggle-open">▼&nbsp;</span>
</a>
<a class="post-count-link" href="https://gerrysdiamondsettingessays.blogspot.com/2024_12_15_archive.html">
12/15
</a>
<span class="post-count" dir="ltr">(1)</span>
<ul class="posts"><li><a href="https://gerrysdiamondsettingessays.blogspot.com/2024/12/assembly-of-wax-ring-prior-to-casting.html">Assembly of a wax ring prior to casting - 17 photos.</a></li></ul></li>
</ul>
<ul class="hierarchy">
<li class="archivedate expanded">
<a class="toggle" href="javascript:void(0)">
<span class="zippy toggle-open">▼&nbsp;</span>
</a>
<a class="post-count-link" href="https://gerrysdiamondsettingessays.blogspot.com/2024_12_01_archive.html">
12/01
</a>
<span class="post-count" dir="ltr">(1)</span>
<ul class="posts"><li><a href="https://gerrysdiamondsettingessays.blogspot.com/2024/12/defects-prior-to-setting-stones-in-wax.html">DEFECTS PRIOR TO "SETTING STONES IN WAX" =&gt; 13 photos</a></li></ul></li>
</ul>
<ul class="hierarchy">
<li class="archivedate expanded">
<a class="toggle" href="javascript:void(0)">
<span class="zippy toggle-open">▼&nbsp;</span>
</a>
<a class="post-count-link" href="https://gerrysdiamondsettingessays.blogspot.com/2024_11_24_archive.html">
11/24
</a>
<span class="post-count" dir="ltr">(1)</span>
<ul class="posts"><li><a href="https://gerrysdiamondsettingessays.blogspot.com/2024/11/are-these-gem-quality-stones-or-just.html">Are these 'Diamonds', or just 'white stones'? =&gt; 5...</a></li></ul></li>
</ul>
<ul class="hierarchy">
<li class="archivedate expanded">
<a class="toggle" href="javascript:void(0)">
<span class="zippy toggle-open">▼&nbsp;</span>
</a>
<a class="post-count-link" href="https://gerrysdiamondsettingessays.blogspot.com/2024_11_17_archive.html">
11/17
</a>
<span class="post-count" dir="ltr">(2)</span>
<ul class="posts"><li><a href="https://gerrysdiamondsettingessays.blogspot.com/2024/11/in-depth-study-why-use-adjustable.html">(IN-DEPTH STUDY) Why use an "Adjustable, graver ha...</a></li><li><a href="https://gerrysdiamondsettingessays.blogspot.com/2024/11/how-to-repair-your-defective-onglette.html">How to repair your defective "Onglette graver" =&gt; ...</a></li></ul></li>
</ul>
<ul class="hierarchy">
<li class="archivedate expanded">
<a class="toggle" href="javascript:void(0)">
<span class="zippy toggle-open">▼&nbsp;</span>
</a>
<a class="post-count-link" href="https://gerrysdiamondsettingessays.blogspot.com/2024_11_10_archive.html">
11/10
</a>
<span class="post-count" dir="ltr">(2)</span>
<ul class="posts"><li><a href="https://gerrysdiamondsettingessays.blogspot.com/2024/11/how-to-clean-repair-your-flat-graver-15.html">How to 'clean &amp; repair' your "FLAT graver"? =&gt; 15 ...</a></li><li><a href="https://gerrysdiamondsettingessays.blogspot.com/2024/11/how-do-you-prepare-for-future-metal.html">How do you prepare for future metal cuttings? =&gt;  ...</a></li></ul></li>
</ul>
<ul class="hierarchy">
<li class="archivedate expanded">
<a class="toggle" href="javascript:void(0)">
<span class="zippy toggle-open">▼&nbsp;</span>
</a>
<a class="post-count-link" href="https://gerrysdiamondsettingessays.blogspot.com/2024_11_03_archive.html">
11/03
</a>
<span class="post-count" dir="ltr">(1)</span>
<ul class="posts"><li><a href="https://gerrysdiamondsettingessays.blogspot.com/2024/11/how-to-remove-shellac-off-of-your.html">How to REMOVE shellac off of your jewellery? =&gt; 9 ...</a></li></ul></li>
</ul>
<ul class="hierarchy">
<li class="archivedate expanded">
<a class="toggle" href="javascript:void(0)">
<span class="zippy toggle-open">▼&nbsp;</span>
</a>
<a class="post-count-link" href="https://gerrysdiamondsettingessays.blogspot.com/2024_10_27_archive.html">
10/27
</a>
<span class="post-count" dir="ltr">(5)</span>
<ul class="posts"><li><a href="https://gerrysdiamondsettingessays.blogspot.com/2024/10/what-tools-are-available-in-your-local.html">What tools are available in your local 'HARDWARE S...</a></li><li><a href="https://gerrysdiamondsettingessays.blogspot.com/2024/10/how-to-drill-holes-in-metal-correctly.html">How to drill holes in metal...correctly? =&gt; 10 photos</a></li><li><a href="https://gerrysdiamondsettingessays.blogspot.com/2024/10/indepth-review-of-graver-cutting-38.html">INDEPTH REVIEW of 'GRAVER CUTTING' =&gt; 38 photos</a></li><li><a href="https://gerrysdiamondsettingessays.blogspot.com/2024/10/the-jewellers-bench-book-orchid-in.html">"The Jeweller's Bench Book" =&gt; "Orchid in Print" V...</a></li><li><a href="https://gerrysdiamondsettingessays.blogspot.com/2024/10/review-flush-setting-your-large-square.html">(REVIEW) =&gt; "Flush Setting"  your large Square sto...</a></li></ul></li>
</ul>
<ul class="hierarchy">
<li class="archivedate expanded">
<a class="toggle" href="javascript:void(0)">
<span class="zippy toggle-open">▼&nbsp;</span>
</a>
<a class="post-count-link" href="https://gerrysdiamondsettingessays.blogspot.com/2024_10_20_archive.html">
10/20
</a>
<span class="post-count" dir="ltr">(2)</span>
<ul class="posts"><li><a href="https://gerrysdiamondsettingessays.blogspot.com/2024/10/how-do-you-shape-your-new-onglette.html">How do you shape your NEW ONGLETTE graver? =&gt; 31 p...</a></li><li><a href="https://gerrysdiamondsettingessays.blogspot.com/2024/10/do-you-use-pliers-in-gem-stone-setting.html">Do YOU use PLIERS in gem-stone setting? =&gt; 48 photos</a></li></ul></li>
</ul>
<ul class="hierarchy">
<li class="archivedate expanded">
<a class="toggle" href="javascript:void(0)">
<span class="zippy toggle-open">▼&nbsp;</span>
</a>
<a class="post-count-link" href="https://gerrysdiamondsettingessays.blogspot.com/2024_10_13_archive.html">
10/13
</a>
<span class="post-count" dir="ltr">(1)</span>
<ul class="posts"><li><a href="https://gerrysdiamondsettingessays.blogspot.com/2024/10/how-to-prepare-to-set-round-stone-in.html">How to 'prepare' to set a ROUND stone in a FLUSH-G...</a></li></ul></li>
</ul>
<ul class="hierarchy">
<li class="archivedate expanded">
<a class="toggle" href="javascript:void(0)">
<span class="zippy toggle-open">▼&nbsp;</span>
</a>
<a class="post-count-link" href="https://gerrysdiamondsettingessays.blogspot.com/2024_10_06_archive.html">
10/06
</a>
<span class="post-count" dir="ltr">(3)</span>
<ul class="posts"><li><a href="https://gerrysdiamondsettingessays.blogspot.com/2024/10/how-to-maintain-graver-with-oil-stone.html">How to maintain the graver with an Oil Stone? =&gt; 1...</a></li><li><a href="https://gerrysdiamondsettingessays.blogspot.com/2024/10/would-you-use-hss-or-156c-bur-when.html">Would you use a "HSS", or a "156C" bur? When &amp; whe...</a></li><li><a href="https://gerrysdiamondsettingessays.blogspot.com/2024/10/review-with-newer-photos-cut-down.html">ANOTHER REVIEW (but with newer photos) "Cut-Down, ...</a></li></ul></li>
</ul>
<ul class="hierarchy">
<li class="archivedate expanded">
<a class="toggle" href="javascript:void(0)">
<span class="zippy toggle-open">▼&nbsp;</span>
</a>
<a class="post-count-link" href="https://gerrysdiamondsettingessays.blogspot.com/2024_09_29_archive.html">
09/29
</a>
<span class="post-count" dir="ltr">(2)</span>
<ul class="posts"><li><a href="https://gerrysdiamondsettingessays.blogspot.com/2024/10/ajm-essay-of-gerry-lewys-diamond.html">"AJM-magazine essay of Gerry Lewy's =&gt; Diamond Set...</a></li><li><a href="https://gerrysdiamondsettingessays.blogspot.com/2024/09/design-flaws-from-cad-rendering.html">Design "flaws" seen on a CAD rendering.</a></li></ul></li>
</ul>
<ul class="hierarchy">
<li class="archivedate expanded">
<a class="toggle" href="javascript:void(0)">
<span class="zippy toggle-open">▼&nbsp;</span>
</a>
<a class="post-count-link" href="https://gerrysdiamondsettingessays.blogspot.com/2024_09_22_archive.html">
09/22
</a>
<span class="post-count" dir="ltr">(30)</span>
<ul class="posts"><li><a href="https://gerrysdiamondsettingessays.blogspot.com/2024/09/creating-azure-design-video-link.html">Creating an "AZURE" design =&gt; video link</a></li><li><a href="https://gerrysdiamondsettingessays.blogspot.com/2024/09/third-video-on-creating-beads-in-series.html">Third video on "Creating Beads" =&gt; in a series of ...</a></li><li><a href="https://gerrysdiamondsettingessays.blogspot.com/2024/09/second-film-in-creating-beads-2nd-video.html">Second film in "Creating Beads" 2nd video =&gt; in a ...</a></li><li><a href="https://gerrysdiamondsettingessays.blogspot.com/2024/09/one-new-video-on-creating-beads-1-of-4.html">First video on "Creating Beads. =&gt; 1 of 3 in a ser...</a></li><li><a href="https://gerrysdiamondsettingessays.blogspot.com/2024/09/detailed-review-on-gypsy-flush-setting.html">VERY DETAILED - REVIEW on =&gt; "Gypsy" - Flush Setti...</a></li><li><a href="https://gerrysdiamondsettingessays.blogspot.com/2024/09/drilling-holes-in-wax-video-link.html">Drilling Holes in Wax =&gt; VIDEO link</a></li><li><a href="https://gerrysdiamondsettingessays.blogspot.com/2024/09/moving-hole-video-link.html">Moving a Hole =&gt; Video link</a></li><li><a href="https://gerrysdiamondsettingessays.blogspot.com/2024/09/bright-cutting-review-video-link.html">Bright Cutting (REVIEW) =&gt; VIDEO link</a></li><li><a href="https://gerrysdiamondsettingessays.blogspot.com/2024/09/bead-burnishing-video-link.html">Bead Burnishing =&gt; VIDEO link</a></li><li><a href="https://gerrysdiamondsettingessays.blogspot.com/2024/09/video-link-tool-modification-on-video-a.html">Tool Modification on Video "A" =&gt; Video link</a></li><li><a href="https://gerrysdiamondsettingessays.blogspot.com/2024/09/video-link-bezel-setting-on-video-c.html">Bezel Setting on 'Video link C'</a></li><li><a href="https://gerrysdiamondsettingessays.blogspot.com/2024/09/video-link-cluster-rings-with-mini.html">Cluster rings with Mini-Claws. "Video D"</a></li><li><a href="https://gerrysdiamondsettingessays.blogspot.com/2024/09/video-link-for-bead-setting-video-f-in.html">Bead Setting. Video F in a series of 5 films</a></li><li><a href="https://gerrysdiamondsettingessays.blogspot.com/2024/09/video-link-princess-stone-in-round.html">Princess stone in Round Claws "Video E"</a></li><li><a href="https://gerrysdiamondsettingessays.blogspot.com/2024/09/video-link-graver-cutting-silver-surface.html">Graver Cutting a silver surface =&gt; VIDEO link</a></li><li><a href="https://gerrysdiamondsettingessays.blogspot.com/2024/09/video-link-introduction-to-cut-down_24.html">Introduction to 'Cut Down' Metal Cutting =&gt; VIDEO ...</a></li><li><a href="https://gerrysdiamondsettingessays.blogspot.com/2024/09/video-link-introduction-to-cut-down.html">Close-up of "Bright-Cutting" on a metal surface. V...</a></li><li><a href="https://gerrysdiamondsettingessays.blogspot.com/2024/09/video-link-engraving-tools-for-all.html">Engraving tools for all reasons =&gt; VIDEO link</a></li><li><a href="https://gerrysdiamondsettingessays.blogspot.com/2024/09/video-link-burs-gravers.html">Burs &amp; gravers =&gt; VIDEO link</a></li><li><a href="https://gerrysdiamondsettingessays.blogspot.com/2024/09/video-link-bezel-setting-using-your-hss.html">Bezel Setting using your HSS bur =&gt; VIDEO link</a></li><li><a href="https://gerrysdiamondsettingessays.blogspot.com/2024/09/video-link-for-4-claw-stone-setting-2.html">4-claw, Stone Setting' #2 version =&gt; Video "B" link</a></li><li><a href="https://gerrysdiamondsettingessays.blogspot.com/2024/09/bead-videos.html">4 videos on 'Pave' =&gt; Bead Setting VIDEO link</a></li><li><a href="https://gerrysdiamondsettingessays.blogspot.com/2024/09/many-details-for-bezel-setting-52-photos.html">MANY DETAILS for "Bezel Setting" - 52 photos</a></li><li><a href="https://gerrysdiamondsettingessays.blogspot.com/2024/09/video-bright-cutting-with-right-sided.html">Bright Cutting with a 'Right-Sided' Onglet graver ...</a></li><li><a href="https://gerrysdiamondsettingessays.blogspot.com/2024/09/video-bright-cutting-inside-of-bezel.html">Bright-Cutting Inside of a Bezel Setting =&gt; VIDEO ...</a></li><li><a href="https://gerrysdiamondsettingessays.blogspot.com/2024/09/video-designing-with-cad.html">DESIGNING WITH CAD =&gt; VIDEO link</a></li><li><a href="https://gerrysdiamondsettingessays.blogspot.com/2024/09/drilling-holes.html">Preparing to drill holes =&gt; VIDEO link</a></li><li><a href="https://gerrysdiamondsettingessays.blogspot.com/2024/09/links-to-videos-that-are-seen-on-youtube.html">WAX-CARVING TO A SILVER PENDANT =&gt; Video link = 53...</a></li><li><a href="https://gerrysdiamondsettingessays.blogspot.com/2024/09/multiple-baguette-bezels-in-one-ring-3.html">Multiple 'Baguette Bezels' in one ring - 3 photos</a></li><li><a href="https://gerrysdiamondsettingessays.blogspot.com/2024/09/cad-results-for-small-gemstones-20.html">"CAD". Results for small gemstones - 20 photos</a></li></ul></li>
</ul>
    '''
    
    print("🎯 Parsing user-provided HTML sample...")
    
    soup = BeautifulSoup(user_html, 'html.parser')
    
    # Find all <ul class="posts"> sections
    posts_sections = soup.select('ul.posts')
    
    articles = []
    
    for posts_section in posts_sections:
        # Find the date for this posts section
        date_link = posts_section.find_previous('a', class_='post-count-link')
        date_text = "unknown"
        if date_link:
            date_text = date_link.get_text(strip=True)
            if date_text == "2024":  # Skip the main year link
                continue
        
        # Find the post count
        count_span = posts_section.find_previous('span', class_='post-count')
        expected_count = 0
        if count_span:
            count_match = re.search(r'\((\d+)\)', count_span.get_text())
            if count_match:
                expected_count = int(count_match.group(1))
        
        # Extract all article links
        article_links = posts_section.select('li a[href]')
        
        print(f"   📅 {date_text}: {len(article_links)}/{expected_count} articles")
        
        for link in article_links:
            href = link.get('href')
            title = link.get_text(strip=True)
            
            # Clean up HTML entities
            title = title.replace('&gt;', '>').replace('&lt;', '<').replace('&amp;', '&')
            
            article = {
                'url': href,
                'title': title,
                'archive_date': date_text,
                'expected_count': expected_count,
                'source': 'user_provided_html'
            }
            articles.append(article)
    
    print(f"\n✅ Total articles extracted from sample: {len(articles)}")
    
    return articles

def main():
    print("=" * 80)
    print("🎯 EXTRACT FROM USER-PROVIDED HTML")
    print("=" * 80)
    
    # Extract from the user's HTML sample
    articles = extract_from_user_html_sample()
    
    # Count articles by date
    date_counts = {}
    for article in articles:
        date = article['archive_date']
        if date not in date_counts:
            date_counts[date] = 0
        date_counts[date] += 1
    
    print("\n📊 ARTICLES BY DATE:")
    total_found = 0
    total_expected = 0
    
    for date, count in sorted(date_counts.items()):
        # Find expected count for this date
        expected = 0
        for article in articles:
            if article['archive_date'] == date:
                expected = article['expected_count']
                break
        
        status = "✅" if count == expected else "⚠️"
        print(f"   {status} {date}: {count}/{expected} articles")
        total_found += count
        total_expected += expected
    
    print(f"\n📊 SAMPLE TOTALS:")
    print(f"   📊 Found: {total_found} articles")
    print(f"   🎯 Expected: {total_expected} articles")
    print(f"   📈 Coverage: {total_found/total_expected*100:.1f}%")
    
    # Save results
    results = {
        'extraction_date': datetime.now().isoformat(),
        'sample_source': 'user_provided_html',
        'total_found': total_found,
        'total_expected': total_expected,
        'coverage_percentage': round(total_found/total_expected*100, 1),
        'date_counts': date_counts,
        'all_articles': articles
    }
    
    output_path = Path(config.OUTPUT_DIR) / "metadata" / "user_html_extraction.json"
    output_path.parent.mkdir(parents=True, exist_ok=True)
    
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    print(f"\n📋 Results saved to: {output_path}")
    
    print(f"\n🎯 ANALYSIS:")
    print(f"   ✅ Successfully parsed user's HTML structure")
    print(f"   📊 This sample contains {total_found} articles")
    print(f"   🔍 To get all 227 articles, we need the complete expanded HTML")
    print(f"   📝 The parsing logic is working correctly!")

if __name__ == "__main__":
    main()
