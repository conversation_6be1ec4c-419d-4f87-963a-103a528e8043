#!/usr/bin/env python3
"""
<PERSON>'s Blog Validation and Gap Filling Script

This script:
1. Audits the current downloaded archive for duplicates and validates counts
2. <PERSON>ans the live blog to create a master list of all articles
3. Cross-references to find missing articles
4. Downloads missing articles using the same structure as the main crawler
"""

import requests
import json
import re
import time
from pathlib import Path
from urllib.parse import urljoin, urlparse
from datetime import datetime
from typing import List, Dict, Set, Optional, Tuple
from collections import defaultdict

from bs4 import BeautifulSoup
import config
from crawler import BlogCrawler

class BlogValidator:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        self.downloaded_articles = {}  # URL -> file_path mapping
        self.live_articles = {}        # URL -> article_info mapping
        self.missing_articles = {}     # URL -> article_info mapping
        
    def audit_downloaded_archive(self) -> Dict:
        """Audit the current downloaded archive"""
        print("🔍 Auditing downloaded archive...")
        
        audit_results = {
            'total_files': 0,
            'files_by_year': defaultdict(int),
            'duplicates': [],
            'invalid_files': []
        }
        
        # Scan all HTML files in archive
        archive_dir = Path(config.OUTPUT_DIR) / "archive"
        if not archive_dir.exists():
            print("❌ Archive directory not found!")
            return audit_results
            
        # Track articles by URL to detect duplicates
        url_to_files = defaultdict(list)
        
        for year_dir in archive_dir.iterdir():
            if year_dir.is_dir() and year_dir.name.isdigit():
                year = year_dir.name
                year_files = list(year_dir.glob("*.html"))
                audit_results['files_by_year'][year] = len(year_files)
                audit_results['total_files'] += len(year_files)
                
                # Extract original URLs from each file
                for html_file in year_files:
                    try:
                        with open(html_file, 'r', encoding='utf-8') as f:
                            content = f.read()
                            
                        # Extract original URL from the "View Original" link
                        soup = BeautifulSoup(content, 'html.parser')
                        original_link = soup.find('a', string='View Original')
                        if original_link and original_link.get('href'):
                            original_url = original_link['href']
                            url_to_files[original_url].append(html_file)
                            self.downloaded_articles[original_url] = html_file
                        else:
                            audit_results['invalid_files'].append(str(html_file))
                            
                    except Exception as e:
                        print(f"⚠️  Error reading {html_file}: {e}")
                        audit_results['invalid_files'].append(str(html_file))
        
        # Find duplicates
        for url, files in url_to_files.items():
            if len(files) > 1:
                audit_results['duplicates'].append({
                    'url': url,
                    'files': [str(f) for f in files]
                })
        
        return audit_results
    
    def scan_live_blog(self) -> Dict:
        """Scan the live blog to create master list of all articles"""
        print("🌐 Scanning live blog for all articles...")
        
        # Get all archive URLs (same logic as batch crawler)
        response = self.session.get(config.BASE_URL)
        if not response:
            print("❌ Failed to fetch blog homepage")
            return {}
            
        soup = BeautifulSoup(response.content, 'html.parser')
        archive_links = soup.select('a[href*="_archive.html"]')
        archive_urls = [urljoin(config.BASE_URL, link['href']) for link in archive_links]
        
        print(f"📚 Found {len(archive_urls)} archive pages to scan")
        
        article_count = 0
        for i, archive_url in enumerate(archive_urls, 1):
            print(f"📖 Scanning archive {i}/{len(archive_urls)}: {archive_url}")
            
            try:
                response = self.session.get(archive_url)
                if not response:
                    continue
                    
                soup = BeautifulSoup(response.content, 'html.parser')
                post_containers = soup.select(config.SELECTORS["post_container"])
                
                for container in post_containers:
                    try:
                        # Extract post info
                        title_elem = container.select_one(config.SELECTORS["post_title"])
                        if not title_elem:
                            continue
                            
                        post_url = title_elem.get('href')
                        title = title_elem.get_text(strip=True)
                        
                        # Extract date
                        date_elem = container.select_one(config.SELECTORS["post_date"])
                        date_str = date_elem.get('title') if date_elem else ""
                        
                        # Extract snippet from post content
                        content_elem = container.select_one('.post-body')
                        snippet = ""
                        if content_elem:
                            snippet = content_elem.get_text(strip=True)[:200] + "..."
                        
                        # Extract year from URL
                        year_match = re.search(r'/(\d{4})/', post_url)
                        year = int(year_match.group(1)) if year_match else None
                        
                        self.live_articles[post_url] = {
                            'title': title,
                            'url': post_url,
                            'date': date_str,
                            'snippet': snippet,
                            'year': year,
                            'archive_source': archive_url
                        }
                        article_count += 1
                        
                    except Exception as e:
                        print(f"⚠️  Error extracting post from container: {e}")
                        continue
                        
            except Exception as e:
                print(f"⚠️  Error processing archive {archive_url}: {e}")
                continue
                
            # Respectful crawling
            time.sleep(config.REQUEST_DELAY)
        
        print(f"✅ Found {article_count} total articles on live blog")
        return self.live_articles
    
    def find_missing_articles(self) -> Dict:
        """Cross-reference to find missing articles"""
        print("🔍 Cross-referencing to find missing articles...")
        
        downloaded_urls = set(self.downloaded_articles.keys())
        live_urls = set(self.live_articles.keys())
        
        missing_urls = live_urls - downloaded_urls
        
        for url in missing_urls:
            self.missing_articles[url] = self.live_articles[url]
        
        print(f"📊 Analysis Results:")
        print(f"   📄 Downloaded: {len(downloaded_urls)} articles")
        print(f"   🌐 Live Blog: {len(live_urls)} articles")
        print(f"   ❌ Missing: {len(missing_urls)} articles")
        
        # Group missing by year
        missing_by_year = defaultdict(list)
        for url, info in self.missing_articles.items():
            year = info.get('year', 'Unknown')
            missing_by_year[year].append(info)
        
        print(f"\n📅 Missing articles by year:")
        for year in sorted(missing_by_year.keys()):
            print(f"   {year}: {len(missing_by_year[year])} articles")
        
        return self.missing_articles
    
    def download_missing_articles(self):
        """Download missing articles using the same logic as main crawler"""
        if not self.missing_articles:
            print("✅ No missing articles to download!")
            return
            
        print(f"⬇️  Starting download of {len(self.missing_articles)} missing articles...")
        
        # Initialize crawler
        crawler = BlogCrawler()
        
        # Group missing articles by year for organized processing
        missing_by_year = defaultdict(list)
        for url, info in self.missing_articles.items():
            year = info.get('year', 'Unknown')
            missing_by_year[year].append(info)
        
        # Process by year (latest first)
        for year in sorted(missing_by_year.keys(), reverse=True):
            articles = missing_by_year[year]
            print(f"\n🗓️  Processing {len(articles)} missing articles from {year}")
            
            for i, article_info in enumerate(articles, 1):
                print(f"   📄 Downloading {i}/{len(articles)}: {article_info['title'][:50]}...")
                
                # Create post dict in expected format
                post = {
                    'url': article_info['url'],
                    'title': article_info['title'],
                    'date': article_info['date'],
                    'author': 'Gerry Lewy'
                }
                
                # Download using crawler
                success = crawler.download_post_content(post)
                if success:
                    print(f"   ✅ Successfully downloaded: {article_info['title'][:50]}")
                else:
                    print(f"   ❌ Failed to download: {article_info['title'][:50]}")
                
                # Update index after each download
                crawler.generate_index()
                
                # Respectful crawling
                time.sleep(config.REQUEST_DELAY)
        
        print(f"\n✅ Completed downloading missing articles!")
        print(f"📊 Final stats: {crawler.posts_downloaded} posts, {crawler.images_downloaded} images")
    
    def save_analysis_report(self, audit_results: Dict):
        """Save detailed analysis report"""
        report = {
            'analysis_date': datetime.now().isoformat(),
            'audit_results': audit_results,
            'live_blog_stats': {
                'total_articles': len(self.live_articles),
                'articles_by_year': {}
            },
            'missing_articles': {
                'count': len(self.missing_articles),
                'articles': list(self.missing_articles.values())
            }
        }
        
        # Group live articles by year
        live_by_year = defaultdict(int)
        for info in self.live_articles.values():
            year = info.get('year', 'Unknown')
            live_by_year[year] += 1
        report['live_blog_stats']['articles_by_year'] = dict(live_by_year)
        
        # Save report
        report_path = Path(config.OUTPUT_DIR) / "metadata" / "validation_report.json"
        report_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        print(f"📋 Analysis report saved to: {report_path}")

def main():
    print("=" * 80)
    print("🔍 GERRY'S BLOG VALIDATION AND GAP FILLING")
    print("=" * 80)
    
    validator = BlogValidator()
    
    # Step 1: Audit downloaded archive
    audit_results = validator.audit_downloaded_archive()
    
    print(f"\n📊 Archive Audit Results:")
    print(f"   📄 Total files: {audit_results['total_files']}")
    print(f"   📅 Files by year:")
    for year in sorted(audit_results['files_by_year'].keys()):
        print(f"      {year}: {audit_results['files_by_year'][year]} files")
    
    if audit_results['duplicates']:
        print(f"   ⚠️  Found {len(audit_results['duplicates'])} duplicate articles")
        for dup in audit_results['duplicates']:
            print(f"      URL: {dup['url']}")
            print(f"      Files: {dup['files']}")
    
    if audit_results['invalid_files']:
        print(f"   ❌ Found {len(audit_results['invalid_files'])} invalid files")
    
    # Step 2: Scan live blog
    validator.scan_live_blog()
    
    # Step 3: Find missing articles
    validator.find_missing_articles()
    
    # Step 4: Save analysis report
    validator.save_analysis_report(audit_results)
    
    # Step 5: Ask user if they want to download missing articles
    if validator.missing_articles:
        response = input(f"\n🤔 Found {len(validator.missing_articles)} missing articles. Download them? (y/N): ")
        if response.lower() == 'y':
            validator.download_missing_articles()
        else:
            print("📋 Analysis complete. Missing articles list saved to validation_report.json")
    else:
        print("✅ No missing articles found! Archive is complete.")

if __name__ == "__main__":
    main()
