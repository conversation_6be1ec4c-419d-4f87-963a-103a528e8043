#!/usr/bin/env python3
"""
Check Current State of Downloads

Check what was downloaded during the interrupted run.
"""

import sqlite3
import json
from pathlib import Path

def check_current_state():
    print("=" * 60)
    print("📊 CURRENT DOWNLOAD STATE CHECK")
    print("=" * 60)
    
    # Check database
    try:
        conn = sqlite3.connect('output/posts.db')
        cursor = conn.cursor()
        cursor.execute('SELECT COUNT(*) FROM posts')
        total_posts = cursor.fetchone()[0]
        print(f'📊 Database: {total_posts} posts')
        
        cursor.execute('SELECT COUNT(*) FROM posts WHERE date LIKE "2025%"')
        posts_2025 = cursor.fetchone()[0]
        print(f'📅 2025 posts in DB: {posts_2025}')
        
        conn.close()
    except Exception as e:
        print(f'❌ Database error: {e}')
    
    # Check files
    archive_dir = Path('output/archive')
    if archive_dir.exists():
        total_files = len(list(archive_dir.rglob('*.html')))
        print(f'📁 Archive files: {total_files}')
        
        year_2025_dir = archive_dir / '2025'
        if year_2025_dir.exists():
            files_2025 = len(list(year_2025_dir.rglob('*.html')))
            print(f'📅 2025 files: {files_2025}')
            
            # List some 2025 files
            files_list = list(year_2025_dir.rglob('*.html'))[:5]
            if files_list:
                print(f"📋 Sample 2025 files:")
                for f in files_list:
                    print(f"   - {f.name}")
        else:
            print('❌ 2025 directory not found')
    else:
        print('❌ Archive directory not found')
    
    # Check comparison report
    report_file = Path('output/metadata/correct_comparison_report.json')
    if report_file.exists():
        with open(report_file, 'r') as f:
            data = json.load(f)
        missing_2025 = len([a for a in data.get('missing_articles', []) if a.get('year') == '2025'])
        print(f'📋 Missing 2025 articles (original): {missing_2025}')
    else:
        print('❌ Comparison report not found')
    
    # Check images directory
    images_dir = Path('output/images')
    if images_dir.exists():
        total_images = len(list(images_dir.rglob('*.png')))
        print(f'🖼️  Total images: {total_images}')
        
        # Check recent images (from interrupted download)
        recent_images = sorted(images_dir.rglob('*.png'), key=lambda x: x.stat().st_mtime, reverse=True)[:10]
        if recent_images:
            print(f"🖼️  Recent images (last 10):")
            for img in recent_images:
                print(f"   - {img.name}")
    else:
        print('❌ Images directory not found')

if __name__ == "__main__":
    check_current_state()
