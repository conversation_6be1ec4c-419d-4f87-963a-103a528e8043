"""
Configuration settings for Gerry's Diamond Setting Essays Blog Crawler
"""

import os
from pathlib import Path

# Base configuration
BASE_URL = "https://gerrysdiamondsettingessays.blogspot.com"
OUTPUT_DIR = Path("output")
ARCHIVE_DIR = OUTPUT_DIR / "archive"
IMAGES_DIR = OUTPUT_DIR / "images"
METADATA_DIR = OUTPUT_DIR / "metadata"
STATIC_DIR = OUTPUT_DIR / "static"

# Year-based organization
ORGANIZE_BY_YEAR = True

# Crawler settings
REQUEST_DELAY = 2.0  # Seconds between requests (respectful crawling)
MAX_RETRIES = 3
TIMEOUT = 30
CONCURRENT_DOWNLOADS = 3

# Image processing settings
CONVERT_TO_PNG = True
OPTIMIZE_IMAGES = True
MAX_IMAGE_SIZE = (1920, 1080)  # Max width, height for optimization
JPEG_QUALITY = 85

# User agent for requests
USER_AGENT = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"

# Archive URL patterns
ARCHIVE_PATTERNS = {
    "yearly": "{base_url}/{year}/",
    "monthly": "{base_url}/{year}_{month:02d}_archive.html",
    "weekly": "{base_url}/{year}_{month:02d}_{day:02d}_archive.html"
}

# Content selectors (CSS selectors for BeautifulSoup)
SELECTORS = {
    "post_container": ".post",
    "post_title": ".post-title a",
    "post_content": ".post-body",
    "post_date": ".published",
    "post_author": ".post-author",
    "images": "img",
    "archive_links": ".archive-count",
    "pagination_next": ".blog-pager-older-link",
    "pagination_prev": ".blog-pager-newer-link"
}

# File naming patterns
FILENAME_PATTERNS = {
    "post": "{year}_{month:02d}_{day:02d}_{title_slug}.html",
    "image": "img_{index:03d}.png",
    "metadata": "posts_index.json",
    "log": "download_log.db"
}

# Logging configuration
LOG_LEVEL = "INFO"
LOG_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
LOG_FILE = OUTPUT_DIR / "crawler.log"

# Database schema for tracking progress
DB_SCHEMA = """
CREATE TABLE IF NOT EXISTS posts (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    url TEXT UNIQUE NOT NULL,
    title TEXT,
    date TEXT,
    author TEXT,
    content_path TEXT,
    images_count INTEGER DEFAULT 0,
    status TEXT DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS images (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    post_id INTEGER,
    original_url TEXT NOT NULL,
    local_path TEXT,
    filename TEXT,
    size_bytes INTEGER,
    status TEXT DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (post_id) REFERENCES posts (id)
);

CREATE TABLE IF NOT EXISTS crawl_sessions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    start_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    end_time TIMESTAMP,
    posts_discovered INTEGER DEFAULT 0,
    posts_downloaded INTEGER DEFAULT 0,
    images_downloaded INTEGER DEFAULT 0,
    status TEXT DEFAULT 'running'
);
"""

# Test mode settings (for initial testing)
TEST_MODE = False  # Set to True for testing, False for full crawl
TEST_MAX_POSTS = 5  # Limit posts for testing
TEST_YEARS = [2024, 2025]  # Only test recent years
