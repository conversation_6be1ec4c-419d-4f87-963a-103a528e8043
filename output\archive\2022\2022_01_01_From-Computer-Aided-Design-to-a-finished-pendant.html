<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>From "Computer Aided Design" to a finished pendant...</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #fafafa;
            padding: 20px;
        }

        .container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .post-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px;
            text-align: center;
        }

        .post-title {
            font-size: 2.2em;
            font-weight: 700;
            margin-bottom: 15px;
            line-height: 1.2;
        }

        .post-meta {
            font-size: 0.95em;
            opacity: 0.9;
            display: flex;
            justify-content: center;
            gap: 20px;
            flex-wrap: wrap;
        }

        .post-meta a {
            color: #fff;
            text-decoration: underline;
        }

        .post-content {
            padding: 40px;
            font-size: 1.1em;
            line-height: 1.8;
        }

        /* Font normalization - override all inline styles */
        .post-content * {
            font-family: inherit !important;
            font-size: inherit !important;
            line-height: inherit !important;
        }

        .post-content p {
            margin-bottom: 1.2em;
            font-size: 1.1em !important;
        }

        .post-content div {
            font-size: 1.1em !important;
        }

        .post-content span {
            font-size: inherit !important;
        }

        .post-content img {
            max-width: 100%;
            height: auto;
            border-radius: 8px;
            margin: 20px 0;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
        }

        .post-content h1, .post-content h2, .post-content h3 {
            margin: 1.5em 0 0.8em 0;
            color: #2c3e50;
            font-size: 1.4em !important;
            font-weight: 600 !important;
        }

        .post-content strong, .post-content b {
            color: #2c3e50;
            font-weight: 600 !important;
        }

        .post-content em, .post-content i {
            color: #555;
            font-style: italic !important;
        }

        .separator {
            margin: 2em 0;
        }

        .back-link {
            position: fixed;
            top: 20px;
            left: 20px;
            background: #667eea;
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: 500;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;
        }

        .back-link:hover {
            background: #5a6fd8;
            transform: translateY(-2px);
        }

        @media (max-width: 768px) {
            .container {
                margin: 10px;
                border-radius: 8px;
            }

            .post-header {
                padding: 30px 20px;
            }

            .post-title {
                font-size: 1.8em;
            }

            .post-content {
                padding: 30px 20px;
            }

            .post-meta {
                flex-direction: column;
                gap: 10px;
            }
        }
    </style>
</head>
<body>
    <a href="../../../index.html" class="back-link">← Back to Index</a>

    <div class="container">
        <div class="post-header">
            <h1 class="post-title">From "Computer Aided Design" to a finished pendant...</h1>
            <div class="post-meta">
                <span>By: Gerry Lewy</span>
                <span>Date: 2022-01-01</span>
                <span><a href="https://gerrysdiamondsettingessays.blogspot.com/2022/11/from-computer-aided-design-to-finished.html" target="_blank">View Original</a></span>
            </div>
        </div>
        <div class="post-content">
            <div class="post-body entry-content" id="post-body-6744007536030182254" itemprop="description articleBody">
<p></p><div class="separator" style="clear: both; text-align: center;"><span style="text-align: left;"> This essay all started with a computer rendering and it finished with an actual pendant. All though it covers only 6 photos, the process captured all of the exacting details. So lets now start the explanation!</span></div><p></p><div class="separator" style="clear: both; text-align: justify;"><b><i><span style="font-size: medium;">========================================= </span></i></b></div><div class="separator" style="clear: both; text-align: justify;"> When the computer designer decides what pattern he or she needs, they must align the "little pins &amp; bezels" for the stones, this is the most delicate area to work with. The exacting placement of these pins must be accurately aligned to the gemstones. <b><i>All of the stones must be of the same width and depth.</i></b></div><div class="separator" style="clear: both; text-align: center;"><a href="https://blogger.googleusercontent.com/img/b/R29vZ2xl/AVvXsEikQN9BokyLumyvK5WbJvC0Aibh4RrD21607Mru7qGgnSW4DjzvodGKYu5B7MjLhP9cOICkFuiGfeU2c7MFfz7ie2VIMuHVTrG1-cIEH92D5_x1g2JJTCXSEXf4KoH1bMtOIqQqwh47McdIKhMGQCSIJAWjnXA1V8Lk06sYtJ92RiLZi6yyXjz_rzlK/s800/PrespectiveKiteDiamond1.jpg" style="margin-left: 1em; margin-right: 1em;"><img border="0" data-original-height="501" data-original-width="800" height="400" loading="lazy" src="../../images/unknown/From-Computer-Aided-Design-to-a-finished-pendant.._img_001.png" width="640"/></a></div><br/><div class="separator" style="clear: both; text-align: justify;"> There must not be any guesswork in how or where the pins should be placed. <b><i>As what you have here will be where your stones will be arranged. The designer MUST have a a minimum of 'Intermediate' level of experience in gemstone setting.</i></b> ("Basic Level" of setting is not sufficient in this area of designing.)</div><div class="separator" style="clear: both; text-align: justify;">  I'm now speaking from experience, a designer using C.A.D. finished the pattern and she handed me her pendant. None of the stones were usable as the little pins were not addressed to accommodate any of the Marquise or Round Stones. The owner &amp; manager was furious that his time, gold and money was lost and I had to try and salvage those many errors.</div><div class="separator" style="clear: both; text-align: justify;"> Please, <b><i>Learn Diamond Setting from my blog...FIRST!!!!</i></b></div><div class="separator" style="clear: both; text-align: center;"><a href="https://blogger.googleusercontent.com/img/b/R29vZ2xl/AVvXsEig3S1LoF57H1wN_d6B63CSRQODqMUlN09gOqJBwCKHR0kxjbL5oxbTS8Gign7YaELcVEzFeU3xXmI4yV0IPXTbQNoTJOMHZpiyDnoHgoIm8i3cmGXfJ6eBxDIbssFoBlRz8UjPh2S15B2NMCRtgQfhpNsJyQMzSItIWovQ3xDCYRSmcr6XoqnhvaRi/s651/SkiPendant1.jpg" style="margin-left: 1em; margin-right: 1em;"><img border="0" data-original-height="651" data-original-width="438" loading="lazy" src="../../images/unknown/From-Computer-Aided-Design-to-a-finished-pendant.._img_002.png"/></a></div><div><br/></div> From the photo-rendering you can see the finished gold pattern. I had to first set the many round stones, then Bright-Cut the border. <i><b>I used an Onglette #2 on all of the borders, </b>when the main fancy-cut stones were set,<b> I used my Flat-Graver #40 to trim &amp; shape the many bezels.</b></i><br/><div class="separator" style="clear: both; text-align: center;"><a href="https://blogger.googleusercontent.com/img/b/R29vZ2xl/AVvXsEjJWltIXYqaV1tO6Z5Nbc6krkvUQwVKj2Ltpt2pjPOwIuOkvhCw7nlbIFQBhmsP4EaU27QFuMa_LtCSlSznXLPs7JozpZdKmeEasr6PiTKzOIMZFPqByIWsoYr5Dbl3FSId8V1Cbpm377N24hGhMcTaIWKVUF3B2d9Gmto5GuiRsgVQgM8zXhMtZU7Q/s800/DSC00130.JPG" style="margin-left: 1em; margin-right: 1em;"><img border="0" data-original-height="600" data-original-width="800" height="480" loading="lazy" src="../../images/unknown/From-Computer-Aided-Design-to-a-finished-pendant.._img_003.png" width="640"/></a></div><div><br/></div> Around the "Fancy-Cut" shaped stones I decided to trim the extra metal surrounding the thick bezels. Where there was a pointed bezel, I made sure the bezels were finely cut to accommodate the different shapes.<div>  All around the Round stone Bezels, I "Bright-Cut" (with much care) as to keep the Bezel in a Round configuration. Where there was a Princess stone, (Square-stone). I kept the sharp corners of the Bezels finely trimmed. <br/><div class="separator" style="clear: both; text-align: center;"><a href="https://blogger.googleusercontent.com/img/b/R29vZ2xl/AVvXsEiynXy33PiTqI3HHlFWVqvIkEdN4TxwqFnZLIv0DG3bt_8WNjhRFDp7IFXEsiCAaENk89doo71MGKaeVSXmdi4n7A9O1awBzLhFDQZt2zN202Sy9AnAJCRs6UiNWEDGtVu1kigG8X4vGsZ44h0_F4Cl9dg-SBQT-bmlrTUvabrt-H5LK3u2sKjZ3FIj/s800/DSC00131.JPG" style="margin-left: 1em; margin-right: 1em;"><img border="0" data-original-height="600" data-original-width="800" height="480" loading="lazy" src="../../images/unknown/From-Computer-Aided-Design-to-a-finished-pendant.._img_004.png" width="640"/></a></div><div class="separator" style="clear: both; text-align: center;"><br/></div><div class="separator" style="clear: both; text-align: justify;">  <b><i>From a computer rendering, until the final gold casting, this pendant is now completed. That is why an "Advanced Level of Diamond Setting" is warranted</i></b>..(pushing over a multitude of mini-claws does not do any justice to this item.)</div><div class="separator" style="clear: both; text-align: justify;"> All of my gravers are finely polished as to keep the metal shining, a gentle wheel-polishing is all that is needed. </div><div class="separator" style="clear: both; text-align: center;"><a href="https://blogger.googleusercontent.com/img/b/R29vZ2xl/AVvXsEguKYV0yNk6UqSt-OH2l_Ba_11oHkTuPyNSGrkLa30z3YV0dUT-bIuh3f8xigMlErgHFDMzfYFWJROGfSONHJWSADKz4rqvTyFFpJxqLdreti2VEVP3hUQhseWofhmREfDeBQIx0weUFNQlH6JMmE37cK5d8zEftDe2X8eV0Fu_zxL8uegETa0xrr0p/s800/DSC00140.JPG" style="margin-left: 1em; margin-right: 1em;"><img border="0" data-original-height="600" data-original-width="800" height="480" loading="lazy" src="../../images/unknown/From-Computer-Aided-Design-to-a-finished-pendant.._img_005.png" width="627"/></a></div><br/><div class="separator" style="clear: both; text-align: justify;">  Although that this tutorial-essay is only 6 photos in length, it shows that there is so much to learn.</div><div class="separator" style="clear: both; text-align: justify;"> For any questions, kindly contact me via email; "<b><i><span style="font-size: medium;"><EMAIL></span></i></b>"  </div><br/><div class="separator" style="clear: both; text-align: center;"><br/></div><br/><div class="separator" style="clear: both; text-align: center;"><br/></div><br/><p><br/></p></div>
<div style="clear: both;"></div>
</div>
        </div>
    </div>
</body>
</html>