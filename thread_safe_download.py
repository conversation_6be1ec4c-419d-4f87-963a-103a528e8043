#!/usr/bin/env python3
"""
Thread-Safe Download Missing Articles

Download the 156 missing articles using thread-safe approach.
Each thread creates its own database connection to avoid SQLite threading issues.
"""

import json
import sys
import time
import concurrent.futures
import threading
import sqlite3
from pathlib import Path
from typing import List, Dict

# Add current directory to path
sys.path.insert(0, str(Path(__file__).parent))

from crawler import BlogCrawler
import config

class ThreadSafeMissingDownloader:
    def __init__(self, max_streams: int = 5):
        self.max_streams = max_streams
        self.results_lock = threading.Lock()
        self.index_update_lock = threading.Lock()
        
    def load_missing_articles(self) -> List[Dict]:
        """Load missing articles from the comparison report"""
        report_file = Path("output/metadata/correct_comparison_report.json")
        
        if not report_file.exists():
            print(f"❌ Missing articles report not found: {report_file}")
            print("   Please run correct_comparison.py first to generate the report")
            return []
        
        with open(report_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        missing_articles = data.get('missing_articles', [])
        print(f"📋 Loaded {len(missing_articles)} missing articles from report")
        
        return missing_articles
    
    def is_post_downloaded_thread_safe(self, url: str) -> bool:
        """Thread-safe check if post is already downloaded"""
        try:
            # Create a new database connection for this thread
            conn = sqlite3.connect(config.DATABASE_PATH)
            cursor = conn.cursor()
            
            cursor.execute("SELECT COUNT(*) FROM posts WHERE url = ?", (url,))
            count = cursor.fetchone()[0]
            
            conn.close()
            return count > 0
            
        except Exception as e:
            # If database doesn't exist or has issues, assume not downloaded
            return False
    
    def download_single_article_thread_safe(self, article: Dict, stream_id: int, article_index: int, total_articles: int) -> Dict:
        """Download a single article with thread-safe database operations"""
        result = {
            'article': article,
            'success': False,
            'skipped': False,
            'error': None,
            'stream_id': stream_id
        }
        
        try:
            title_preview = article['title'][:40] + "..." if len(article['title']) > 40 else article['title']
            print(f"   🔄 Stream-{stream_id} [{article_index:3d}/{total_articles}] - {title_preview}")
            
            # Thread-safe check if already downloaded
            if self.is_post_downloaded_thread_safe(article['url']):
                print(f"      ⏭️  Stream-{stream_id} - Already downloaded (skipping)")
                result['skipped'] = True
                return result
            
            # Create a new crawler instance for this thread
            thread_crawler = BlogCrawler()
            
            # Convert to post format expected by crawler
            post = {
                'url': article['url'],
                'title': article['title'],
                'year': article.get('year', 'unknown'),
                'author': 'Gerry Lewy',  # Default author for missing articles
                'date': f"{article.get('year', 'unknown')}-01-01",  # Default date format
                'source': 'thread_safe_missing_download'
            }
            
            # Download the post (this is the time-consuming part)
            success = thread_crawler.download_post_content(post)
            
            if success:
                print(f"      ✅ Stream-{stream_id} - Downloaded successfully")
                result['success'] = True
            else:
                print(f"      ❌ Stream-{stream_id} - Download failed")
                result['error'] = "Download failed"
            
            # Reduced delay for parallel processing
            time.sleep(config.REQUEST_DELAY / self.max_streams)
            
        except Exception as e:
            print(f"      ❌ Stream-{stream_id} - Error: {e}")
            result['error'] = str(e)
        
        return result
    
    def download_articles_parallel(self, missing_articles: List[Dict]) -> Dict:
        """Download all missing articles using thread-safe parallel streams"""
        
        if not missing_articles:
            print("✅ No missing articles to download!")
            return {'success': True, 'downloaded': 0}
        
        # Group by year for organized processing
        articles_by_year = {}
        for article in missing_articles:
            year = article.get('year', 'unknown')
            if year not in articles_by_year:
                articles_by_year[year] = []
            articles_by_year[year].append(article)
        
        results = {
            'total_attempted': 0,
            'successful': 0,
            'failed': 0,
            'skipped': 0,
            'by_year': {}
        }
        
        print(f"\n🎯 Starting THREAD-SAFE PARALLEL download of {len(missing_articles)} missing articles...")
        print(f"🚀 Using {self.max_streams} parallel streams with thread-safe database operations")
        print(f"📊 Years to process: {sorted(articles_by_year.keys(), reverse=True)}")
        print("=" * 80)
        
        start_time = time.time()
        
        # Process years in reverse chronological order (latest first)
        for year in sorted(articles_by_year.keys(), reverse=True):
            articles = articles_by_year[year]
            
            year_results = {
                'attempted': 0,
                'successful': 0,
                'failed': 0,
                'skipped': 0
            }
            
            print(f"\n📅 Processing {year} ({len(articles)} missing articles)")
            print(f"🚀 Using {self.max_streams} thread-safe parallel streams")
            print("=" * 60)
            
            try:
                # Process articles in parallel using ThreadPoolExecutor
                with concurrent.futures.ThreadPoolExecutor(max_workers=self.max_streams) as executor:
                    # Create futures for all articles in this year
                    futures = []
                    for i, article in enumerate(articles, 1):
                        future = executor.submit(
                            self.download_single_article_thread_safe, 
                            article, 
                            (i % self.max_streams) + 1,  # Stream ID 1-5
                            i, 
                            len(articles)
                        )
                        futures.append(future)
                    
                    # Process completed downloads as they finish
                    completed_count = 0
                    for future in concurrent.futures.as_completed(futures):
                        try:
                            result = future.result()
                            completed_count += 1
                            
                            # Update results thread-safely
                            with self.results_lock:
                                if result['skipped']:
                                    year_results['skipped'] += 1
                                    results['skipped'] += 1
                                elif result['success']:
                                    year_results['successful'] += 1
                                    results['successful'] += 1
                                    year_results['attempted'] += 1
                                    results['total_attempted'] += 1
                                else:
                                    year_results['failed'] += 1
                                    results['failed'] += 1
                                    year_results['attempted'] += 1
                                    results['total_attempted'] += 1
                                
                                # Update index every 10 successful downloads
                                if results['successful'] % 10 == 0 and result['success']:
                                    with self.index_update_lock:
                                        print(f"      🎯 Updating index... ({results['successful']} completed)")
                                        # Create a new crawler for index generation
                                        index_crawler = BlogCrawler()
                                        index_crawler.generate_index()
                            
                            # Show progress every 5 completions or at the end
                            if completed_count % 5 == 0 or completed_count == len(articles):
                                with self.results_lock:
                                    print(f"   📊 Progress: {completed_count}/{len(articles)} completed | ✅ {year_results['successful']} success | ❌ {year_results['failed']} failed | ⏭️ {year_results['skipped']} skipped")
                        
                        except Exception as e:
                            print(f"   ❌ Error processing future result: {e}")
                            with self.results_lock:
                                year_results['failed'] += 1
                                results['failed'] += 1
            
            except KeyboardInterrupt:
                print(f"\n⏹️  Download interrupted by user in {year}")
                print(f"📊 Progress so far: {results['successful']} successful, {results['failed']} failed")
                return results
            
            # Store year results
            results['by_year'][year] = year_results
            
            # Update index after each year
            print(f"\n🎯 Updating index after completing {year}...")
            final_crawler = BlogCrawler()
            final_crawler.generate_index()
            
            # Show year summary
            print(f"📊 {year} Summary: {year_results['successful']} successful, {year_results['failed']} failed, {year_results['skipped']} skipped")
        
        # Final summary
        elapsed_time = time.time() - start_time
        print(f"\n" + "=" * 80)
        print("🎉 THREAD-SAFE PARALLEL DOWNLOAD COMPLETE!")
        print("=" * 80)
        print(f"📊 Final Results:")
        print(f"   ✅ Successful: {results['successful']}")
        print(f"   ❌ Failed: {results['failed']}")
        print(f"   ⏭️  Skipped: {results['skipped']}")
        print(f"   ⏱️  Time: {elapsed_time/60:.1f} minutes")
        
        if elapsed_time > 0 and results['successful'] > 0:
            print(f"   🚀 Speed: ~{results['successful']/(elapsed_time/60):.1f} articles/minute")
        
        if results['successful'] > 0:
            print(f"\n🎯 Generating final index...")
            final_crawler = BlogCrawler()
            final_crawler.generate_index()
            print(f"📁 Check {config.OUTPUT_DIR}/index.html to browse the complete archive")
        
        return results
    
    def run_download(self):
        """Run the complete thread-safe parallel missing articles download process"""
        print("=" * 80)
        print("🔒 THREAD-SAFE PARALLEL MISSING ARTICLES DOWNLOADER")
        print("=" * 80)
        
        # Load missing articles
        missing_articles = self.load_missing_articles()
        
        if not missing_articles:
            print("❌ No missing articles found. Please run correct_comparison.py first.")
            return
        
        # Show summary
        articles_by_year = {}
        for article in missing_articles:
            year = article.get('year', 'unknown')
            articles_by_year[year] = articles_by_year.get(year, 0) + 1
        
        print(f"\n📊 Missing Articles Summary:")
        for year in sorted(articles_by_year.keys(), reverse=True):
            print(f"   📅 {year}: {articles_by_year[year]} articles")
        
        print(f"\n🚀 Thread-Safe Parallel Download Configuration:")
        print(f"   🔢 Max Streams: {self.max_streams}")
        print(f"   🔒 Thread-Safe: Each stream creates its own database connection")
        print(f"   ⏱️  Estimated Time: ~{len(missing_articles)/(self.max_streams*2):.1f} minutes")
        
        # Auto-proceed with download (no user input required)
        print(f"\n🚀 Starting download of {len(missing_articles)} missing articles using {self.max_streams} thread-safe parallel streams...")
        print("⏳ Download will begin automatically in 3 seconds...")
        time.sleep(3)
        
        # Start thread-safe parallel download
        results = self.download_articles_parallel(missing_articles)
        
        if results['successful'] > 0:
            print(f"\n🎉 Successfully downloaded {results['successful']} missing articles!")
            print(f"📈 Your archive is now more complete!")
        else:
            print(f"\n⚠️  No articles were successfully downloaded.")
            if results['failed'] > 0:
                print(f"   💡 Check the error messages above for troubleshooting")

def main():
    try:
        # You can adjust the number of streams here (default: 5)
        downloader = ThreadSafeMissingDownloader(max_streams=5)
        downloader.run_download()
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
