#!/usr/bin/env python3
"""
Date Archive Crawler

Now we know the solution! The sidebar contains exact date archive URLs like:
- 2024_12_29_archive.html (2 articles)
- 2024_12_22_archive.html (4 articles)
- etc.

This script will:
1. Extract all date archive URLs from the sidebar
2. Crawl each date archive URL directly
3. Cross-check found vs expected for each date
4. Get all 227 articles for 2024
"""

import requests
import json
import re
import time
from pathlib import Path
from typing import List, Dict, Tuple
from datetime import datetime
from bs4 import BeautifulSoup
from urllib.parse import urljoin

import config

class DateArchiveCrawler:
    def __init__(self, target_year: int = 2024):
        self.target_year = target_year
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        self.date_archives = {}  # date_string -> {url, expected_count}
        
    def extract_date_archives_from_sidebar(self) -> Dict[str, Dict]:
        """Extract all date archive URLs and expected counts from sidebar"""
        print("📊 Extracting date archives from sidebar...")
        
        try:
            response = self.session.get(config.BASE_URL, timeout=30)
            if response.status_code != 200:
                print(f"❌ HTTP {response.status_code}")
                return {}
            
            soup = BeautifulSoup(response.content, 'html.parser')
            archive_section = soup.select_one('#BlogArchive1')
            
            if not archive_section:
                print("❌ Archive section not found")
                return {}
            
            # Find all links that match the date archive pattern
            date_archives = {}
            
            # Look for links like "12/29" with href containing "2024_12_29_archive.html"
            links = archive_section.find_all('a')
            
            for link in links:
                href = link.get('href', '')
                text = link.get_text(strip=True)
                
                # Check if this is a 2024 date archive link
                if f'{self.target_year}_' in href and '_archive.html' in href:
                    # Extract date from URL like "2024_12_29_archive.html"
                    date_match = re.search(rf'{self.target_year}_(\d{{2}})_(\d{{2}})_archive\.html', href)
                    if date_match:
                        month = date_match.group(1)
                        day = date_match.group(2)
                        date_key = f"{month}/{day}"
                        
                        # Find the expected count from the link text or nearby text
                        # Look for pattern like "12/29 (2)" in the text
                        count_match = re.search(r'(\d{1,2})/(\d{1,2})\s*\((\d+)\)', text)
                        if count_match:
                            expected_count = int(count_match.group(3))
                        else:
                            # Look in parent elements for the count
                            parent = link.parent
                            if parent:
                                parent_text = parent.get_text()
                                count_match = re.search(rf'{month}/{day}\s*\((\d+)\)', parent_text)
                                if count_match:
                                    expected_count = int(count_match.group(1))
                                else:
                                    expected_count = 1  # Default assumption
                            else:
                                expected_count = 1
                        
                        date_archives[date_key] = {
                            'url': href,
                            'expected_count': expected_count
                        }
                        
                        print(f"   📅 {date_key}: {expected_count} articles -> {href}")
            
            # Also extract from the raw text using regex
            archive_text = archive_section.get_text()
            
            # Find all date patterns with counts
            date_pattern = re.compile(r'(\d{1,2})/(\d{1,2})\s*\((\d+)\)')
            date_matches = date_pattern.findall(archive_text)
            
            for month_str, day_str, count_str in date_matches:
                month = int(month_str)
                day = int(day_str)
                count = int(count_str)
                
                date_key = f"{month:02d}/{day:02d}"
                
                # Build the expected archive URL
                archive_url = f"https://gerrysdiamondsettingessays.blogspot.com/{self.target_year}_{month:02d}_{day:02d}_archive.html"
                
                # Only add if we haven't seen this date or if we have better info
                if date_key not in date_archives or date_archives[date_key]['expected_count'] == 1:
                    date_archives[date_key] = {
                        'url': archive_url,
                        'expected_count': count
                    }
                    print(f"   📅 {date_key}: {count} articles -> {archive_url}")
            
            total_expected = sum(info['expected_count'] for info in date_archives.values())
            print(f"✅ Found {len(date_archives)} date archives with {total_expected} total expected articles")
            
            return date_archives
            
        except Exception as e:
            print(f"❌ Error extracting date archives: {e}")
            return {}
    
    def crawl_date_archive(self, date_key: str, archive_info: Dict) -> List[Dict]:
        """Crawl a specific date archive URL"""
        url = archive_info['url']
        expected_count = archive_info['expected_count']
        
        print(f"📅 Crawling {date_key} - Expected: {expected_count} articles")
        print(f"   🔗 URL: {url}")
        
        articles = []
        
        try:
            response = self.session.get(url, timeout=20)
            
            if response.status_code == 200:
                soup = BeautifulSoup(response.content, 'html.parser')
                post_containers = soup.select(config.SELECTORS["post_container"])
                
                print(f"   📄 Found {len(post_containers)} post containers")
                
                for i, container in enumerate(post_containers, 1):
                    try:
                        title_elem = container.select_one(config.SELECTORS["post_title"])
                        if title_elem and title_elem.get('href'):
                            article_url = title_elem.get('href')
                            title = title_elem.get_text(strip=True)
                            
                            # Verify it's from target year
                            year_match = re.search(r'/(\d{4})/', article_url)
                            if year_match and int(year_match.group(1)) == self.target_year:
                                article = {
                                    'url': article_url,
                                    'title': title,
                                    'date': date_key,
                                    'source': 'date_archive'
                                }
                                articles.append(article)
                                print(f"      ✅ {i:2d}: {title[:60]}...")
                    except Exception as e:
                        print(f"      ❌ Error processing container {i}: {e}")
                        continue
                
                found_count = len(articles)
                
                if found_count == expected_count:
                    status = "✅ PERFECT"
                elif found_count > expected_count:
                    status = f"➕ EXTRA {found_count - expected_count}"
                else:
                    status = f"⚠️  MISSING {expected_count - found_count}"
                
                print(f"   📊 Result: {found_count}/{expected_count} articles - {status}")
                
            else:
                print(f"   ❌ HTTP {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ Error: {e}")
        
        time.sleep(0.3)  # Be respectful
        return articles
    
    def run_complete_date_archive_crawl(self) -> Dict:
        """Run complete date archive crawl"""
        print("=" * 80)
        print(f"📅 DATE ARCHIVE CRAWLER FOR {self.target_year}")
        print("=" * 80)
        print("🎯 Goal: Crawl each date archive URL to get ALL 227 articles")
        print()
        
        # Step 1: Extract date archives from sidebar
        self.date_archives = self.extract_date_archives_from_sidebar()
        
        if not self.date_archives:
            print("❌ Could not extract date archives from sidebar")
            return {}
        
        total_expected = sum(info['expected_count'] for info in self.date_archives.values())
        print(f"📊 Total expected articles: {total_expected}")
        
        # Step 2: Crawl each date archive
        all_articles = []
        dates_perfect = 0
        dates_missing = 0
        dates_extra = 0
        
        # Sort dates in reverse chronological order (latest first)
        sorted_dates = sorted(self.date_archives.keys(), key=lambda x: (int(x.split('/')[0]), int(x.split('/')[1])), reverse=True)
        
        for date_key in sorted_dates:
            archive_info = self.date_archives[date_key]
            articles = self.crawl_date_archive(date_key, archive_info)
            
            found_count = len(articles)
            expected_count = archive_info['expected_count']
            
            if found_count == expected_count:
                dates_perfect += 1
            elif found_count > expected_count:
                dates_extra += 1
            else:
                dates_missing += 1
            
            all_articles.extend(articles)
        
        # Step 3: Remove duplicates (just in case)
        unique_articles = []
        seen_urls = set()
        
        for article in all_articles:
            if article['url'] not in seen_urls:
                unique_articles.append(article)
                seen_urls.add(article['url'])
        
        duplicates_removed = len(all_articles) - len(unique_articles)
        
        # Step 4: Summary
        total_found = len(unique_articles)
        
        print("\n" + "=" * 80)
        print("📊 DATE ARCHIVE CRAWL SUMMARY")
        print("=" * 80)
        print(f"📅 Total dates processed: {len(self.date_archives)}")
        print(f"📊 Expected articles: {total_expected}")
        print(f"📊 Found articles: {total_found}")
        print(f"🔄 Duplicates removed: {duplicates_removed}")
        print(f"✅ Perfect dates: {dates_perfect}")
        print(f"➕ Extra dates: {dates_extra}")
        print(f"⚠️  Missing dates: {dates_missing}")
        
        if total_found >= total_expected:
            print("🎉 SUCCESS: Found all expected articles!")
        else:
            print(f"⚠️  STILL MISSING: {total_expected - total_found} articles")
        
        # Step 5: Save results
        results = {
            'crawl_date': datetime.now().isoformat(),
            'target_year': self.target_year,
            'total_expected': total_expected,
            'total_found': total_found,
            'duplicates_removed': duplicates_removed,
            'dates_perfect': dates_perfect,
            'dates_missing': dates_missing,
            'dates_extra': dates_extra,
            'date_archives': self.date_archives,
            'all_articles': unique_articles
        }
        
        output_path = Path(config.OUTPUT_DIR) / "metadata" / f"date_archive_crawl_{self.target_year}.json"
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        
        # Also save a simple text list
        text_output_path = Path(config.OUTPUT_DIR) / "metadata" / f"complete_article_list_{self.target_year}.txt"
        with open(text_output_path, 'w', encoding='utf-8') as f:
            f.write(f"Complete Article List for {self.target_year}\n")
            f.write(f"Generated: {datetime.now().isoformat()}\n")
            f.write(f"Total Found: {total_found} articles\n")
            f.write("=" * 80 + "\n\n")
            
            for i, article in enumerate(unique_articles, 1):
                f.write(f"{i:3d}. {article['title']}\n")
                f.write(f"     URL: {article['url']}\n")
                f.write(f"     Date: {article['date']}\n\n")
        
        print(f"\n📋 Results saved to: {output_path}")
        print(f"📄 Article list saved to: {text_output_path}")
        
        return results

def main():
    target_year = 2024
    
    crawler = DateArchiveCrawler(target_year)
    results = crawler.run_complete_date_archive_crawl()
    
    if results:
        print(f"\n🎯 FINAL SUMMARY:")
        print(f"   📊 Found {results['total_found']} articles out of {results['total_expected']} expected")
        print(f"   📅 Processed {len(results['date_archives'])} date archives")
        
        if results['total_found'] >= results['total_expected']:
            print("   🎉 SUCCESS: Ready for duplicate checking and local comparison!")
        else:
            print("   🔍 Some articles still missing - may need additional investigation")

if __name__ == "__main__":
    main()
