<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Errors in using the 'Gravers' while cutting'!</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #fafafa;
            padding: 20px;
        }

        .container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .post-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px;
            text-align: center;
        }

        .post-title {
            font-size: 2.2em;
            font-weight: 700;
            margin-bottom: 15px;
            line-height: 1.2;
        }

        .post-meta {
            font-size: 0.95em;
            opacity: 0.9;
            display: flex;
            justify-content: center;
            gap: 20px;
            flex-wrap: wrap;
        }

        .post-meta a {
            color: #fff;
            text-decoration: underline;
        }

        .post-content {
            padding: 40px;
            font-size: 1.1em;
            line-height: 1.8;
        }

        /* Font normalization - override all inline styles */
        .post-content * {
            font-family: inherit !important;
            font-size: inherit !important;
            line-height: inherit !important;
        }

        .post-content p {
            margin-bottom: 1.2em;
            font-size: 1.1em !important;
        }

        .post-content div {
            font-size: 1.1em !important;
        }

        .post-content span {
            font-size: inherit !important;
        }

        .post-content img {
            max-width: 100%;
            height: auto;
            border-radius: 8px;
            margin: 20px 0;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
        }

        .post-content h1, .post-content h2, .post-content h3 {
            margin: 1.5em 0 0.8em 0;
            color: #2c3e50;
            font-size: 1.4em !important;
            font-weight: 600 !important;
        }

        .post-content strong, .post-content b {
            color: #2c3e50;
            font-weight: 600 !important;
        }

        .post-content em, .post-content i {
            color: #555;
            font-style: italic !important;
        }

        .separator {
            margin: 2em 0;
        }

        .back-link {
            position: fixed;
            top: 20px;
            left: 20px;
            background: #667eea;
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: 500;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;
        }

        .back-link:hover {
            background: #5a6fd8;
            transform: translateY(-2px);
        }

        @media (max-width: 768px) {
            .container {
                margin: 10px;
                border-radius: 8px;
            }

            .post-header {
                padding: 30px 20px;
            }

            .post-title {
                font-size: 1.8em;
            }

            .post-content {
                padding: 30px 20px;
            }

            .post-meta {
                flex-direction: column;
                gap: 10px;
            }
        }
    </style>
</head>
<body>
    <a href="../../../index.html" class="back-link">← Back to Index</a>

    <div class="container">
        <div class="post-header">
            <h1 class="post-title">Errors in using the 'Gravers' while cutting'!</h1>
            <div class="post-meta">
                <span>By: Posted byGerry Lewy</span>
                <span>Date: 2018-09-04T19:23:00-04:00</span>
                <span><a href="https://gerrysdiamondsettingessays.blogspot.com/2018/09/errors-in-using-gravers-while-cutting.html" target="_blank">View Original</a></span>
            </div>
        </div>
        <div class="post-content">
            <div class="post-body entry-content" id="post-body-7251454508209696653" itemprop="description articleBody">
<div dir="ltr" style="text-align: left;" trbidi="on">
<div style="text-align: justify;">
<br/></div>
 <i><b>In my ongoing series of explaining how to use your fantastic Onglette Gravers #5/0, #0 &amp; #1. </b></i>These two gravers can 'make or break' the confidence of a beginner who is just getting started in gemstone setting. <b><i>How difficult is this exercise?</i></b> <b><i>On a Scale of 10, I'd place this at 9+ on the Difficulty Scale.</i></b><br/>
<br/>
<i><b> What is required are 4-5 years of graver manipulation! </b></i>You should have the basic graver carving and understanding just what might happen if certain rules are not followed, and there are many..;&gt;(!<br/>
<i>BTW, if some of these photographs are not crystal clear, my apologies!</i><br/>
<br/>
<b><i> Now let's get on with this essay!!</i></b><br/>
<i><b> What appears to be going on here? </b></i>For the first few stone-holes being shown, the results are decent looking, then the cutting line seems to be drifting away from some of the lines of holes. Not nice!<br/>
<br/>
<i><b>With the "Singular Pointing Arrow"</b></i>, you can see that the rough-cuts for the beginning of making beads the metal cutting appears to be way off! <b><i>A proverbial 'mess' in the making!</i></b><br/>
<br/>
<div class="separator" style="clear: both; text-align: center;">
<a href="https://blogger.googleusercontent.com/img/b/R29vZ2xl/AVvXsEj7UVmGwvq6-laYV1_PWLHcPxD0C9m3w9npnzaXtn5k8ExhQXX1sg2CZxySoyDIp5cs2ye81HkQmNPQSaNKlaQZCXxHRvmoWwvY5AV2c69yLi9rZXPJxxk7sI27f9cWleKLLGRNLkwfAP0/s1600/3+y%2527s.jpg" style="margin-left: 1em; margin-right: 1em;"><img border="0" data-original-height="320" data-original-width="240" loading="lazy" src="../../images/unknown/Errors-in-using-the-Gravers-while-cutting_img_001.png"/></a></div>
<br/>
<i><b>What happened again here?</b></i> I was attempting to prepare a cut with my 'Separating Disk' to make little cuts for the Fish-Tail pattern to be initiated. I actually erred, why did I do this?<br/>
<br/>
<b><i>It is sometimes the basic level 'setter-in-training' can make the same mistake</i></b>. Please be sure you are always aware of the pattern being created. Don't take anything for granted, even the slightest unnecessary cut now might be a major problem during the next few following 'cuts'.<br/>
<div class="separator" style="clear: both; text-align: center;">
<a href="https://blogger.googleusercontent.com/img/b/R29vZ2xl/AVvXsEjcKHtaT5kk1wNgc5PBneJwwpvLBTi51tByrfBoL8Gnp7J6M_sZAQceOnp2lYWUpMRUXuy-F2Ln_f_yqknQYXKAsvogYJO53G_webTaISDkj3NICvpXlvyRApsOC8O_7Bt1pe7hSmIx7Ec/s1600/error%25231.jpg" style="margin-left: 1em; margin-right: 1em;"><img border="0" data-original-height="640" data-original-width="480" height="640" loading="lazy" src="../../images/unknown/Errors-in-using-the-Gravers-while-cutting_img_002.png" width="480"/></a></div>
<br/>
<i><b>In the second photo, </b></i>you can see the graver cuts are not all against the hole for the stones.<br/>
<div class="separator" style="clear: both; text-align: center;">
<a href="https://blogger.googleusercontent.com/img/b/R29vZ2xl/AVvXsEhOlCDQJDCnvNGrHUnCQV_jk1NJhT5faTBT-qc7lN0mCQwKvdIp0q4bDe0K_jTzlNL2I2z14WgiYmq439ACkbHUYdK7__BdfIOSMRz8yzcf5oWDsQ9H3AwuCBJAYfqzz2UfNUjk1gdO-W8/s1600/error%25232.jpg" style="margin-left: 1em; margin-right: 1em;"><img border="0" data-original-height="640" data-original-width="480" height="640" loading="lazy" src="../../images/unknown/Errors-in-using-the-Gravers-while-cutting_img_003.png" width="480"/></a></div>
<br/>
<i><b>The 3-arrows pointing to the beginning stages of a 'bead' are now so minuscule</b></i>, no setter (even I) can prepare to set a stone! The mini-beads are so small, this section is now being wasted!<br/>
<div class="separator" style="clear: both; text-align: center;">
<a href="https://blogger.googleusercontent.com/img/b/R29vZ2xl/AVvXsEgOLQ9P3tHV2ifDGKnpOsHWrI-Q183DQ79qsZtYuII7YMy3QJHeHKgCcdnpRSBLb_gvEUlX6MgI7Dc9zOF6tRz_fZbguRN7Ca44YVFcNY9yH1EDOW_8-bbWAhE5OD8UA4FHxKxwXxjtarQ/s1600/errror%25233.jpg" style="margin-left: 1em; margin-right: 1em;"><img border="0" data-original-height="640" data-original-width="480" height="640" loading="lazy" src="../../images/unknown/Errors-in-using-the-Gravers-while-cutting_img_004.png" width="480"/></a></div>
<br/>
<b><i>My graver cutting is in a straight line</i></b> (for now) <i><b>but the holes are not in line</b></i>. This drilling was done in the wax stages. At 0.10mm's out of alignment, this is still too far away from the graver-cuttings!<br/>
<div class="separator" style="clear: both; text-align: center;">
<a href="https://blogger.googleusercontent.com/img/b/R29vZ2xl/AVvXsEhVAAm2tM8gRshIO7dDpmti31kCMabjxaKJsMQMxJmJnsQ-WU201medPwjBDHYkwwqDAKmoxkBVrruWACYsHPTesBiKAax7XU1RxcHh1HM1Mttf0RNT8Qx54ULl7Vkx7szKxf2SKYcm7yA/s1600/missed+cut%2521.jpg" style="margin-left: 1em; margin-right: 1em;"><img border="0" data-original-height="320" data-original-width="240" height="400" loading="lazy" src="../../images/unknown/Errors-in-using-the-Gravers-while-cutting_img_005.png" width="300"/></a></div>
<i><b><br/></b></i>
<i><b>This little area is so now difficult to repair,</b></i> to say 'YUK' is an understatement. I would have my jeweller solder those grooves on the edge totally filled in and start again!<br/>
<div class="separator" style="clear: both; text-align: center;">
<a href="https://blogger.googleusercontent.com/img/b/R29vZ2xl/AVvXsEixGWUvvf6JnMCBotmGjoOzwbZrP67ZVCtuUVcDUuWKql2vSsgKF7OqIAhyphenhypheniKg8Jp7-Djbxlg23B0d_8yPQvPaJcJDYaDKtqK1_pZgHuhyphenhyphen74HmwYYTAZitH_HvNg63NYN_w0Dy1m_KhTSk/s1600/ouch%25232.jpg" style="margin-left: 1em; margin-right: 1em;"><img border="0" data-original-height="320" data-original-width="240" height="400" loading="lazy" src="../../images/unknown/Errors-in-using-the-Gravers-while-cutting_img_006.png" width="300"/></a></div>
<b><i><br/></i></b>
<b><i>Now you can see how the holes are again, not in line</i></b>. This is so-o important in these preliminary stages of cutting. If these holes are not in line, you can do either scrap the item or spend $$'s in a costly repair (soldering fees) to start again!<br/>
<div class="separator" style="clear: both; text-align: center;">
<a href="https://blogger.googleusercontent.com/img/b/R29vZ2xl/AVvXsEg6jWt_phZkcEqLPQIDPrBJWoA19xNoVumN9ltoQqnQFldetiJyv5QI5T0fdZ1N-MgE18AYaKH4OsHtkTGP39MoLXml9EI_ifRd7LU-4cbb7I7kYGKWgEDq66ukmYEJkjV45OiW7_-snMc/s1600/ouch.jpg" style="margin-left: 1em; margin-right: 1em;"><img border="0" data-original-height="320" data-original-width="240" loading="lazy" src="../../images/unknown/Errors-in-using-the-Gravers-while-cutting_img_007.png"/></a></div>
<b><i><br/></i></b> <b><i>In this photograph, something happened here, what was it?</i></b> This needs a total "solder-to-fix". The 'Separating Disk' cut into the wrong area again.<br/>
<div class="separator" style="clear: both; text-align: center;">
<a href="https://blogger.googleusercontent.com/img/b/R29vZ2xl/AVvXsEj5kukZfWc5HJEjgJa-BoUPFlNtzHP6FLXmQHz2MQdob35XbOpxfT5_zdAZ-GzxJgE8OjzaPbWJGlBFEcnNDSa-gG2wgckBXe1MVnDkkqrVZ5Y-14AZioFFdSZM6zMO83d36YDASC-F36U/s1600/what%2521.jpg" style="margin-left: 1em; margin-right: 1em;"><img border="0" data-original-height="320" data-original-width="240" height="640" loading="lazy" src="../../images/unknown/Errors-in-using-the-Gravers-while-cutting_img_008.png" width="480"/></a></div>
<br/>
<i><b>Mistakes in using the separating disk</b></i>, I won't be using this method again! But now you can see just what can happen when care is not used! <b>All of these "mistakes" were done on purpose.<br/>
</b><div class="separator" style="clear: both; text-align: center;">
</div>
<div class="separator" style="clear: both; text-align: center;">
<a href="https://blogger.googleusercontent.com/img/b/R29vZ2xl/AVvXsEjA6ZMfmqJIZn4fFizKqXuJc77Mzs3wNZskobk1nles2v9XlEaepnIycBzapL43EcEidVwgXHEKm8AV0DC8YheRPAexjMgTAoXiO3pnJLfAn4RjSvM59erqlTuaLIeLDHa0xwQUGYVp_BA/s1600/yuk+%2526+ouch.JPG" style="margin-left: 1em; margin-right: 1em; text-align: center;"><b><img border="0" data-original-height="320" data-original-width="320" loading="lazy" src="../../images/unknown/Errors-in-using-the-Gravers-while-cutting_img_009.png"/></b></a></div>
 <br/>
<i><b><br/></b></i>
<i><b> These little cuts were all created with my Onglette #1 graver</b></i> and having the width totally modified.<br/>
<b><i>Why so?</i></b> I need to cut deeper, but never wider in these "Rough-cutting" stages. If the lines are straight and all of the beads are well formed, I would gladly forge ahead.<br/>
<b><i><br/></i></b>
<b><i> I am using a 'greatly modified' graver,</i></b> I shaved off most of the metal on both sides and it is down to only .03mm's in (the cutting) width. I created this very thin graver face, as this has to get me into the little corners of the metal that a wider graver-face won't be able to reach!<br/>
<div class="separator" style="clear: both; text-align: center;">
<a href="https://blogger.googleusercontent.com/img/b/R29vZ2xl/AVvXsEgos1NXEbSt2kZ46k9pKLTj-6A1gYLBUnXq9jHb9Wcsx5l2OeR92I4E-j9E7S2SQPDtqcHJ-TBCTS0rr1iVNIbNEsnIp49DpDmQibutx_4lddhM_Jj9ReuFcpjl5uDEqoTM5MDaaIHhWBk/s1600/3+gravers.jpg" style="margin-left: 1em; margin-right: 1em;"><img border="0" data-original-height="640" data-original-width="480" height="640" loading="lazy" src="../../images/unknown/Errors-in-using-the-Gravers-while-cutting_img_010.png" width="480"/></a></div>
<br/>
<br/>
 <b><i>But as I see it now, the chances of continuing further are not good</i></b>.  Too many errors with many mistakes to make this an appealing "Piece of Beauty".<br/>
<div class="separator" style="clear: both; text-align: center;">
<a href="https://blogger.googleusercontent.com/img/b/R29vZ2xl/AVvXsEhBShG1ZkLbkh-PAuI2KSQdt73btSISIMnckgiF-UCsKkzohrhkWIh_sYHsxg01M6aYoQ7AM8hSsBp5p3TajMJsTzUwryDTDndTSC0Dk2_WUkw7_1EKiSI8SSm_ooSA1tOaXwP0SeVBUGE/s1600/yuk%25231+%25282%2529.jpg" style="margin-left: 1em; margin-right: 1em;"><img border="0" data-original-height="320" data-original-width="240" height="400" loading="lazy" src="../../images/unknown/Errors-in-using-the-Gravers-while-cutting_img_011.png" width="300"/></a></div>
<b><i><br/></i></b> <b><i>T</i></b><i><b>he forming of these 'beads' is now a thing of the past</b></i>. This item is now getting closer to being 'scrapped' or metal-refined. As for what you can see, the 'cuts' are not good and not to be continued any further.<br/>
<div class="separator" style="clear: both; text-align: center;">
<a href="https://blogger.googleusercontent.com/img/b/R29vZ2xl/AVvXsEilzq3tmXseWG57RPTnNnTerPXJA4bd05mXJ-gWGsL-Ztn9sxmGe3J0VmGBRahs0o7aJau76g-i_ouyFDAHbQSNGsYplCvAfL889zE0M-pc9MyWsHf-0ozpd-6823917m17SojkZ4z7zUY/s1600/yuk%25232+%25282%2529.jpg" style="margin-left: 1em; margin-right: 1em;"><img border="0" data-original-height="320" data-original-width="240" loading="lazy" src="../../images/unknown/Errors-in-using-the-Gravers-while-cutting_img_012.png"/></a></div>
<div class="separator" style="clear: both; text-align: left;">
<br/></div>
<div class="separator" style="clear: both; text-align: left;">
Here are a few photographs of the finished Fish-Tail pendant. These photos are 'eye-candy' for me.;)</div>
<div class="separator" style="clear: both; text-align: center;">
<a href="https://blogger.googleusercontent.com/img/b/R29vZ2xl/AVvXsEheNmF444grU0ga87IWVOPcwWIzdS-XrxVtTdAUXIxTl9lFS9QXVF7QtlASTDXlM2ku7lGs3vh-02W2iiQd-mtpZFZTkz8ohPLV8zqShEcefX5IW6hSevLE49SFrn8Is-Y-T_OuBmSmtvk/s1600/b4+%2526+after.jpg" style="margin-left: 1em; margin-right: 1em;"><img border="0" data-original-height="366" data-original-width="640" height="366" loading="lazy" src="../../images/unknown/Errors-in-using-the-Gravers-while-cutting_img_013.png" width="640"/></a></div>
<div class="separator" style="clear: both; text-align: left;">
<b><i><br/></i></b></div>
<div class="separator" style="clear: both; text-align: left;">
<b><i>This pendant took me only 2.5 hours 'start to finish'.</i></b> The cubic zirconia shown are only 1.5 mm's in size. I could have set stones at 1.25mm's, (only if there were available in this city)!</div>
<div class="separator" style="clear: both; text-align: center;">
<a href="https://blogger.googleusercontent.com/img/b/R29vZ2xl/AVvXsEjaI_vPs-c3dOlRxWqXoRh6eGcEtrBblZb1g6kDXNlGPUtUBUnjPx24dx-blT53VeOi5uoe6cHroS7KNCEUXIQaUG3gq_ITgC8FX94jEU2s4lDMski-qfdoMbjvlbdfmkoVjrMPTQrpyKM/s1600/best.jpg" style="margin-left: 1em; margin-right: 1em;"><img border="0" data-original-height="640" data-original-width="638" height="640" loading="lazy" src="../../images/unknown/Errors-in-using-the-Gravers-while-cutting_img_014.png" width="638"/></a></div>
<div class="separator" style="clear: both; text-align: left;">
 <b><i>In another essay, I will explain the Bright-Cutting stages for making this pendant.</i></b> I will walk you through the many rules of using the many Onglette &amp; Flat gravers. You too can achieve this fantastic pattern with my help and assistance.</div>
<div class="separator" style="clear: both; text-align: left;">
<br/></div>
<div class="separator" style="clear: both; text-align: left;">
<i><b>BTW, none of my 'setting with gravers' patterns is using any Computer Aided Designing (CAD) program.</b></i></div>
<div class="separator" style="clear: both; text-align: left;">
 Gerrylewy18 (at) gmail.com</div>
<div class="separator" style="clear: both; text-align: left;">
<br/></div>
</div>
<div style="clear: both;"></div>
</div>
        </div>
    </div>
</body>
</html>