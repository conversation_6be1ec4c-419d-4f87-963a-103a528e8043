"""
Main crawler module for <PERSON>'s Diamond Setting Essays Blog
"""

import requests
import time
import logging
import sqlite3
import json
import re
from pathlib import Path
from urllib.parse import urljoin, urlparse
from datetime import datetime
from typing import List, Dict, Optional, Tuple

from bs4 import BeautifulSoup
from PIL import Image
import config

class BlogCrawler:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({'User-Agent': config.USER_AGENT})
        self.setup_directories()
        self.setup_logging()
        self.setup_database()
        self.posts_downloaded = 0
        self.images_downloaded = 0
        
    def setup_directories(self):
        """Create necessary directories"""
        for directory in [config.OUTPUT_DIR, config.ARCHIVE_DIR, config.IMAGES_DIR, 
                         config.METADATA_DIR, config.STATIC_DIR]:
            directory.mkdir(parents=True, exist_ok=True)
            
    def setup_logging(self):
        """Setup logging configuration"""
        logging.basicConfig(
            level=getattr(logging, config.LOG_LEVEL),
            format=config.LOG_FORMAT,
            handlers=[
                logging.FileHandler(config.LOG_FILE),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        
    def setup_database(self):
        """Initialize SQLite database for tracking progress"""
        self.db_path = config.METADATA_DIR / config.FILENAME_PATTERNS["log"]
        self.conn = sqlite3.connect(self.db_path)
        self.conn.executescript(config.DB_SCHEMA)
        self.conn.commit()
        
    def make_request(self, url: str, retries: int = 0) -> Optional[requests.Response]:
        """Make HTTP request with retry logic"""
        try:
            self.logger.info(f"Fetching: {url}")
            response = self.session.get(url, timeout=config.TIMEOUT)
            response.raise_for_status()
            time.sleep(config.REQUEST_DELAY)  # Respectful crawling
            return response
        except requests.RequestException as e:
            if retries < config.MAX_RETRIES:
                self.logger.warning(f"Request failed, retrying ({retries + 1}/{config.MAX_RETRIES}): {e}")
                time.sleep(config.REQUEST_DELAY * (retries + 1))
                return self.make_request(url, retries + 1)
            else:
                self.logger.error(f"Request failed after {config.MAX_RETRIES} retries: {e}")
                return None
                
    def extract_posts_from_page(self, url: str) -> List[Dict]:
        """Extract post information from a page"""
        response = self.make_request(url)
        if not response:
            return []
            
        soup = BeautifulSoup(response.content, 'html.parser')
        posts = []
        
        # Find all post containers
        post_containers = soup.select(config.SELECTORS["post_container"])
        
        for container in post_containers:
            try:
                # Extract post title and URL
                title_elem = container.select_one(config.SELECTORS["post_title"])
                if not title_elem:
                    continue
                    
                post_url = title_elem.get('href')
                title = title_elem.get_text(strip=True)
                
                # Extract date
                date_elem = container.select_one(config.SELECTORS["post_date"])
                date_str = date_elem.get('title') if date_elem else ""
                
                # Extract author
                author_elem = container.select_one(config.SELECTORS["post_author"])
                author = author_elem.get_text(strip=True) if author_elem else "Gerry Lewy"
                
                posts.append({
                    'url': post_url,
                    'title': title,
                    'date': date_str,
                    'author': author,
                    'source_page': url
                })
                
            except Exception as e:
                self.logger.warning(f"Error extracting post from container: {e}")
                continue
                
        self.logger.info(f"Extracted {len(posts)} posts from {url}")
        return posts
        
    def discover_archive_urls(self) -> List[str]:
        """Discover all archive URLs to crawl"""
        archive_urls = [config.BASE_URL]  # Start with main page
        
        # Get main page to find archive links
        response = self.make_request(config.BASE_URL)
        if not response:
            return archive_urls
            
        soup = BeautifulSoup(response.content, 'html.parser')
        
        # Find archive links in sidebar
        archive_links = soup.find_all('a', href=re.compile(r'archive\.html'))
        
        for link in archive_links:
            href = link.get('href')
            if href:
                full_url = urljoin(config.BASE_URL, href)
                if full_url not in archive_urls:
                    archive_urls.append(full_url)
                    
        # Limit for testing
        if config.TEST_MODE:
            archive_urls = archive_urls[:10]  # Limit to first 10 for testing
            
        self.logger.info(f"Discovered {len(archive_urls)} archive URLs")
        return archive_urls
        
    def download_post_content(self, post: Dict) -> Optional[str]:
        """Download and process individual post content"""
        response = self.make_request(post['url'])
        if not response:
            return None
            
        soup = BeautifulSoup(response.content, 'html.parser')
        
        # Extract main content
        content_elem = soup.select_one(config.SELECTORS["post_content"])
        if not content_elem:
            self.logger.warning(f"No content found for post: {post['url']}")
            return None
            
        # Process images in content
        images = content_elem.select(config.SELECTORS["images"])
        image_mappings = {}
        
        for i, img in enumerate(images):
            img_url = img.get('src')
            if img_url:
                # Download and convert image
                local_path = self.download_image(img_url, post, i)
                if local_path:
                    image_mappings[img_url] = local_path
                    
        # Replace image URLs with local paths
        for original_url, local_path in image_mappings.items():
            content_html = str(content_elem)
            content_html = content_html.replace(original_url, str(local_path))
            content_elem = BeautifulSoup(content_html, 'html.parser')
            
        return str(content_elem)
        
    def download_image(self, img_url: str, post: Dict, index: int) -> Optional[Path]:
        """Download and convert image to PNG"""
        try:
            # Make URL absolute
            if img_url.startswith('//'):
                img_url = 'https:' + img_url
            elif img_url.startswith('/'):
                img_url = urljoin(config.BASE_URL, img_url)
                
            response = self.make_request(img_url)
            if not response:
                return None
                
            # Create filename
            filename = config.FILENAME_PATTERNS["image"].format(index=index + 1)
            
            # Create year/month directory structure
            post_date = self.parse_date(post.get('date', ''))
            if post_date:
                year_dir = config.IMAGES_DIR / str(post_date.year)
                month_dir = year_dir / f"{post_date.month:02d}"
            else:
                month_dir = config.IMAGES_DIR / "unknown"
                
            month_dir.mkdir(parents=True, exist_ok=True)
            image_path = month_dir / filename
            
            # Save and convert image
            with open(image_path.with_suffix('.tmp'), 'wb') as f:
                f.write(response.content)
                
            # Convert to PNG if enabled
            if config.CONVERT_TO_PNG:
                try:
                    with Image.open(image_path.with_suffix('.tmp')) as img:
                        # Optimize if needed
                        if config.OPTIMIZE_IMAGES:
                            img.thumbnail(config.MAX_IMAGE_SIZE, Image.Resampling.LANCZOS)
                            
                        # Convert to RGB if necessary (for PNG)
                        if img.mode in ('RGBA', 'LA', 'P'):
                            img = img.convert('RGB')
                            
                        img.save(image_path, 'PNG', optimize=True)
                        
                    # Remove temporary file
                    image_path.with_suffix('.tmp').unlink()
                    
                    self.images_downloaded += 1
                    self.logger.info(f"Downloaded and converted image: {filename}")
                    return image_path
                    
                except Exception as e:
                    self.logger.error(f"Error converting image {img_url}: {e}")
                    # Keep original if conversion fails
                    image_path.with_suffix('.tmp').rename(image_path.with_suffix('.jpg'))
                    return image_path.with_suffix('.jpg')
            else:
                image_path.with_suffix('.tmp').rename(image_path)
                return image_path
                
        except Exception as e:
            self.logger.error(f"Error downloading image {img_url}: {e}")
            return None
            
    def parse_date(self, date_str: str) -> Optional[datetime]:
        """Parse date string to datetime object"""
        if not date_str:
            return None
            
        # Try different date formats
        formats = [
            "%B %d, %Y",  # "July 04, 2025"
            "%Y-%m-%d",   # "2025-07-04"
            "%m/%d/%Y",   # "07/04/2025"
        ]
        
        for fmt in formats:
            try:
                return datetime.strptime(date_str, fmt)
            except ValueError:
                continue
                
        self.logger.warning(f"Could not parse date: {date_str}")
        return None
        
    def save_post(self, post: Dict, content: str) -> Optional[Path]:
        """Save post content to file"""
        try:
            # Create filename
            post_date = self.parse_date(post.get('date', ''))
            if post_date:
                year = post_date.year
                month = post_date.month
                day = post_date.day
            else:
                # Use current date as fallback
                now = datetime.now()
                year, month, day = now.year, now.month, now.day

            # Create safe filename from title
            title_slug = re.sub(r'[^\w\s-]', '', post['title']).strip()
            title_slug = re.sub(r'[-\s]+', '-', title_slug)[:50]  # Limit length

            filename = config.FILENAME_PATTERNS["post"].format(
                year=year, month=month, day=day, title_slug=title_slug
            )

            # Create directory structure
            year_dir = config.ARCHIVE_DIR / str(year)
            month_dir = year_dir / f"{month:02d}"
            month_dir.mkdir(parents=True, exist_ok=True)

            post_path = month_dir / filename

            # Create HTML template
            html_content = f"""<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{post['title']}</title>
    <style>
        body {{ font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }}
        .post-header {{ border-bottom: 1px solid #ccc; margin-bottom: 20px; padding-bottom: 10px; }}
        .post-title {{ color: #333; }}
        .post-meta {{ color: #666; font-size: 0.9em; }}
        .post-content {{ line-height: 1.6; }}
        .post-content img {{ max-width: 100%; height: auto; }}
    </style>
</head>
<body>
    <div class="post-header">
        <h1 class="post-title">{post['title']}</h1>
        <div class="post-meta">
            <span>By: {post['author']}</span> |
            <span>Date: {post['date']}</span> |
            <span>Source: <a href="{post['url']}">{post['url']}</a></span>
        </div>
    </div>
    <div class="post-content">
        {content}
    </div>
</body>
</html>"""

            with open(post_path, 'w', encoding='utf-8') as f:
                f.write(html_content)

            self.posts_downloaded += 1
            self.logger.info(f"Saved post: {filename}")
            return post_path

        except Exception as e:
            self.logger.error(f"Error saving post {post['title']}: {e}")
            return None

    def run_test_crawl(self):
        """Run a test crawl with limited scope"""
        self.logger.info("Starting test crawl...")

        try:
            # Discover archive URLs
            archive_urls = self.discover_archive_urls()

            all_posts = []
            posts_processed = 0

            # Extract posts from each archive page
            for url in archive_urls:
                if config.TEST_MODE and posts_processed >= config.TEST_MAX_POSTS:
                    break

                posts = self.extract_posts_from_page(url)
                all_posts.extend(posts)
                posts_processed += len(posts)

            # Limit posts for testing
            if config.TEST_MODE:
                all_posts = all_posts[:config.TEST_MAX_POSTS]

            self.logger.info(f"Found {len(all_posts)} posts to process")

            # Process each post
            for i, post in enumerate(all_posts, 1):
                self.logger.info(f"Processing post {i}/{len(all_posts)}: {post['title']}")

                # Download post content
                content = self.download_post_content(post)
                if content:
                    # Save post
                    saved_path = self.save_post(post, content)
                    if saved_path:
                        self.logger.info(f"Successfully processed: {post['title']}")
                    else:
                        self.logger.error(f"Failed to save: {post['title']}")
                else:
                    self.logger.error(f"Failed to download content: {post['title']}")

            # Generate summary
            self.generate_summary()

        except Exception as e:
            self.logger.error(f"Error during crawl: {e}")
            raise

    def generate_summary(self):
        """Generate crawl summary"""
        summary = {
            "crawl_date": datetime.now().isoformat(),
            "posts_downloaded": self.posts_downloaded,
            "images_downloaded": self.images_downloaded,
            "output_directory": str(config.OUTPUT_DIR),
            "test_mode": config.TEST_MODE
        }

        summary_path = config.METADATA_DIR / "crawl_summary.json"
        with open(summary_path, 'w') as f:
            json.dump(summary, f, indent=2)

        self.logger.info(f"Crawl completed! Posts: {self.posts_downloaded}, Images: {self.images_downloaded}")
        self.logger.info(f"Summary saved to: {summary_path}")

    def __del__(self):
        """Cleanup database connection"""
        if hasattr(self, 'conn'):
            self.conn.close()
