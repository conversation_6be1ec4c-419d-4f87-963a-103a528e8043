"""
Main crawler module for Gerry's Diamond Setting Essays Blog
"""

import requests
import time
import logging
import sqlite3
import json
import re
from pathlib import Path
from urllib.parse import urljoin, urlparse
from datetime import datetime
from typing import List, Dict, Optional, Tuple

from bs4 import BeautifulSoup
from PIL import Image
import config

class BlogCrawler:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({'User-Agent': config.USER_AGENT})
        self.setup_directories()
        self.setup_logging()
        self.setup_database()
        self.posts_downloaded = 0
        self.images_downloaded = 0
        self.all_posts_info = []  # Store post info for index generation
        
    def setup_directories(self):
        """Create necessary directories"""
        for directory in [config.OUTPUT_DIR, config.ARCHIVE_DIR, config.IMAGES_DIR, 
                         config.METADATA_DIR, config.STATIC_DIR]:
            directory.mkdir(parents=True, exist_ok=True)
            
    def setup_logging(self):
        """Setup logging configuration"""
        logging.basicConfig(
            level=getattr(logging, config.LOG_LEVEL),
            format=config.LOG_FORMAT,
            handlers=[
                logging.FileHandler(config.LOG_FILE),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        
    def setup_database(self):
        """Initialize SQLite database for tracking progress"""
        self.db_path = config.METADATA_DIR / config.FILENAME_PATTERNS["log"]
        self.conn = sqlite3.connect(self.db_path)
        self.conn.executescript(config.DB_SCHEMA)
        self.conn.commit()
        
    def make_request(self, url: str, retries: int = 0) -> Optional[requests.Response]:
        """Make HTTP request with retry logic"""
        try:
            self.logger.info(f"Fetching: {url}")
            response = self.session.get(url, timeout=config.TIMEOUT)
            response.raise_for_status()
            time.sleep(config.REQUEST_DELAY)  # Respectful crawling
            return response
        except requests.RequestException as e:
            if retries < config.MAX_RETRIES:
                self.logger.warning(f"Request failed, retrying ({retries + 1}/{config.MAX_RETRIES}): {e}")
                time.sleep(config.REQUEST_DELAY * (retries + 1))
                return self.make_request(url, retries + 1)
            else:
                self.logger.error(f"Request failed after {config.MAX_RETRIES} retries: {e}")
                return None
                
    def extract_posts_from_page(self, url: str) -> List[Dict]:
        """Extract post information from a page"""
        response = self.make_request(url)
        if not response:
            return []
            
        soup = BeautifulSoup(response.content, 'html.parser')
        posts = []
        
        # Find all post containers
        post_containers = soup.select(config.SELECTORS["post_container"])
        
        for container in post_containers:
            try:
                # Extract post title and URL
                title_elem = container.select_one(config.SELECTORS["post_title"])
                if not title_elem:
                    continue
                    
                post_url = title_elem.get('href')
                title = title_elem.get_text(strip=True)
                
                # Extract date
                date_elem = container.select_one(config.SELECTORS["post_date"])
                date_str = date_elem.get('title') if date_elem else ""
                
                # Extract author
                author_elem = container.select_one(config.SELECTORS["post_author"])
                author = author_elem.get_text(strip=True) if author_elem else "Gerry Lewy"
                
                posts.append({
                    'url': post_url,
                    'title': title,
                    'date': date_str,
                    'author': author,
                    'source_page': url
                })
                
            except Exception as e:
                self.logger.warning(f"Error extracting post from container: {e}")
                continue
                
        self.logger.info(f"Extracted {len(posts)} posts from {url}")
        return posts
        
    def discover_archive_urls(self) -> List[str]:
        """Discover all archive URLs to crawl"""
        archive_urls = [config.BASE_URL]  # Start with main page
        
        # Get main page to find archive links
        response = self.make_request(config.BASE_URL)
        if not response:
            return archive_urls
            
        soup = BeautifulSoup(response.content, 'html.parser')
        
        # Find archive links in sidebar
        archive_links = soup.find_all('a', href=re.compile(r'archive\.html'))
        
        for link in archive_links:
            href = link.get('href')
            if href:
                full_url = urljoin(config.BASE_URL, href)
                if full_url not in archive_urls:
                    archive_urls.append(full_url)
                    
        # Limit for testing
        if config.TEST_MODE:
            archive_urls = archive_urls[:10]  # Limit to first 10 for testing
            
        self.logger.info(f"Discovered {len(archive_urls)} archive URLs")
        return archive_urls
        
    def download_post_content(self, post: Dict) -> bool:
        """Download and process individual post content"""
        response = self.make_request(post['url'])
        if not response:
            return False

        soup = BeautifulSoup(response.content, 'html.parser')

        # Extract main content
        content_elem = soup.select_one(config.SELECTORS["post_content"])
        if not content_elem:
            self.logger.warning(f"No content found for post: {post['url']}")
            return False

        # Process images in content
        images = content_elem.select(config.SELECTORS["images"])
        image_mappings = {}

        for i, img in enumerate(images):
            img_url = img.get('src')
            if img_url:
                # Download and convert image
                local_path = self.download_image(img_url, post, i)
                if local_path:
                    image_mappings[img_url] = local_path

        # Replace image URLs with local paths using BeautifulSoup for more reliable parsing
        for original_url, local_path in image_mappings.items():
            # Convert to relative path from the HTML file location
            if local_path.parent.name == "unknown":
                relative_path = f"../../../images/unknown/{local_path.name}"
            else:
                # For year/month structure: ../../../images/YYYY/MM/filename.png
                year = local_path.parent.parent.name
                month = local_path.parent.name
                relative_path = f"../../../images/{year}/{month}/{local_path.name}"

            # Find all img tags with this src URL
            img_tags = content_elem.find_all('img', src=original_url)
            for img_tag in img_tags:
                img_tag['src'] = relative_path

            # Find all anchor tags that link to the full-size version of this image
            # The href usually points to s1600 version while src points to s320-rw version
            base_url_pattern = original_url.split('/s320-rw/')[0] if '/s320-rw/' in original_url else original_url.split('/s640-rw/')[0] if '/s640-rw/' in original_url else original_url.rsplit('/', 1)[0]

            # Look for anchor tags that contain this image
            for a_tag in content_elem.find_all('a'):
                href = a_tag.get('href', '')
                if href and base_url_pattern in href:
                    a_tag['href'] = relative_path

        # Save the post with processed content
        final_content = str(content_elem)
        saved_path = self.save_post(post, final_content)

        return saved_path is not None
        
    def download_image(self, img_url: str, post: Dict, index: int) -> Optional[Path]:
        """Download and convert image to PNG"""
        try:
            # Make URL absolute
            if img_url.startswith('//'):
                img_url = 'https:' + img_url
            elif img_url.startswith('/'):
                img_url = urljoin(config.BASE_URL, img_url)

            response = self.make_request(img_url)
            if not response:
                return None

            # Create unique filename using post slug and image index
            post_slug = post.get('title', 'unknown')
            # Remove all invalid filename characters for Windows
            post_slug = re.sub(r'[<>:"/\\|?*=]', '', post_slug)  # Remove invalid chars including =>
            post_slug = post_slug.replace(' ', '-').replace('!', '').replace(',', '').replace("'", '')
            post_slug = re.sub(r'-+', '-', post_slug).strip('-')[:50]  # Clean up multiple dashes
            filename = f"{post_slug}_img_{index + 1:03d}.png"

            # Create year/month directory structure
            post_date = self.parse_date(post.get('date', ''))
            if post_date:
                year_dir = config.IMAGES_DIR / str(post_date.year)
                month_dir = year_dir / f"{post_date.month:02d}"
            else:
                month_dir = config.IMAGES_DIR / "unknown"

            month_dir.mkdir(parents=True, exist_ok=True)
            image_path = month_dir / filename
            
            # Save and convert image
            with open(image_path.with_suffix('.tmp'), 'wb') as f:
                f.write(response.content)
                
            # Convert to PNG if enabled
            if config.CONVERT_TO_PNG:
                try:
                    with Image.open(image_path.with_suffix('.tmp')) as img:
                        # Optimize if needed
                        if config.OPTIMIZE_IMAGES:
                            img.thumbnail(config.MAX_IMAGE_SIZE, Image.Resampling.LANCZOS)
                            
                        # Convert to RGB if necessary (for PNG)
                        if img.mode in ('RGBA', 'LA', 'P'):
                            img = img.convert('RGB')
                            
                        img.save(image_path, 'PNG', optimize=True)
                        
                    # Remove temporary file
                    image_path.with_suffix('.tmp').unlink()
                    
                    self.images_downloaded += 1
                    self.logger.info(f"Downloaded and converted image: {filename}")
                    return image_path
                    
                except Exception as e:
                    self.logger.error(f"Error converting image {img_url}: {e}")
                    # Keep original if conversion fails
                    image_path.with_suffix('.tmp').rename(image_path.with_suffix('.jpg'))
                    return image_path.with_suffix('.jpg')
            else:
                image_path.with_suffix('.tmp').rename(image_path)
                return image_path
                
        except Exception as e:
            self.logger.error(f"Error downloading image {img_url}: {e}")
            return None
            
    def parse_date(self, date_str: str) -> Optional[datetime]:
        """Parse date string to datetime object"""
        if not date_str:
            return None
            
        # Try different date formats
        formats = [
            "%B %d, %Y",  # "July 04, 2025"
            "%Y-%m-%d",   # "2025-07-04"
            "%m/%d/%Y",   # "07/04/2025"
        ]
        
        for fmt in formats:
            try:
                return datetime.strptime(date_str, fmt)
            except ValueError:
                continue
                
        self.logger.warning(f"Could not parse date: {date_str}")
        return None

    def extract_year_from_post(self, post: Dict) -> Optional[int]:
        """Extract year from post URL or date"""
        # Try to extract year from URL first (most reliable)
        url = post.get('url', '')
        if url:
            # Look for year pattern in URL like /2018/04/post-title.html
            year_match = re.search(r'/(\d{4})/', url)
            if year_match:
                return int(year_match.group(1))

        # Fallback to parsing date
        post_date = self.parse_date(post.get('date', ''))
        if post_date:
            return post_date.year

        return None

    def save_post(self, post: Dict, content: str) -> Optional[Path]:
        """Save post content to file"""
        try:
            # Extract year from post URL or date
            post_year = self.extract_year_from_post(post)

            # Create filename
            post_date = self.parse_date(post.get('date', ''))
            if post_date:
                year = post_date.year
                month = post_date.month
                day = post_date.day
            else:
                # Use current date as fallback
                now = datetime.now()
                year, month, day = now.year, now.month, now.day

            # Create safe filename from title
            title_slug = re.sub(r'[^\w\s-]', '', post['title']).strip()
            title_slug = re.sub(r'[-\s]+', '-', title_slug)[:50]  # Limit length

            filename = config.FILENAME_PATTERNS["post"].format(
                year=year, month=month, day=day, title_slug=title_slug
            )

            # Create directory structure based on organization preference
            if config.ORGANIZE_BY_YEAR and post_year:
                # Organize by year only
                year_dir = config.ARCHIVE_DIR / str(post_year)
                year_dir.mkdir(parents=True, exist_ok=True)
                post_path = year_dir / filename
            else:
                # Traditional year/month organization
                year_dir = config.ARCHIVE_DIR / str(year)
                month_dir = year_dir / f"{month:02d}"
                month_dir.mkdir(parents=True, exist_ok=True)
                post_path = month_dir / filename

            # Create modern HTML template
            html_content = f"""<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{post['title']}</title>
    <style>
        * {{
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }}

        body {{
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #fafafa;
            padding: 20px;
        }}

        .container {{
            max-width: 900px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }}

        .post-header {{
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px;
            text-align: center;
        }}

        .post-title {{
            font-size: 2.2em;
            font-weight: 700;
            margin-bottom: 15px;
            line-height: 1.2;
        }}

        .post-meta {{
            font-size: 0.95em;
            opacity: 0.9;
            display: flex;
            justify-content: center;
            gap: 20px;
            flex-wrap: wrap;
        }}

        .post-meta a {{
            color: #fff;
            text-decoration: underline;
        }}

        .post-content {{
            padding: 40px;
            font-size: 1.1em;
            line-height: 1.8;
        }}

        /* Font normalization - override all inline styles */
        .post-content * {{
            font-family: inherit !important;
            font-size: inherit !important;
            line-height: inherit !important;
        }}

        .post-content p {{
            margin-bottom: 1.2em;
            font-size: 1.1em !important;
        }}

        .post-content div {{
            font-size: 1.1em !important;
        }}

        .post-content span {{
            font-size: inherit !important;
        }}

        .post-content img {{
            max-width: 100%;
            height: auto;
            border-radius: 8px;
            margin: 20px 0;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
        }}

        .post-content h1, .post-content h2, .post-content h3 {{
            margin: 1.5em 0 0.8em 0;
            color: #2c3e50;
            font-size: 1.4em !important;
            font-weight: 600 !important;
        }}

        .post-content strong, .post-content b {{
            color: #2c3e50;
            font-weight: 600 !important;
        }}

        .post-content em, .post-content i {{
            color: #555;
            font-style: italic !important;
        }}

        .separator {{
            margin: 2em 0;
        }}

        .back-link {{
            position: fixed;
            top: 20px;
            left: 20px;
            background: #667eea;
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: 500;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;
        }}

        .back-link:hover {{
            background: #5a6fd8;
            transform: translateY(-2px);
        }}

        @media (max-width: 768px) {{
            .container {{
                margin: 10px;
                border-radius: 8px;
            }}

            .post-header {{
                padding: 30px 20px;
            }}

            .post-title {{
                font-size: 1.8em;
            }}

            .post-content {{
                padding: 30px 20px;
            }}

            .post-meta {{
                flex-direction: column;
                gap: 10px;
            }}
        }}
    </style>
</head>
<body>
    <a href="../../../index.html" class="back-link">← Back to Index</a>

    <div class="container">
        <div class="post-header">
            <h1 class="post-title">{post['title']}</h1>
            <div class="post-meta">
                <span>By: {post['author']}</span>
                <span>Date: {post['date']}</span>
                <span><a href="{post['url']}" target="_blank">View Original</a></span>
            </div>
        </div>
        <div class="post-content">
            {content}
        </div>
    </div>
</body>
</html>"""

            with open(post_path, 'w', encoding='utf-8') as f:
                f.write(html_content)

            # Store post info for index
            self.all_posts_info.append({
                'title': post['title'],
                'author': post['author'],
                'date': post['date'],
                'url': post['url'],
                'local_path': post_path,
                'relative_path': f"archive/{year}/{month:02d}/{filename}"
            })

            self.posts_downloaded += 1
            self.logger.info(f"Saved post: {filename}")
            return post_path

        except Exception as e:
            self.logger.error(f"Error saving post {post['title']}: {e}")
            return None

    def discover_archive_urls(self):
        """Discover all archive URLs from the blog"""
        archive_urls = []

        # Start with main page
        main_page = self.make_request(config.BASE_URL)
        if main_page:
            soup = BeautifulSoup(main_page.text, 'html.parser')

            # Find archive links
            archive_links = soup.find_all('a', href=True)
            for link in archive_links:
                href = link.get('href', '')
                if 'archive.html' in href and config.BASE_URL in href:
                    archive_urls.append(href)

            # Also check for weekly archive pattern
            for year in range(2023, 2026):  # Adjust range as needed
                for month in range(1, 13):
                    for week in range(1, 5):
                        week_start = (week - 1) * 7 + 1
                        archive_url = f"{config.BASE_URL}/{year}_{month:02d}_{week_start:02d}_archive.html"
                        archive_urls.append(archive_url)

        # Remove duplicates and sort
        archive_urls = list(set(archive_urls))
        archive_urls.sort()

        return archive_urls

    def is_post_downloaded(self, post_url):
        """Check if a post has already been downloaded"""
        cursor = self.conn.cursor()
        cursor.execute("SELECT id FROM posts WHERE url = ?", (post_url,))
        return cursor.fetchone() is not None

    def run_test_crawl(self):
        """Run a test crawl with limited scope"""
        self.logger.info("Starting test crawl...")

        try:
            # Discover archive URLs
            archive_urls = self.discover_archive_urls()

            all_posts = []
            posts_processed = 0

            # Extract posts from each archive page
            for url in archive_urls:
                if config.TEST_MODE and posts_processed >= config.TEST_MAX_POSTS:
                    break

                posts = self.extract_posts_from_page(url)
                all_posts.extend(posts)
                posts_processed += len(posts)

            # Limit posts for testing
            if config.TEST_MODE:
                all_posts = all_posts[:config.TEST_MAX_POSTS]

            self.logger.info(f"Found {len(all_posts)} posts to process")

            # Process each post
            for i, post in enumerate(all_posts, 1):
                self.logger.info(f"Processing post {i}/{len(all_posts)}: {post['title']}")

                try:
                    # Download and save post content (download_post_content handles saving internally)
                    success = self.download_post_content(post)
                    if success:
                        self.logger.info(f"Successfully processed: {post['title']}")
                    else:
                        self.logger.error(f"Failed to process: {post['title']}")
                except Exception as e:
                    self.logger.error(f"Error processing post {post['title']}: {e}")
                    continue

            # Generate summary and index
            self.generate_summary()
            self.generate_index()

        except Exception as e:
            self.logger.error(f"Error during crawl: {e}")
            raise

    def generate_summary(self):
        """Generate crawl summary"""
        summary = {
            "crawl_date": datetime.now().isoformat(),
            "posts_downloaded": self.posts_downloaded,
            "images_downloaded": self.images_downloaded,
            "output_directory": str(config.OUTPUT_DIR),
            "test_mode": config.TEST_MODE
        }

        summary_path = config.METADATA_DIR / "crawl_summary.json"
        with open(summary_path, 'w') as f:
            json.dump(summary, f, indent=2)

        self.logger.info(f"Crawl completed! Posts: {self.posts_downloaded}, Images: {self.images_downloaded}")
        self.logger.info(f"Summary saved to: {summary_path}")

    def generate_index(self):
        """Generate main index.html file with year-wise organization"""
        # Group posts by year
        posts_by_year = {}
        for post in self.all_posts_info:
            year = self.extract_year_from_post(post)
            if year:
                if year not in posts_by_year:
                    posts_by_year[year] = []
                posts_by_year[year].append(post)

        # Sort years in descending order (latest first)
        sorted_years = sorted(posts_by_year.keys(), reverse=True)

        # Generate year-wise post list HTML
        posts_html = ""
        total_posts = 0

        # Year summary section
        year_summary_html = ""
        for year in sorted_years:
            year_posts = posts_by_year[year]
            year_count = len(year_posts)
            total_posts += year_count
            year_summary_html += f"""
            <div class="year-summary">
                <span class="year-label">{year}</span>
                <span class="year-count">{year_count} posts</span>
            </div>"""

        # Detailed posts by year
        for year in sorted_years:
            year_posts = sorted(posts_by_year[year], key=lambda x: x['title'].lower())
            posts_html += f"""
            <div class="year-section">
                <h2 class="year-header">{year} ({len(year_posts)} posts)</h2>
                <div class="posts-grid">"""

            for i, post in enumerate(year_posts, 1):
                # Clean title for display
                display_title = post['title'].replace(' => ', ' - ')
                posts_html += f"""
                <div class="post-item">
                    <div class="post-number">{i:02d}</div>
                    <div class="post-details">
                        <h3><a href="{post['relative_path']}">{display_title}</a></h3>
                        <div class="post-meta">
                            <span class="author">By: {post['author']}</span>
                            <span class="date">{post['date']}</span>
                        </div>
                    </div>
                </div>"""

            posts_html += """
                </div>
            </div>"""

        index_html = f"""<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gerry's Diamond Setting Essays - Complete Archive</title>
    <style>
        * {{
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }}

        body {{
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }}

        .header {{
            text-align: center;
            padding: 60px 20px;
            color: white;
        }}

        .header h1 {{
            font-size: 3em;
            font-weight: 700;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }}

        .header p {{
            font-size: 1.2em;
            opacity: 0.9;
            max-width: 600px;
            margin: 0 auto;
        }}

        .stats {{
            display: flex;
            justify-content: center;
            gap: 40px;
            margin-top: 30px;
            flex-wrap: wrap;
        }}

        .stat {{
            text-align: center;
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 10px;
            backdrop-filter: blur(10px);
        }}

        .stat-number {{
            font-size: 2em;
            font-weight: bold;
            display: block;
        }}

        .stat-label {{
            font-size: 0.9em;
            opacity: 0.8;
        }}

        .container {{
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px 20px 0 0;
            min-height: 60vh;
            box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);
        }}

        .search-bar {{
            padding: 30px;
            border-bottom: 1px solid #eee;
            text-align: center;
        }}

        .search-input {{
            width: 100%;
            max-width: 500px;
            padding: 15px 20px;
            font-size: 1.1em;
            border: 2px solid #ddd;
            border-radius: 25px;
            outline: none;
            transition: border-color 0.3s ease;
        }}

        .search-input:focus {{
            border-color: #667eea;
        }}

        .year-summary-section {{
            padding: 30px;
            background: white;
            margin-bottom: 20px;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }}

        .year-summary-section h2 {{
            color: #2c3e50;
            margin-bottom: 20px;
            text-align: center;
        }}

        .year-summary-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
        }}

        .year-summary {{
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 8px;
            font-weight: 600;
        }}

        .year-label {{
            font-size: 1.2em;
        }}

        .year-count {{
            font-size: 0.9em;
            opacity: 0.9;
        }}

        .posts-container {{
            padding: 30px;
        }}

        .year-section {{
            margin-bottom: 40px;
        }}

        .year-header {{
            color: #2c3e50;
            font-size: 1.8em;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 3px solid #667eea;
        }}

        .posts-grid {{
            display: grid;
            gap: 20px;
        }}

        .post-item {{
            display: flex;
            align-items: center;
            padding: 20px;
            border: 1px solid #eee;
            border-radius: 12px;
            transition: all 0.3s ease;
            background: #fafafa;
        }}

        .post-item:hover {{
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            border-color: #667eea;
        }}

        .post-number {{
            font-size: 1.2em;
            font-weight: bold;
            color: #667eea;
            margin-right: 20px;
            min-width: 40px;
        }}

        .post-details {{
            flex: 1;
        }}

        .post-details h3 {{
            margin-bottom: 8px;
        }}

        .post-details h3 a {{
            color: #2c3e50;
            text-decoration: none;
            font-weight: 600;
            font-size: 1.1em;
        }}

        .post-details h3 a:hover {{
            color: #667eea;
        }}

        .post-meta {{
            display: flex;
            gap: 20px;
            font-size: 0.9em;
            color: #666;
            flex-wrap: wrap;
        }}

        .footer {{
            text-align: center;
            padding: 40px;
            color: #666;
            border-top: 1px solid #eee;
        }}

        @media (max-width: 768px) {{
            .header h1 {{
                font-size: 2.2em;
            }}

            .stats {{
                gap: 20px;
            }}

            .stat {{
                padding: 15px;
            }}

            .container {{
                margin: 10px;
                border-radius: 15px;
            }}

            .post-item {{
                flex-direction: column;
                align-items: flex-start;
                text-align: left;
            }}

            .post-number {{
                margin-bottom: 10px;
            }}

            .post-meta {{
                flex-direction: column;
                gap: 5px;
            }}
        }}
    </style>
</head>
<body>
    <div class="header">
        <h1>Gerry's Diamond Setting Essays</h1>
        <p>Complete offline archive of professional diamond setting tutorials and techniques</p>

        <div class="stats">
            <div class="stat">
                <span class="stat-number">{total_posts}</span>
                <span class="stat-label">Essays</span>
            </div>
            <div class="stat">
                <span class="stat-number">{self.images_downloaded}</span>
                <span class="stat-label">Images</span>
            </div>
            <div class="stat">
                <span class="stat-number">{len(sorted_years)}</span>
                <span class="stat-label">Years</span>
            </div>
        </div>
    </div>

    <div class="container">
        <div class="year-summary-section">
            <h2>Posts by Year</h2>
            <div class="year-summary-grid">
                {year_summary_html}
            </div>
        </div>

        <div class="search-bar">
            <input type="text" class="search-input" placeholder="Search essays by title..." id="searchInput">
        </div>

        <div class="posts-container" id="postsContainer">
            {posts_html}
        </div>

        <div class="footer">
            <p>Archive generated on {datetime.now().strftime('%B %d, %Y')}</p>
            <p>Original blog: <a href="https://gerrysdiamondsettingessays.blogspot.com" target="_blank">gerrysdiamondsettingessays.blogspot.com</a></p>
        </div>
    </div>

    <script>
        // Simple search functionality
        document.getElementById('searchInput').addEventListener('input', function(e) {{
            const searchTerm = e.target.value.toLowerCase();
            const posts = document.querySelectorAll('.post-item');

            posts.forEach(post => {{
                const title = post.querySelector('h3 a').textContent.toLowerCase();
                if (title.includes(searchTerm)) {{
                    post.style.display = 'flex';
                }} else {{
                    post.style.display = 'none';
                }}
            }});
        }});
    </script>
</body>
</html>"""

        index_path = config.OUTPUT_DIR / "index.html"
        with open(index_path, 'w', encoding='utf-8') as f:
            f.write(index_html)

        self.logger.info(f"Generated index with {total_posts} posts: {index_path}")

    def __del__(self):
        """Cleanup database connection"""
        if hasattr(self, 'conn'):
            self.conn.close()
