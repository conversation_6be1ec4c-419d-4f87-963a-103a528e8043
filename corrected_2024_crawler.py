#!/usr/bin/env python3
"""
Corrected 2024 Crawler

The previous script was extracting dates from ALL years mixed together.
This script will ONLY extract dates that belong to 2024 specifically.

From the sidebar text, we can see:
- 2024 (227) <- This is the correct total
- Then specific 2024 dates like 12/29 (2), 12/22 (4), etc.

We need to parse ONLY the dates that come after "2024 (227)" and before the next year.
"""

import requests
import json
import re
import time
from pathlib import Path
from typing import List, Dict, Tuple
from datetime import datetime
from bs4 import BeautifulSoup
from urllib.parse import urljoin

import config

class Corrected2024Crawler:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        self.date_archives_2024 = {}  # Only 2024 dates
        
    def extract_2024_dates_only(self) -> Dict[str, Dict]:
        """Extract ONLY dates that belong to 2024"""
        print("📊 Extracting 2024 dates ONLY from sidebar...")
        
        try:
            response = self.session.get(config.BASE_URL, timeout=30)
            if response.status_code != 200:
                print(f"❌ HTTP {response.status_code}")
                return {}
            
            soup = BeautifulSoup(response.content, 'html.parser')
            archive_section = soup.select_one('#BlogArchive1')
            
            if not archive_section:
                print("❌ Archive section not found")
                return {}
            
            # Get the full text content
            archive_text = archive_section.get_text()
            
            # Find the 2024 section
            # Look for "2024" followed by "(227)" then extract dates until next year
            lines = archive_text.split('\n')
            
            in_2024_section = False
            date_archives = {}
            
            for i, line in enumerate(lines):
                line = line.strip()
                
                # Check if we're entering 2024 section
                if line == "2024":
                    # Check if next non-empty line contains (227)
                    for j in range(i+1, min(i+5, len(lines))):
                        next_line = lines[j].strip()
                        if next_line == "(227)":
                            in_2024_section = True
                            print("✅ Found 2024 section with (227) articles")
                            break
                    continue
                
                # Check if we're leaving 2024 section (next year found)
                if in_2024_section and re.match(r'^\d{4}$', line):
                    if line != "2024":  # Different year
                        print(f"📅 Exiting 2024 section at year: {line}")
                        break
                
                # If we're in 2024 section, look for date patterns
                if in_2024_section:
                    # Look for MM/DD pattern
                    date_match = re.match(r'^(\d{1,2})/(\d{1,2})$', line)
                    if date_match:
                        month = int(date_match.group(1))
                        day = int(date_match.group(2))
                        
                        # Look for count in next few lines
                        count = 1  # Default
                        for j in range(i+1, min(i+5, len(lines))):
                            count_line = lines[j].strip()
                            count_match = re.match(r'^\((\d+)\)$', count_line)
                            if count_match:
                                count = int(count_match.group(1))
                                break
                        
                        date_key = f"{month:02d}/{day:02d}"
                        archive_url = f"https://gerrysdiamondsettingessays.blogspot.com/2024_{month:02d}_{day:02d}_archive.html"
                        
                        date_archives[date_key] = {
                            'url': archive_url,
                            'expected_count': count
                        }
                        
                        print(f"   📅 {date_key}: {count} articles")
            
            total_expected = sum(info['expected_count'] for info in date_archives.values())
            print(f"✅ Found {len(date_archives)} 2024 dates with {total_expected} total expected articles")
            
            if total_expected == 227:
                print("🎉 Perfect match! Total equals 227 as expected")
            else:
                print(f"⚠️  Count mismatch: Found {total_expected}, expected 227")
            
            return date_archives
            
        except Exception as e:
            print(f"❌ Error extracting 2024 dates: {e}")
            return {}
    
    def crawl_2024_date_archive(self, date_key: str, archive_info: Dict) -> List[Dict]:
        """Crawl a specific 2024 date archive URL"""
        url = archive_info['url']
        expected_count = archive_info['expected_count']
        
        print(f"📅 Crawling 2024/{date_key} - Expected: {expected_count} articles")
        
        articles = []
        
        try:
            response = self.session.get(url, timeout=20)
            
            if response.status_code == 200:
                soup = BeautifulSoup(response.content, 'html.parser')
                post_containers = soup.select(config.SELECTORS["post_container"])
                
                for i, container in enumerate(post_containers, 1):
                    try:
                        title_elem = container.select_one(config.SELECTORS["post_title"])
                        if title_elem and title_elem.get('href'):
                            article_url = title_elem.get('href')
                            title = title_elem.get_text(strip=True)
                            
                            # STRICT 2024 verification
                            year_match = re.search(r'/(\d{4})/', article_url)
                            if year_match and int(year_match.group(1)) == 2024:
                                article = {
                                    'url': article_url,
                                    'title': title,
                                    'date': date_key,
                                    'source': '2024_date_archive'
                                }
                                articles.append(article)
                                print(f"      ✅ {i:2d}: {title[:50]}...")
                    except Exception as e:
                        continue
                
                found_count = len(articles)
                
                if found_count == expected_count:
                    status = "✅ PERFECT"
                elif found_count > expected_count:
                    status = f"➕ EXTRA {found_count - expected_count}"
                else:
                    status = f"⚠️  MISSING {expected_count - found_count}"
                
                print(f"   📊 Result: {found_count}/{expected_count} articles - {status}")
                
            else:
                print(f"   ❌ HTTP {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ Error: {e}")
        
        time.sleep(0.3)
        return articles
    
    def run_corrected_2024_crawl(self) -> Dict:
        """Run corrected 2024-only crawl"""
        print("=" * 80)
        print("📅 CORRECTED 2024 CRAWLER")
        print("=" * 80)
        print("🎯 Goal: Extract ONLY 2024 dates and get exactly 227 articles")
        print()
        
        # Step 1: Extract 2024 dates only
        self.date_archives_2024 = self.extract_2024_dates_only()
        
        if not self.date_archives_2024:
            print("❌ Could not extract 2024 dates from sidebar")
            return {}
        
        total_expected = sum(info['expected_count'] for info in self.date_archives_2024.values())
        
        if total_expected != 227:
            print(f"⚠️  WARNING: Expected total {total_expected} != 227")
            print("This suggests the parsing is still not correct")
        
        # Step 2: Crawl each 2024 date archive
        all_articles = []
        dates_perfect = 0
        dates_missing = 0
        dates_extra = 0
        
        # Sort dates in reverse chronological order (latest first)
        sorted_dates = sorted(self.date_archives_2024.keys(), 
                            key=lambda x: (int(x.split('/')[0]), int(x.split('/')[1])), 
                            reverse=True)
        
        for date_key in sorted_dates:
            archive_info = self.date_archives_2024[date_key]
            articles = self.crawl_2024_date_archive(date_key, archive_info)
            
            found_count = len(articles)
            expected_count = archive_info['expected_count']
            
            if found_count == expected_count:
                dates_perfect += 1
            elif found_count > expected_count:
                dates_extra += 1
            else:
                dates_missing += 1
            
            all_articles.extend(articles)
        
        # Step 3: Remove duplicates
        unique_articles = []
        seen_urls = set()
        
        for article in all_articles:
            if article['url'] not in seen_urls:
                unique_articles.append(article)
                seen_urls.add(article['url'])
        
        duplicates_removed = len(all_articles) - len(unique_articles)
        
        # Step 4: Summary
        total_found = len(unique_articles)
        
        print("\n" + "=" * 80)
        print("📊 CORRECTED 2024 CRAWL SUMMARY")
        print("=" * 80)
        print(f"📅 Total 2024 dates processed: {len(self.date_archives_2024)}")
        print(f"📊 Expected articles: {total_expected}")
        print(f"📊 Found articles: {total_found}")
        print(f"🔄 Duplicates removed: {duplicates_removed}")
        print(f"✅ Perfect dates: {dates_perfect}")
        print(f"➕ Extra dates: {dates_extra}")
        print(f"⚠️  Missing dates: {dates_missing}")
        
        if total_found >= 227:
            print("🎉 SUCCESS: Found at least 227 articles for 2024!")
        else:
            print(f"⚠️  STILL MISSING: {227 - total_found} articles")
        
        # Step 5: Save results
        results = {
            'crawl_date': datetime.now().isoformat(),
            'target_year': 2024,
            'total_expected': total_expected,
            'total_found': total_found,
            'duplicates_removed': duplicates_removed,
            'dates_perfect': dates_perfect,
            'dates_missing': dates_missing,
            'dates_extra': dates_extra,
            'date_archives_2024': self.date_archives_2024,
            'all_articles': unique_articles
        }
        
        output_path = Path(config.OUTPUT_DIR) / "metadata" / f"corrected_2024_crawl.json"
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        
        print(f"\n📋 Results saved to: {output_path}")
        
        return results

def main():
    crawler = Corrected2024Crawler()
    results = crawler.run_corrected_2024_crawl()
    
    if results:
        print(f"\n🎯 FINAL SUMMARY:")
        print(f"   📊 Found {results['total_found']} articles")
        print(f"   📅 Processed {len(results['date_archives_2024'])} 2024 dates")
        
        if results['total_found'] >= 227:
            print("   🎉 SUCCESS: Ready for comparison with local files!")
        else:
            print("   🔍 Still need to investigate missing articles")

if __name__ == "__main__":
    main()
