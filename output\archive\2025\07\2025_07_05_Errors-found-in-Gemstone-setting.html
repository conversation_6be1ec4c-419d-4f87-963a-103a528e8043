<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>"Errors found in Gemstone setting."</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #fafafa;
            padding: 20px;
        }

        .container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .post-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px;
            text-align: center;
        }

        .post-title {
            font-size: 2.2em;
            font-weight: 700;
            margin-bottom: 15px;
            line-height: 1.2;
        }

        .post-meta {
            font-size: 0.95em;
            opacity: 0.9;
            display: flex;
            justify-content: center;
            gap: 20px;
            flex-wrap: wrap;
        }

        .post-meta a {
            color: #fff;
            text-decoration: underline;
        }

        .post-content {
            padding: 40px;
            font-size: 1.1em;
            line-height: 1.8;
        }

        .post-content img {
            max-width: 100%;
            height: auto;
            border-radius: 8px;
            margin: 20px 0;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
        }

        .post-content p {
            margin-bottom: 1.2em;
        }

        .post-content h1, .post-content h2, .post-content h3 {
            margin: 1.5em 0 0.8em 0;
            color: #2c3e50;
        }

        .post-content strong, .post-content b {
            color: #2c3e50;
            font-weight: 600;
        }

        .post-content em, .post-content i {
            color: #555;
        }

        .separator {
            margin: 2em 0;
        }

        .back-link {
            position: fixed;
            top: 20px;
            left: 20px;
            background: #667eea;
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: 500;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;
        }

        .back-link:hover {
            background: #5a6fd8;
            transform: translateY(-2px);
        }

        @media (max-width: 768px) {
            .container {
                margin: 10px;
                border-radius: 8px;
            }

            .post-header {
                padding: 30px 20px;
            }

            .post-title {
                font-size: 1.8em;
            }

            .post-content {
                padding: 30px 20px;
            }

            .post-meta {
                flex-direction: column;
                gap: 10px;
            }
        }
    </style>
</head>
<body>
    <a href="../../../index.html" class="back-link">← Back to Index</a>

    <div class="container">
        <div class="post-header">
            <h1 class="post-title">"Errors found in Gemstone setting."</h1>
            <div class="post-meta">
                <span>By: Posted byGerry Lewy</span>
                <span>Date: 2018-09-04T16:21:00-04:00</span>
                <span><a href="https://gerrysdiamondsettingessays.blogspot.com/2018/09/errors-found-in-gemstone-setting.html" target="_blank">View Original</a></span>
            </div>
        </div>
        <div class="post-content">
            <div class="post-body entry-content" id="post-body-5006875216827271343" itemprop="description articleBody">
<div dir="ltr" style="text-align: left;" trbidi="on">
<i><b> </b></i> <i><b>In these "Series of Failed Setting Techniques"</b></i>, I've attempted to make some 'setting-mistakes' and these results have been now photographed.<br/>
<br/>
<b><i> I'm going to scrap these few items and have the silver refined.</i></b> Why? The 'used' silver tends to develop pin-holes just in areas that you don't want any..(<b><i>experience speaking here</i>!)</b><br/>
<br/>
 <b><i>I bought an upscale book on jewellery designs</i></b> and went through the pages, specifically looking at their Diamond Setting photographs. OUCH, these pictures stirred me to take a few photographs to show you! So what went so wrong?<b><i> Literally, everything went wrong,</i> </b>is this<b> </b>possible?<br/>
<br/>
 <b><i>The setter neglected &amp; avoided 'Burnishing the multitude of Beads'</i></b>. I know how he separated the bead-bar in between the stones. He used an old roughy shaped Flat-graver to 'split &amp; push' over the four (4) beads.<br/>
<br/>
 <b><i>The setter, with no respect to being careful, avoided improving the flat graver quality</i></b>. He just dug in with his graver and no care was given on who would be looking at his workmanship afterwards! <br/>
<br/>
You can observe all of the stones were set FIRST! <b><i>I stopped using this method over 35 years ago!</i></b><br/>
<b><i>Now what I do is to cut the metal pattern FIRST, then Bright-Cut.</i></b> Thus leaving a nice clean edge around the stones' girdle. What this setter did was also not bother to how the stones will be seating and many of them are literally all over the place.<i><b> Not two of them are basically 'in-line'. </b></i><br/>
<i><b>Is this a testament to our professional ability &amp; attitude?</b></i><br/>
<br/>
 <i><b>The amazing part of these two following photo's is that the 'upscale jeweller-owner' hadn't noticed anything wrong, pity! </b>Sorry, I'm not permitted to divulge the name of this company &amp; I don't like to have lawyers calling me..:&gt;( </i><br/>
<br/>
<div>
<b><i>To improve on this after I would have done the Diamond Setting</i></b>, I would use a #006 bud bur to clear up any unsightly marks in between the beads. I would avoid any stone setting until the lines around the diamond hole are clean of any little lines from the graver cutting!<br/>
<br/>
<b>Can you see that all of the diamonds that were set are 'not in line'! How obvious is this great mistake?</b></div>
<div class="separator" style="clear: both; text-align: center;">
<a href="https://blogger.googleusercontent.com/img/b/R29vZ2xl/AVvXsEgcHz8srqgYdeg34QigDGftkkp_5FqJRDMXp6eMWQ04XZCeQlHEdcuRJo1rqYq67UFfUNaiv0Ow4P_7A0QVbZNJaJdtDXfblcpOL7vmCJimIEk4IFSDUvvsfhXjCsyVm6SsbGdxKyrpn6c/s1600/b%25231.jpg" style="margin-left: 1em; margin-right: 1em;"><img border="0" data-original-height="640" data-original-width="480" height="640" loading="lazy" src="../../../images/unknown/Errors-found-in-Gemstone-setting._img_001.png" width="480"/></a></div>
<br/>
<i>There was no hint of this setter using his ''Bead-Burnisher" to just nicely 'round-off the beads' into little round balls.</i> <b><i>Out of a Scale of 10 for being a good setter, I'd give him a definite 4..(maybe)!</i></b><br/>
<b><i> BTW,</i></b> <i><b>Where is the "Quality Control" manager in this setting project? </b></i><br/>
<div class="separator" style="clear: both; text-align: center;">
<a href="https://blogger.googleusercontent.com/img/b/R29vZ2xl/AVvXsEjlOMF-6KBGqrcFT1i2bpjxXB4To8I7gHoXcNpZMfaUOcdbHC1wFq6t0kFOPC6GopAwy-qTUmqYjSeMg-2h74ToYtmeDZ408-Q8nml7K1VdrPRJt4_Gn5jJkQFD6ZE7VaiklD6IgRgKd3M/s1600/b%25232.jpg" style="margin-left: 1em; margin-right: 1em;"><img border="0" data-original-height="640" data-original-width="480" height="640" loading="lazy" src="../../../images/unknown/Errors-found-in-Gemstone-setting._img_002.png" width="480"/></a></div>
<b><i><br/></i></b>
<b><i><br/></i></b>
<b><i>As you can see in these two photo's </i></b>is that there is no angled faceting for the Pavilion of the Princess-cut stone. The little angles are not in line and the stone won't be sitting correctly, if at all securely!<br/>
<div class="separator" style="clear: both; text-align: center;">
<a href="https://blogger.googleusercontent.com/img/b/R29vZ2xl/AVvXsEgqGYGpPuy3owu8NkEZKzkuG3GKGDiiHGJ7z7yNtUtDbRMky_UVfRUAwheB4PQ31pjEozSWctgU8pTOdFfu4xabAyE3OJWhqQ4wgzBYGJz0A3pLKuYuPT59B2Y07uTyOU33lksdSf7Ux3Y/s1600/nfg%25232.jpg" style="margin-left: 1em; margin-right: 1em;"><img border="0" data-original-height="640" data-original-width="480" height="640" loading="lazy" src="../../../images/unknown/Errors-found-in-Gemstone-setting._img_003.png" width="480"/></a></div>
<br/>
 <b style="font-style: italic;">I saw these 'mistakes' </b>and I want you to understand just how important it is to use your 10x magnification of your loupe. If the setter caught these problems, he would have made a better attempt &amp; no problems would be noted here, trust me!<br/>
<div class="separator" style="clear: both; text-align: center;">
<a href="https://blogger.googleusercontent.com/img/b/R29vZ2xl/AVvXsEgEOjO_lUzrtEEOrJ8YCUhSCmPIYH6_sTj0zb03CFquoSAGbe6OhZi-0qimyyflpxeocSlCOHWAFZDoLF0zfKduLF7E1Vk3J8quTWeQnAhLUtP1MWm_pmJ41pPmgMJxY96-KYnhUj-PL80/s1600/princess+nfg.jpg" style="margin-left: 1em; margin-right: 1em;"><img border="0" data-original-height="640" data-original-width="480" height="640" loading="lazy" src="../../../images/unknown/Errors-found-in-Gemstone-setting._img_004.png" width="480"/></a></div>
<br/>
<b><i>What do we have here? </i></b>We have a girdle of the stone that is not sitting anywhere near the 'bearing' for the stone. The girdle just doesn't even match where the 'bearing cut' was made, again no angled-cutting for the Pavillion facets. Is this stone going to be sitting in this ring for long-term? Hardly or not at all!<br/>
<div class="separator" style="clear: both; text-align: center;">
<a href="https://blogger.googleusercontent.com/img/b/R29vZ2xl/AVvXsEhr94T-1LWLumEP9MuehBlvta7lxLEX78PsU8old5VWpIJCXQzgwvW1_kxSSpMXA3b0LfKwldqjiCJazQ6oEB8BOtJYyOfn9SX1xXDUL-iEafejfK3qmBg3BeSdM3vc9FluPOZUW04Drtk/s1600/y%25232.jpg" style="margin-left: 1em; margin-right: 1em; text-align: center;"><img border="0" data-original-height="480" data-original-width="640" height="480" loading="lazy" src="../../../images/unknown/Errors-found-in-Gemstone-setting._img_005.png" width="640"/></a></div>
<br/>
<br/>
 <b><i>Can you see that these two stones are overlapping each other</i></b> at the most delicate areas, the girdles!<br/>
The stones were secured with a flat graver carving a sliver of metal to hold the stone. This process is a 'temporary' second-option in holding, but not securing the stones.<br/>
<div class="separator" style="clear: both; text-align: center;">
<a href="https://blogger.googleusercontent.com/img/b/R29vZ2xl/AVvXsEhU-HtqJZEjSnTEgAozK3a-q-ViOacFWKJ7wD2V_0VJ3oBX2U48KTRU07RL_ZONN7UuzrMXVrtZQlETwr-xPtiRz2xiLZN9Djq7P3r2M6zP4qRQSH5TBripHnBhl9fMO0ngYwTxM4eY6vM/s1600/y%25233.jpg" style="margin-left: 1em; margin-right: 1em;"><img border="0" data-original-height="640" data-original-width="480" height="640" loading="lazy" src="../../../images/unknown/Errors-found-in-Gemstone-setting._img_006.png" width="480"/></a></div>
<br/>
<br/>
<b><i>I</i></b><i><b>n this better photograph, you can now see just how 'ugly' the setting now has become.</b></i> All you see are pin-holes 'here &amp; there' just making the setting look like an amateur tried their hands on it, yuk!<br/>
<br/>
<div class="separator" style="clear: both; text-align: center;">
<a href="https://blogger.googleusercontent.com/img/b/R29vZ2xl/AVvXsEh8Zo6xmz9vyHeYLkMyla-s_3SH3upH5IbHT5sDX1MP6uZ1zCAB62nIYpMDH_AGaxIjK1yNDLXFSx8Mm4qOZrJoQeP_E-2yLPL8t16ZUdibWHY-iK1MQfUjoCmxrDej5cs5Q8wV7nloyso/s1600/y%25234.jpg" style="margin-left: 1em; margin-right: 1em;"><img border="0" data-original-height="640" data-original-width="480" height="640" loading="lazy" src="../../../images/unknown/Errors-found-in-Gemstone-setting._img_007.png" width="480"/></a></div>
<div style="clear: both; text-align: center;">
<b><i><br/></i></b></div>
<div style="clear: both; text-align: center;">
<b><i><br/></i></b></div>
<div style="clear: both; text-align: center;">
<b><i>Under 20x power magnification, you can see again just how this stone IS NOT SITTING CORRECTLY!</i></b></div>
<div class="separator" style="clear: both; text-align: center;">
<a href="../../../images/unknown/Errors-found-in-Gemstone-setting._img_008.png" style="margin-left: 1em; margin-right: 1em;"><img border="0" data-original-height="480" data-original-width="640" height="480" loading="lazy" src="../../../images/unknown/Errors-found-in-Gemstone-setting._img_008.png" width="640"/></a></div>
<div class="separator" style="clear: both; text-align: center;">
<i><b><br/></b></i></div>
<div class="separator" style="clear: both; text-align: center;">
<b style="font-style: italic;"><br/></b></div>
<div class="separator" style="clear: both; text-align: center;">
<b style="font-style: italic;">Can you count the many </b><i>(12)</i><b style="font-style: italic;"> little 'wire-beads'</b>...(<i>as I call them</i>) to secure the poorly set stones! </div>
<div class="separator" style="clear: both; text-align: left;">
Only three stones were of the same colour, the stone shown was '2 shades of a darker pink'.</div>
<div class="separator" style="clear: both; text-align: left;">
<br/></div>
<div class="separator" style="clear: both; text-align: left;">
 In the last photograph, you can see just how close to the edge of the bezel, is the girdle of the stone<span style="text-align: center;">! One little attempt of polishing &amp; the girdle would be overlapping the sides of the ring &amp; gallery.</span></div>
<div class="separator" style="clear: both; text-align: left;">
<span style="text-align: center;"><b><i><br/></i></b></span></div>
<div class="separator" style="clear: both; text-align: left;">
<span style="text-align: center;"><b><i>OMG, another two more stones overlapping each other, why is this?</i></b> Here are a few reasons, the stones were picked long after the ring was created. I call this poor process; "<i>Make &amp; Find afterwards</i>". </span><i><b>All of my creations in</b></i><i><b> past</b></i><i><b> years were <u>made after</u> I bought my gemstones.</b></i></div>
<div class="separator" style="clear: both; text-align: left;">
<br/></div>
<div class="separator" style="clear: both; text-align: left;">
 This simple process allows the skilled jeweller, or the "Computer Aided Designer" to create his ring to match my stones.</div>
<div>
<br/></div>
<div class="separator" style="clear: both; text-align: center;">
<a href="https://blogger.googleusercontent.com/img/b/R29vZ2xl/AVvXsEigMIuLmaNbKj_XyBxnRrkytEZqRCCM2uMdMKPielCNsKdUX2gabpZvDEQCBu7Yiz2WEeKu5S-Tag-zqshbvmQoOkd9UByvPQbwXeIKJnyucdaA3XNLfPbndkeFEO8d84muo6acIxgu0a8/s1600/y%25236.jpg" style="margin-left: 1em; margin-right: 1em;"><img border="0" data-original-height="640" data-original-width="480" height="640" loading="lazy" src="../../../images/unknown/Errors-found-in-Gemstone-setting._img_009.png" width="480"/></a></div>
<div class="separator" style="clear: both; text-align: left;">
  I have explored in detail how some setters think that no one will be viewing any of these errors!</div>
<div class="separator" style="clear: both; text-align: left;">
<b><i>Are these mistakes acceptable?</i></b> I have other 'very descriptive words' for the reader, but they are not to be written, I'd just say <b><span style="font-size: medium;">that this ring looks ultra-disgusting!</span></b>..:&gt;(</div>
</div>
<div style="clear: both;"></div>
</div>
        </div>
    </div>
</body>
</html>