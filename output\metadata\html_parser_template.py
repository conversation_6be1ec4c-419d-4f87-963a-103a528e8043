#!/usr/bin/env python3
"""
Complete HTML Parser Template
"""

from bs4 import BeautifulSoup
from typing import List, Dict


def parse_complete_expanded_html(html_content: str) -> List[Dict]:
    """
    Parse the complete expanded sidebar HTML to extract all 227 articles.
    
    Args:
        html_content: The complete expanded sidebar HTML from the browser
    
    Returns:
        List of all article dictionaries
    """
    
    soup = BeautifulSoup(html_content, 'html.parser')
    
    # Find the 2024 section
    # Look for the link that contains "2024" and has post-count "(227)"
    year_2024_section = None
    
    # Find all post-count-links
    post_count_links = soup.select('a.post-count-link')
    
    for link in post_count_links:
        if '2024' in link.get('href', '') and '2024' in link.get_text():
            # Check if the next post-count span shows (227)
            next_span = link.find_next_sibling('span', class_='post-count')
            if next_span and '(227)' in next_span.get_text():
                # This is the 2024 section
                year_2024_section = link.find_parent('li', class_='archivedate')
                break
    
    if not year_2024_section:
        print("❌ Could not find 2024 section with (227) articles")
        return []
    
    print("✅ Found 2024 section with (227) articles")
    
    # Now find all <ul class="posts"> sections within the 2024 section
    posts_sections = year_2024_section.select('ul.posts')
    
    articles = []
    
    for i, posts_section in enumerate(posts_sections, 1):
        # Find the date for this posts section
        date_link = posts_section.find_previous('a', class_='post-count-link')
        date_text = date_link.get_text(strip=True) if date_link else f"unknown_{i}"
        
        # Extract all article links
        article_links = posts_section.select('li a[href]')
        
        print(f"   📅 {date_text}: {len(article_links)} articles")
        
        for link in article_links:
            href = link.get('href')
            title = link.get_text(strip=True)
            
            # Clean up HTML entities
            title = title.replace('&gt;', '>').replace('&lt;', '<').replace('&amp;', '&')
            
            article = {
                'url': href,
                'title': title,
                'archive_date': date_text,
                'source': 'complete_expanded_html'
            }
            articles.append(article)
    
    print(f"\n✅ Total articles extracted: {len(articles)}")
    
    return articles
    