#!/usr/bin/env python3
"""
Full crawl script for Gerry's Diamond Setting Essays Blog
This will download ALL posts from the blog.
"""

import sys
import traceback
from pathlib import Path
import time

# Add current directory to path
sys.path.insert(0, str(Path(__file__).parent))

from crawler import BlogCrawler
import config

def main():
    """Run full crawl of the entire blog"""
    print("=" * 80)
    print("Gerry's Diamond Setting Essays Blog - FULL CRAWL")
    print("=" * 80)
    print()
    
    print("🚀 STARTING FULL IMPLEMENTATION")
    print()
    print(f"📍 Base URL: {config.BASE_URL}")
    print(f"📁 Output Directory: {config.OUTPUT_DIR}")
    print(f"🔧 Test Mode: {config.TEST_MODE}")
    print(f"🖼️  Convert to PNG: {config.CONVERT_TO_PNG}")
    print(f"⚡ Request Delay: {config.REQUEST_DELAY}s")
    print(f"🎯 Max Image Size: {config.MAX_IMAGE_SIZE}")
    print()
    
    if config.TEST_MODE:
        print("⚠️  WARNING: Test mode is still enabled!")
        print("   This will only download a limited number of posts.")
        print("   Set TEST_MODE = False in config.py for full crawl.")
        print()
    
    # Confirm before starting
    response = input("🤔 Are you ready to start the full crawl? (y/N): ")
    if response.lower() != 'y':
        print("❌ Crawl cancelled.")
        return
    
    print()
    print("🎬 Starting full crawl...")
    print("📊 This may take several hours depending on the number of posts.")
    print("💡 You can monitor progress in the log file: output/crawler.log")
    print()
    
    start_time = time.time()
    
    try:
        # Initialize crawler
        print("🔧 Initializing crawler...")
        crawler = BlogCrawler()
        
        # Run full crawl
        print("🚀 Starting crawl process...")
        crawler.run_test_crawl()  # This will run full crawl when TEST_MODE=False
        
        end_time = time.time()
        duration = end_time - start_time
        hours = int(duration // 3600)
        minutes = int((duration % 3600) // 60)
        seconds = int(duration % 60)
        
        print()
        print("=" * 80)
        print("🎉 FULL CRAWL COMPLETED SUCCESSFULLY!")
        print("=" * 80)
        print()
        print(f"⏱️  Total Time: {hours:02d}:{minutes:02d}:{seconds:02d}")
        print(f"📄 Posts Downloaded: {crawler.posts_downloaded}")
        print(f"🖼️  Images Downloaded: {crawler.images_downloaded}")
        print()
        print("📁 Results saved to:")
        print(f"   📖 Index: {config.OUTPUT_DIR}/index.html")
        print(f"   📚 Posts: {config.ARCHIVE_DIR}")
        print(f"   🖼️  Images: {config.IMAGES_DIR}")
        print(f"   📊 Metadata: {config.METADATA_DIR}")
        print(f"   📝 Logs: {config.LOG_FILE}")
        print()
        print("🌐 Open the index.html file in your browser to browse the archive!")
        print()
        
    except KeyboardInterrupt:
        print("\n⏹️  Crawl interrupted by user.")
        print("📊 Partial results may be available in the output directory.")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Error during crawl: {e}")
        print("\n🔍 Full traceback:")
        traceback.print_exc()
        print()
        print("💡 Check the log file for more details: output/crawler.log")
        sys.exit(1)

if __name__ == "__main__":
    main()
