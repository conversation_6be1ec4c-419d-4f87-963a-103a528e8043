#!/usr/bin/env python3
"""
Compare Downloaded vs Extracted Articles

Compare what we have downloaded vs what was extracted from HTML
to create a precise list of missing articles to download.
"""

import json
import os
import re
import sys
from pathlib import Path
from typing import Set, List, Dict
from urllib.parse import urlparse

# Add current directory to path
sys.path.insert(0, str(Path(__file__).parent))

from crawler import BlogCrawler
import config

class ArticleComparator:
    def __init__(self):
        self.crawler = BlogCrawler()
        self.downloaded_articles = set()
        self.extracted_articles = []
        self.missing_articles = []
        
    def get_downloaded_articles(self) -> Set[str]:
        """Get list of all downloaded article URLs from the file system"""
        print("🔍 Scanning downloaded articles...")
        
        downloaded = set()
        archive_dir = Path(config.OUTPUT_DIR) / "archive"
        
        if not archive_dir.exists():
            print(f"❌ Archive directory not found: {archive_dir}")
            return downloaded
        
        # Scan all HTML files in archive directory
        html_files = list(archive_dir.rglob("*.html"))
        print(f"📊 Found {len(html_files)} downloaded HTML files")
        
        for html_file in html_files:
            # Extract the original URL from the filename
            # Files are saved as: YYYY_MM_DD_Title.html
            filename = html_file.stem
            
            # Remove the date prefix (YYYY_MM_DD_)
            if re.match(r'^\d{4}_\d{2}_\d{2}_', filename):
                title_part = re.sub(r'^\d{4}_\d{2}_\d{2}_', '', filename)
                
                # Reconstruct the likely URL
                # Most articles follow pattern: /YYYY/MM/title.html
                year = html_file.parent.name
                
                # Try to find the original URL in the database
                original_url = self.find_original_url_in_db(title_part, year)
                if original_url:
                    downloaded.add(original_url)
                else:
                    # Fallback: create a normalized identifier
                    normalized_id = f"{year}:{title_part}"
                    downloaded.add(normalized_id)
        
        print(f"✅ Identified {len(downloaded)} downloaded articles")
        return downloaded
    
    def find_original_url_in_db(self, title_part: str, year: str) -> str:
        """Try to find the original URL in the database"""
        try:
            import sqlite3
            conn = sqlite3.connect(config.DATABASE_PATH)
            cursor = conn.cursor()
            
            # Search for posts with similar title and year
            cursor.execute("""
                SELECT url FROM posts 
                WHERE url LIKE ? AND url LIKE ?
                LIMIT 1
            """, (f"%{year}%", f"%{title_part.replace('-', '%')}%"))
            
            result = cursor.fetchone()
            conn.close()
            
            if result:
                return result[0]
        except:
            pass
        
        return None
    
    def load_extracted_articles(self) -> List[Dict]:
        """Load the extracted articles from HTML parsing"""
        print("📂 Loading extracted articles...")
        
        extraction_file = Path(config.OUTPUT_DIR) / "metadata" / "complete_html_extraction.json"
        
        if not extraction_file.exists():
            print(f"❌ Extraction file not found: {extraction_file}")
            return []
        
        with open(extraction_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        articles = data.get('all_articles', [])
        print(f"✅ Loaded {len(articles)} extracted articles")
        
        return articles
    
    def normalize_url_for_comparison(self, url: str) -> str:
        """Normalize URL for comparison"""
        # Remove protocol and domain
        parsed = urlparse(url)
        path = parsed.path
        
        # Remove .html extension
        if path.endswith('.html'):
            path = path[:-5]
        
        # Extract year and title
        match = re.match(r'/(\d{4})/\d{2}/(.+)', path)
        if match:
            year, title = match.groups()
            return f"{year}:{title}"
        
        return url
    
    def find_missing_articles(self) -> List[Dict]:
        """Compare extracted vs downloaded to find missing articles"""
        print("\n🔍 Comparing extracted vs downloaded articles...")
        
        # Get downloaded articles
        self.downloaded_articles = self.get_downloaded_articles()
        
        # Get extracted articles
        self.extracted_articles = self.load_extracted_articles()
        
        if not self.extracted_articles:
            print("❌ No extracted articles to compare!")
            return []
        
        # Create normalized sets for comparison
        downloaded_normalized = set()
        for url in self.downloaded_articles:
            if ':' in url:  # Already normalized
                downloaded_normalized.add(url)
            else:
                downloaded_normalized.add(self.normalize_url_for_comparison(url))
        
        missing = []
        
        for article in self.extracted_articles:
            url = article['url']
            normalized = self.normalize_url_for_comparison(url)
            
            # Check if this article is already downloaded
            if normalized not in downloaded_normalized and url not in self.downloaded_articles:
                missing.append(article)
        
        print(f"📊 Comparison Results:")
        print(f"   📥 Downloaded: {len(self.downloaded_articles)}")
        print(f"   📋 Extracted: {len(self.extracted_articles)}")
        print(f"   ❌ Missing: {len(missing)}")
        
        return missing
    
    def save_missing_articles_report(self, missing_articles: List[Dict]):
        """Save detailed report of missing articles"""
        
        # Group by year
        missing_by_year = {}
        for article in missing_articles:
            year = article.get('year', 'unknown')
            if year not in missing_by_year:
                missing_by_year[year] = []
            missing_by_year[year].append(article)
        
        # Create report
        report = {
            'comparison_date': '2025-07-06',
            'total_extracted': len(self.extracted_articles),
            'total_downloaded': len(self.downloaded_articles),
            'total_missing': len(missing_articles),
            'missing_by_year': missing_by_year,
            'missing_articles': missing_articles
        }
        
        # Save report
        output_path = Path(config.OUTPUT_DIR) / "metadata" / "missing_articles_report.json"
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        print(f"\n📋 Missing articles report saved to: {output_path}")
        
        # Print summary
        print("\n" + "=" * 60)
        print("📊 MISSING ARTICLES SUMMARY")
        print("=" * 60)
        
        for year in sorted(missing_by_year.keys(), reverse=True):
            count = len(missing_by_year[year])
            print(f"   📅 {year}: {count} missing articles")
        
        print(f"\n   📊 TOTAL MISSING: {len(missing_articles)} articles")
        
        return report
    
    def download_missing_articles(self, missing_articles: List[Dict]) -> Dict:
        """Download the missing articles"""
        
        if not missing_articles:
            print("✅ No missing articles to download!")
            return {'success': True, 'downloaded': 0}
        
        print(f"\n🎯 Starting download of {len(missing_articles)} missing articles...")
        
        results = {
            'total_attempted': len(missing_articles),
            'successful': 0,
            'failed': 0,
            'skipped': 0
        }
        
        for i, article in enumerate(missing_articles, 1):
            try:
                title_preview = article['title'][:50] + "..." if len(article['title']) > 50 else article['title']
                print(f"   📄 {i:3d}/{len(missing_articles)} - {title_preview}")
                
                # Convert to post format
                post = {
                    'url': article['url'],
                    'title': article['title'],
                    'year': article.get('year', 'unknown'),
                    'source': 'missing_articles_download'
                }
                
                # Check if already downloaded (double-check)
                if self.crawler.is_post_downloaded(post['url']):
                    print(f"      ⏭️  Already downloaded (skipping)")
                    results['skipped'] += 1
                    continue
                
                # Download the post
                success = self.crawler.download_post_content(post)
                
                if success:
                    print(f"      ✅ Downloaded successfully")
                    results['successful'] += 1
                    
                    # Update index every 10 downloads
                    if results['successful'] % 10 == 0:
                        print(f"      🎯 Updating index...")
                        self.crawler.generate_index()
                else:
                    print(f"      ❌ Download failed")
                    results['failed'] += 1
                
                # Small delay between downloads
                import time
                time.sleep(config.REQUEST_DELAY)
                
            except KeyboardInterrupt:
                print(f"\n⏹️  Download interrupted by user at article {i}")
                break
            except Exception as e:
                print(f"      ❌ Error: {e}")
                results['failed'] += 1
                continue
        
        # Final summary
        print(f"\n📊 Download Results:")
        print(f"   ✅ Successful: {results['successful']}")
        print(f"   ❌ Failed: {results['failed']}")
        print(f"   ⏭️  Skipped: {results['skipped']}")
        
        # Generate final index
        print(f"\n🎯 Generating final index...")
        self.crawler.generate_index()
        
        return results
    
    def run_comparison_and_download(self):
        """Run the complete comparison and download process"""
        print("=" * 80)
        print("🎯 ARTICLE COMPARISON & MISSING DOWNLOAD")
        print("=" * 80)
        
        # Find missing articles
        missing_articles = self.find_missing_articles()
        
        if not missing_articles:
            print("\n🎉 All articles are already downloaded!")
            return
        
        # Save report
        report = self.save_missing_articles_report(missing_articles)
        
        # Ask user if they want to download
        print(f"\n🤔 Found {len(missing_articles)} missing articles.")
        response = input("Download missing articles? (y/N): ")
        
        if response.lower() == 'y':
            results = self.download_missing_articles(missing_articles)
            
            if results['successful'] > 0:
                print(f"\n🎉 Successfully downloaded {results['successful']} missing articles!")
                print(f"📁 Check {config.OUTPUT_DIR}/index.html to browse the complete archive")
            else:
                print(f"\n⚠️  No articles were successfully downloaded")
        else:
            print("\n📋 Download cancelled. Missing articles report saved for later reference.")

def main():
    try:
        comparator = ArticleComparator()
        comparator.run_comparison_and_download()
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
