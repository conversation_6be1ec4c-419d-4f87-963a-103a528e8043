#!/usr/bin/env python3
"""
Targeted Year-Specific Crawler

This script focuses on finding and downloading missing articles from specific years
using multiple scanning methods:
1. Year-specific search URLs with date ranges
2. Monthly archive pages for the target year
3. Blogspot pagination for the year
4. Cross-reference with existing downloads to avoid duplicates
"""

import requests
import json
import re
import time
from pathlib import Path
from collections import defaultdict
from typing import Dict, List, Set, Tuple
from datetime import datetime, timedelta
from bs4 import BeautifulSoup
from urllib.parse import urljoin, urlparse, quote

import config
from crawler import BlogCrawler

class TargetedYearCrawler:
    def __init__(self, target_year: int = 2024):
        self.target_year = target_year
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        self.found_articles = {}  # URL -> article_info
        self.existing_articles = set()  # URLs of already downloaded articles
        self.crawler = BlogCrawler()
        
        # Load existing articles for this year
        self._load_existing_articles()
    
    def _load_existing_articles(self):
        """Load existing articles for the target year"""
        print(f"📁 Loading existing articles for {self.target_year}...")
        
        archive_dir = Path(config.OUTPUT_DIR) / "archive" / str(self.target_year)
        if archive_dir.exists():
            for html_file in archive_dir.glob("*.html"):
                try:
                    with open(html_file, 'r', encoding='utf-8') as f:
                        content = f.read()
                        
                    soup = BeautifulSoup(content, 'html.parser')
                    original_link = soup.find('a', string='View Original')
                    
                    if original_link and original_link.get('href'):
                        self.existing_articles.add(original_link['href'])
                        
                except Exception as e:
                    continue
        
        print(f"📁 Found {len(self.existing_articles)} existing articles for {self.target_year}")
    
    def scan_year_search_url(self) -> Dict:
        """Scan using Blogspot's year-specific search URL"""
        print(f"🔍 Scanning year search URL for {self.target_year}...")

        # Blogspot search URL with date range
        start_date = f"{self.target_year}-01-01T00:00:00-05:00"
        end_date = f"{self.target_year + 1}-01-01T00:00:00-05:00"

        search_url = f"https://gerrysdiamondsettingessays.blogspot.com/search?updated-min={start_date}&updated-max={end_date}&max-results=500"

        print(f"🔗 Search URL: {search_url}")

        articles_found = {}

        try:
            print("   📡 Making request...")
            response = self.session.get(search_url, timeout=30)
            print(f"   📡 Response status: {response.status_code}")

            if not response or response.status_code != 200:
                print("❌ Failed to fetch search URL")
                return articles_found

            print("   🔍 Parsing HTML...")
            soup = BeautifulSoup(response.content, 'html.parser')

            # Find post containers
            post_containers = soup.select(config.SELECTORS["post_container"])
            print(f"📄 Found {len(post_containers)} post containers in search results")

            for i, container in enumerate(post_containers):
                try:
                    title_elem = container.select_one(config.SELECTORS["post_title"])
                    if not title_elem:
                        continue

                    post_url = title_elem.get('href')
                    title = title_elem.get_text(strip=True)

                    print(f"   📄 {i+1}/{len(post_containers)}: {title[:50]}...")

                    # Skip if already exists
                    if post_url in self.existing_articles:
                        print(f"      ⏭️  Already exists, skipping")
                        continue

                    # Extract date
                    date_elem = container.select_one(config.SELECTORS["post_date"])
                    date_str = date_elem.get('title') if date_elem else ""

                    # Verify year from URL
                    year_match = re.search(r'/(\d{4})/', post_url)
                    year = int(year_match.group(1)) if year_match else None

                    if year != self.target_year:
                        print(f"      ⏭️  Wrong year ({year}), skipping")
                        continue

                    # Extract snippet
                    content_elem = container.select_one('.post-body')
                    snippet = ""
                    if content_elem:
                        snippet = content_elem.get_text(strip=True)[:200] + "..."

                    articles_found[post_url] = {
                        'title': title,
                        'url': post_url,
                        'date': date_str,
                        'year': year,
                        'snippet': snippet,
                        'source': 'year_search'
                    }

                    print(f"      ✅ Added to download list")

                except Exception as e:
                    print(f"      ❌ Error processing container: {e}")
                    continue

            print(f"🔍 Year search found {len(articles_found)} new articles")

        except Exception as e:
            print(f"❌ Error scanning year search URL: {e}")

        return articles_found
    
    def scan_monthly_archives(self) -> Dict:
        """Scan monthly archive pages for the target year"""
        print(f"📅 Scanning monthly archives for {self.target_year}...")
        
        articles_found = {}
        
        # Try different monthly archive URL formats
        for month in range(1, 13):
            month_formats = [
                f"https://gerrysdiamondsettingessays.blogspot.com/{self.target_year}_{month:02d}_01_archive.html",
                f"https://gerrysdiamondsettingessays.blogspot.com/{self.target_year}_{month:02d}_15_archive.html",
                f"https://gerrysdiamondsettingessays.blogspot.com/{self.target_year}/{month:02d}/",
            ]
            
            for archive_url in month_formats:
                try:
                    print(f"   📖 Checking: {archive_url}")
                    response = self.session.get(archive_url)
                    
                    if not response or response.status_code != 200:
                        continue
                        
                    soup = BeautifulSoup(response.content, 'html.parser')
                    post_containers = soup.select(config.SELECTORS["post_container"])
                    
                    month_articles = 0
                    for container in post_containers:
                        try:
                            title_elem = container.select_one(config.SELECTORS["post_title"])
                            if not title_elem:
                                continue
                                
                            post_url = title_elem.get('href')
                            title = title_elem.get_text(strip=True)
                            
                            # Skip if already exists or already found
                            if post_url in self.existing_articles or post_url in articles_found:
                                continue
                            
                            # Verify year
                            year_match = re.search(r'/(\d{4})/', post_url)
                            year = int(year_match.group(1)) if year_match else None
                            
                            if year != self.target_year:
                                continue
                            
                            # Extract other info
                            date_elem = container.select_one(config.SELECTORS["post_date"])
                            date_str = date_elem.get('title') if date_elem else ""
                            
                            content_elem = container.select_one('.post-body')
                            snippet = ""
                            if content_elem:
                                snippet = content_elem.get_text(strip=True)[:200] + "..."
                            
                            articles_found[post_url] = {
                                'title': title,
                                'url': post_url,
                                'date': date_str,
                                'year': year,
                                'snippet': snippet,
                                'source': f'monthly_archive_{month:02d}'
                            }
                            month_articles += 1
                            
                        except Exception as e:
                            continue
                    
                    if month_articles > 0:
                        print(f"   ✅ Found {month_articles} articles in {archive_url}")
                        break  # Found articles for this month, no need to try other formats
                    
                except Exception as e:
                    continue
                
                time.sleep(config.REQUEST_DELAY)
        
        print(f"📅 Monthly archives found {len(articles_found)} new articles")
        return articles_found
    
    def scan_year_pagination(self) -> Dict:
        """Scan year-specific pages with pagination"""
        print(f"📄 Scanning year pagination for {self.target_year}...")
        
        articles_found = {}
        
        # Try year-specific URL with pagination
        base_url = f"https://gerrysdiamondsettingessays.blogspot.com/{self.target_year}/"
        
        page = 1
        max_pages = 20  # Reasonable limit
        
        while page <= max_pages:
            if page == 1:
                url = base_url
            else:
                url = f"{base_url}?max-results=20&start={((page-1)*20)+1}"
            
            try:
                print(f"   📄 Page {page}: {url}")
                response = self.session.get(url)
                
                if not response or response.status_code != 200:
                    break
                    
                soup = BeautifulSoup(response.content, 'html.parser')
                post_containers = soup.select(config.SELECTORS["post_container"])
                
                if not post_containers:
                    print(f"   No posts found on page {page}, stopping")
                    break
                
                page_articles = 0
                for container in post_containers:
                    try:
                        title_elem = container.select_one(config.SELECTORS["post_title"])
                        if not title_elem:
                            continue
                            
                        post_url = title_elem.get('href')
                        title = title_elem.get_text(strip=True)
                        
                        # Skip if already exists or found
                        if post_url in self.existing_articles or post_url in articles_found:
                            continue
                        
                        # Verify year
                        year_match = re.search(r'/(\d{4})/', post_url)
                        year = int(year_match.group(1)) if year_match else None
                        
                        if year != self.target_year:
                            continue
                        
                        # Extract info
                        date_elem = container.select_one(config.SELECTORS["post_date"])
                        date_str = date_elem.get('title') if date_elem else ""
                        
                        content_elem = container.select_one('.post-body')
                        snippet = ""
                        if content_elem:
                            snippet = content_elem.get_text(strip=True)[:200] + "..."
                        
                        articles_found[post_url] = {
                            'title': title,
                            'url': post_url,
                            'date': date_str,
                            'year': year,
                            'snippet': snippet,
                            'source': f'year_pagination_{page}'
                        }
                        page_articles += 1
                        
                    except Exception as e:
                        continue
                
                print(f"   ✅ Found {page_articles} articles on page {page}")
                
                if page_articles == 0:
                    break
                    
                page += 1
                time.sleep(config.REQUEST_DELAY)
                
            except Exception as e:
                print(f"   ❌ Error on page {page}: {e}")
                break
        
        print(f"📄 Year pagination found {len(articles_found)} new articles")
        return articles_found
    
    def merge_and_deduplicate(self, *article_dicts) -> Dict:
        """Merge article dictionaries and remove duplicates"""
        print("🔄 Merging and deduplicating articles...")
        
        merged = {}
        
        for article_dict in article_dicts:
            for url, info in article_dict.items():
                if url not in merged:
                    merged[url] = info
        
        print(f"🔄 Merged to {len(merged)} unique articles")
        return merged
    
    def download_missing_articles(self, articles: Dict):
        """Download the missing articles"""
        if not articles:
            print("✅ No missing articles to download!")
            return
            
        print(f"⬇️  Starting download of {len(articles)} missing articles for {self.target_year}...")
        
        success_count = 0
        
        # Sort by title for consistent processing
        sorted_articles = sorted(articles.values(), key=lambda x: x['title'])
        
        for i, article in enumerate(sorted_articles, 1):
            print(f"📄 {i}/{len(articles)}: {article['title'][:60]}...")
            
            post = {
                'url': article['url'],
                'title': article['title'],
                'date': article['date'],
                'author': 'Gerry Lewy'
            }
            
            success = self.crawler.download_post_content(post)
            if success:
                success_count += 1
                print(f"   ✅ Downloaded successfully")
                
                # Update index after each successful download
                self.crawler.generate_index()
            else:
                print(f"   ❌ Download failed")
            
            time.sleep(config.REQUEST_DELAY)
        
        print(f"\n✅ Download complete for {self.target_year}:")
        print(f"   📄 Attempted: {len(articles)} articles")
        print(f"   ✅ Successful: {success_count} articles")
        print(f"   ❌ Failed: {len(articles) - success_count} articles")
        
        return success_count

def main():
    print("=" * 80)
    print("🎯 TARGETED YEAR-SPECIFIC CRAWLER")
    print("=" * 80)

    # Start with 2024 (highest priority - 58 missing articles)
    target_year = 2024

    print(f"🎯 Targeting year: {target_year}")
    print(f"📊 Expected missing articles: 58")
    print()

    print("🏗️  Initializing crawler...")
    crawler = TargetedYearCrawler(target_year)
    print("✅ Crawler initialized")

    # Step 1: Scan using multiple methods
    print("\n🔍 SCANNING PHASE - Using multiple methods...")

    print("\n1️⃣ METHOD 1: Year Search URL")
    search_articles = crawler.scan_year_search_url()

    print(f"\n2️⃣ METHOD 2: Monthly Archives (skipping for now to test Method 1)")
    monthly_articles = {}  # Skip for now
    # monthly_articles = crawler.scan_monthly_archives()

    print(f"\n3️⃣ METHOD 3: Year Pagination (skipping for now to test Method 1)")
    pagination_articles = {}  # Skip for now
    # pagination_articles = crawler.scan_year_pagination()
    
    # Step 2: Merge and deduplicate
    all_found_articles = crawler.merge_and_deduplicate(
        search_articles, 
        monthly_articles, 
        pagination_articles
    )
    
    # Step 3: Show summary
    print(f"\n📊 DISCOVERY SUMMARY for {target_year}:")
    print(f"   🔍 Year search method: {len(search_articles)} articles")
    print(f"   📅 Monthly archives: {len(monthly_articles)} articles")
    print(f"   📄 Year pagination: {len(pagination_articles)} articles")
    print(f"   🔄 Total unique found: {len(all_found_articles)} articles")
    print(f"   📁 Already downloaded: {len(crawler.existing_articles)} articles")
    
    # Step 4: Ask user about downloading
    if all_found_articles:
        print(f"\n🤔 Found {len(all_found_articles)} missing articles for {target_year}.")
        response = input("Download them now? (y/N): ")
        
        if response.lower() == 'y':
            success_count = crawler.download_missing_articles(all_found_articles)
            
            print(f"\n🎯 FINAL RESULTS for {target_year}:")
            print(f"   📄 Articles downloaded: {success_count}")
            print(f"   📊 Gap reduction: {success_count}/58 missing articles")
            
            remaining = 58 - success_count
            if remaining > 0:
                print(f"   ⚠️  Still missing: {remaining} articles")
                print("   💡 Consider running again or trying different year")
            else:
                print("   ✅ All missing articles for 2024 found and downloaded!")
        else:
            print("📋 Discovery complete. Run again to download.")
    else:
        print(f"\n🤔 No new articles found for {target_year}.")
        print("   This might mean:")
        print("   - All articles are already downloaded")
        print("   - Articles are in different archive formats")
        print("   - Need to try different scanning methods")
    
    print(f"\n✅ Targeted crawl for {target_year} complete!")

if __name__ == "__main__":
    main()
