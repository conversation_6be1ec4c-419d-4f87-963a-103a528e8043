#!/usr/bin/env python3
"""
Physical Archive Audit Script

This script performs a thorough physical audit by:
1. Scanning the actual directory structure and counting files
2. Reading the index.html file to extract listed articles
3. Comparing physical files vs index entries
4. Detecting duplicates by title, URL, and content similarity
"""

import os
import json
import re
from pathlib import Path
from collections import defaultdict, Counter
from typing import Dict, List, Set, Tuple
from bs4 import BeautifulSoup
import config

class PhysicalAuditor:
    def __init__(self):
        self.archive_dir = Path(config.OUTPUT_DIR) / "archive"
        self.index_file = Path(config.OUTPUT_DIR) / "index.html"
        self.physical_files = {}  # year -> [file_paths]
        self.physical_articles = {}  # file_path -> article_info
        self.index_articles = []  # articles listed in index
        
    def scan_physical_directories(self) -> Dict:
        """Scan actual directory structure and count files"""
        print("🔍 Scanning physical directory structure...")
        
        if not self.archive_dir.exists():
            print("❌ Archive directory not found!")
            return {}
            
        results = {
            'total_files': 0,
            'files_by_year': {},
            'file_details': {}
        }
        
        # Scan each year directory
        for year_dir in sorted(self.archive_dir.iterdir()):
            if year_dir.is_dir() and year_dir.name.isdigit():
                year = year_dir.name
                html_files = list(year_dir.glob("*.html"))
                
                results['files_by_year'][year] = len(html_files)
                results['total_files'] += len(html_files)
                self.physical_files[year] = html_files
                
                print(f"📁 {year}: {len(html_files)} files")
                
                # Extract details from each file
                for html_file in html_files:
                    try:
                        with open(html_file, 'r', encoding='utf-8') as f:
                            content = f.read()
                            
                        soup = BeautifulSoup(content, 'html.parser')
                        
                        # Extract title
                        title_elem = soup.find('h1', class_='post-title')
                        title = title_elem.get_text(strip=True) if title_elem else "Unknown Title"
                        
                        # Extract original URL
                        original_link = soup.find('a', string='View Original')
                        original_url = original_link['href'] if original_link else "Unknown URL"
                        
                        # Extract date
                        date_elem = soup.find('span', string=lambda x: x and 'Date:' in x)
                        date = date_elem.get_text().replace('Date:', '').strip() if date_elem else "Unknown Date"
                        
                        # Store article info
                        self.physical_articles[str(html_file)] = {
                            'title': title,
                            'url': original_url,
                            'date': date,
                            'year': year,
                            'file_path': str(html_file),
                            'file_name': html_file.name,
                            'file_size': html_file.stat().st_size
                        }
                        
                    except Exception as e:
                        print(f"⚠️  Error reading {html_file}: {e}")
                        results['file_details'][str(html_file)] = {'error': str(e)}
        
        print(f"📊 Physical scan complete: {results['total_files']} total files")
        return results
    
    def parse_index_file(self) -> List[Dict]:
        """Parse index.html to extract listed articles"""
        print("📋 Parsing index.html file...")
        
        if not self.index_file.exists():
            print("❌ Index file not found!")
            return []
            
        try:
            with open(self.index_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
            soup = BeautifulSoup(content, 'html.parser')
            
            # Find all post items
            post_items = soup.find_all('div', class_='post-item')
            
            for item in post_items:
                try:
                    # Extract title and link
                    title_link = item.find('h3').find('a')
                    title = title_link.get_text(strip=True)
                    relative_path = title_link['href']
                    
                    # Extract metadata
                    meta_div = item.find('div', class_='post-meta')
                    author = ""
                    date = ""
                    year = ""
                    
                    if meta_div:
                        spans = meta_div.find_all('span')
                        for span in spans:
                            text = span.get_text(strip=True)
                            if text.startswith('By:'):
                                author = text.replace('By:', '').strip()
                            elif text.startswith('Date:'):
                                date = text.replace('Date:', '').strip()
                            elif text.startswith('Year:'):
                                year = text.replace('Year:', '').strip()
                    
                    # Extract year from data attribute if available
                    data_year = item.get('data-year')
                    if data_year:
                        year = data_year
                    
                    self.index_articles.append({
                        'title': title,
                        'relative_path': relative_path,
                        'author': author,
                        'date': date,
                        'year': year
                    })
                    
                except Exception as e:
                    print(f"⚠️  Error parsing post item: {e}")
                    continue
            
            print(f"📋 Index parsing complete: {len(self.index_articles)} articles listed")
            return self.index_articles
            
        except Exception as e:
            print(f"❌ Error reading index file: {e}")
            return []
    
    def detect_duplicates(self) -> Dict:
        """Detect duplicates by various criteria"""
        print("🔍 Detecting duplicates...")
        
        duplicates = {
            'by_title': defaultdict(list),
            'by_url': defaultdict(list),
            'by_filename_pattern': defaultdict(list),
            'summary': {}
        }
        
        # Group by title (case-insensitive)
        for file_path, info in self.physical_articles.items():
            title_key = info['title'].lower().strip()
            duplicates['by_title'][title_key].append(info)
        
        # Group by original URL
        for file_path, info in self.physical_articles.items():
            if info['url'] != "Unknown URL":
                duplicates['by_url'][info['url']].append(info)
        
        # Group by filename pattern (same title, different dates)
        for file_path, info in self.physical_articles.items():
            # Extract title part from filename
            filename = info['file_name']
            # Remove date prefix (YYYY_MM_DD_) and extension
            title_part = re.sub(r'^\d{4}_\d{2}_\d{2}_', '', filename)
            title_part = re.sub(r'\.html$', '', title_part)
            duplicates['by_filename_pattern'][title_part].append(info)
        
        # Filter to only actual duplicates
        title_dups = {k: v for k, v in duplicates['by_title'].items() if len(v) > 1}
        url_dups = {k: v for k, v in duplicates['by_url'].items() if len(v) > 1}
        filename_dups = {k: v for k, v in duplicates['by_filename_pattern'].items() if len(v) > 1}
        
        duplicates['by_title'] = title_dups
        duplicates['by_url'] = url_dups
        duplicates['by_filename_pattern'] = filename_dups
        
        duplicates['summary'] = {
            'title_duplicates': len(title_dups),
            'url_duplicates': len(url_dups),
            'filename_duplicates': len(filename_dups),
            'total_duplicate_files': sum(len(v) for v in title_dups.values())
        }
        
        print(f"🔍 Duplicate detection results:")
        print(f"   📝 Title duplicates: {len(title_dups)} groups")
        print(f"   🔗 URL duplicates: {len(url_dups)} groups")
        print(f"   📄 Filename duplicates: {len(filename_dups)} groups")
        print(f"   📊 Total duplicate files: {duplicates['summary']['total_duplicate_files']}")
        
        return duplicates
    
    def compare_physical_vs_index(self) -> Dict:
        """Compare physical files vs index entries"""
        print("⚖️  Comparing physical files vs index entries...")
        
        # Create sets for comparison
        physical_titles = set()
        index_titles = set()
        
        for info in self.physical_articles.values():
            physical_titles.add(info['title'].strip())
        
        for article in self.index_articles:
            index_titles.add(article['title'].strip())
        
        comparison = {
            'physical_count': len(self.physical_articles),
            'index_count': len(self.index_articles),
            'physical_only': physical_titles - index_titles,
            'index_only': index_titles - physical_titles,
            'common': physical_titles & index_titles
        }
        
        print(f"⚖️  Comparison results:")
        print(f"   📁 Physical files: {comparison['physical_count']}")
        print(f"   📋 Index entries: {comparison['index_count']}")
        print(f"   📁➡️  Physical only: {len(comparison['physical_only'])}")
        print(f"   📋➡️  Index only: {len(comparison['index_only'])}")
        print(f"   🤝 Common: {len(comparison['common'])}")
        
        return comparison
    
    def generate_detailed_report(self, physical_results: Dict, duplicates: Dict, comparison: Dict):
        """Generate detailed audit report"""
        print("📋 Generating detailed audit report...")
        
        report = {
            'audit_date': str(Path().cwd()),
            'physical_scan': physical_results,
            'duplicates': {
                'summary': duplicates['summary'],
                'title_duplicates': [
                    {
                        'title': title,
                        'count': len(files),
                        'files': [f['file_path'] for f in files]
                    }
                    for title, files in duplicates['by_title'].items()
                ],
                'url_duplicates': [
                    {
                        'url': url,
                        'count': len(files),
                        'files': [f['file_path'] for f in files]
                    }
                    for url, files in duplicates['by_url'].items()
                ]
            },
            'comparison': comparison,
            'recommendations': []
        }
        
        # Add recommendations
        if duplicates['summary']['total_duplicate_files'] > 0:
            report['recommendations'].append("Remove duplicate files to save space and avoid confusion")
        
        if len(comparison['physical_only']) > 0:
            report['recommendations'].append("Update index to include all physical files")
        
        if len(comparison['index_only']) > 0:
            report['recommendations'].append("Remove orphaned index entries or download missing files")
        
        # Save report
        report_path = Path(config.OUTPUT_DIR) / "metadata" / "physical_audit_report.json"
        report_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False, default=str)
        
        print(f"📋 Detailed report saved to: {report_path}")
        
        # Also save a human-readable summary
        summary_path = report_path.with_suffix('.txt')
        with open(summary_path, 'w', encoding='utf-8') as f:
            f.write("PHYSICAL ARCHIVE AUDIT REPORT\n")
            f.write("=" * 50 + "\n\n")
            
            f.write("PHYSICAL FILES BY YEAR:\n")
            for year, count in sorted(physical_results['files_by_year'].items()):
                f.write(f"  {year}: {count} files\n")
            f.write(f"  TOTAL: {physical_results['total_files']} files\n\n")
            
            f.write("DUPLICATES FOUND:\n")
            f.write(f"  Title duplicates: {duplicates['summary']['title_duplicates']} groups\n")
            f.write(f"  URL duplicates: {duplicates['summary']['url_duplicates']} groups\n")
            f.write(f"  Total duplicate files: {duplicates['summary']['total_duplicate_files']}\n\n")
            
            if duplicates['by_title']:
                f.write("TITLE DUPLICATES:\n")
                for title, files in duplicates['by_title'].items():
                    f.write(f"  '{title}' ({len(files)} copies):\n")
                    for file_info in files:
                        f.write(f"    - {file_info['file_path']}\n")
                    f.write("\n")
            
            f.write("PHYSICAL vs INDEX COMPARISON:\n")
            f.write(f"  Physical files: {comparison['physical_count']}\n")
            f.write(f"  Index entries: {comparison['index_count']}\n")
            f.write(f"  Files not in index: {len(comparison['physical_only'])}\n")
            f.write(f"  Index entries without files: {len(comparison['index_only'])}\n")
        
        print(f"📄 Human-readable summary saved to: {summary_path}")

def main():
    print("=" * 80)
    print("🔍 PHYSICAL ARCHIVE AUDIT")
    print("=" * 80)
    
    auditor = PhysicalAuditor()
    
    # Step 1: Scan physical directories
    physical_results = auditor.scan_physical_directories()
    
    # Step 2: Parse index file
    auditor.parse_index_file()
    
    # Step 3: Detect duplicates
    duplicates = auditor.detect_duplicates()
    
    # Step 4: Compare physical vs index
    comparison = auditor.compare_physical_vs_index()
    
    # Step 5: Generate detailed report
    auditor.generate_detailed_report(physical_results, duplicates, comparison)
    
    print("\n✅ Physical audit complete!")
    print("📋 Check the generated reports for detailed analysis.")

if __name__ == "__main__":
    main()
