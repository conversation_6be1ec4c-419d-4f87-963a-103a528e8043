#!/usr/bin/env python3
"""
HTML-Based Article Downloader

Uses the extracted article lists from the complete HTML parser
to download all articles using the proven crawler logic.

This combines:
1. Article URLs from HTML extraction (complete_html_parser.py)
2. Download logic from existing crawler (crawler.py)
3. Batch processing from run_batch_crawl.py
"""

import sys
import json
import time
import traceback
from pathlib import Path
from typing import List, Dict

# Add current directory to path
sys.path.insert(0, str(Path(__file__).parent))

from crawler import BlogCrawler
import config

class HTMLBasedDownloader:
    def __init__(self):
        self.crawler = BlogCrawler()
        self.articles_by_year = {}
        self.all_articles = []
        
    def load_extracted_articles(self) -> bool:
        """Load articles from the HTML extraction results"""
        try:
            # Load the complete extraction results
            extraction_file = Path(config.OUTPUT_DIR) / "metadata" / "complete_html_extraction.json"
            
            if not extraction_file.exists():
                print(f"❌ Extraction file not found: {extraction_file}")
                print("   Please run complete_html_parser.py first!")
                return False
            
            print(f"📂 Loading extracted articles from: {extraction_file}")
            
            with open(extraction_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            self.articles_by_year = data.get('articles_by_year', {})
            self.all_articles = data.get('all_articles', [])
            
            # Validate data
            if not self.articles_by_year or not self.all_articles:
                print("❌ No articles found in extraction file!")
                return False
            
            print(f"✅ Loaded {len(self.all_articles)} articles across {len(self.articles_by_year)} years")
            
            # Show summary
            for year in sorted(self.articles_by_year.keys(), reverse=True):
                count = len(self.articles_by_year[year])
                print(f"   📅 {year}: {count} articles")
            
            return True
            
        except Exception as e:
            print(f"❌ Error loading extracted articles: {e}")
            return False
    
    def convert_to_post_format(self, article: Dict) -> Dict:
        """Convert article dict to post format expected by crawler"""
        return {
            'url': article['url'],
            'title': article['title'],
            'year': article.get('year', 'unknown'),
            'archive_date': article.get('archive_date', 'unknown'),
            'source': 'html_extraction'
        }
    
    def download_articles_by_year(self, target_years: List[str] = None) -> Dict:
        """Download articles year by year, following the proven batch logic"""
        
        if target_years is None:
            target_years = sorted(self.articles_by_year.keys(), reverse=True)  # Latest first
        
        print(f"🎯 Target years: {', '.join(target_years)}")
        
        results = {
            'total_processed': 0,
            'total_successful': 0,
            'total_failed': 0,
            'total_skipped': 0,
            'by_year': {}
        }
        
        start_time = time.time()
        
        for year in target_years:
            if year not in self.articles_by_year:
                print(f"⚠️  Year {year} not found in extracted data")
                continue
            
            articles = self.articles_by_year[year]
            year_results = {
                'processed': 0,
                'successful': 0,
                'failed': 0,
                'skipped': 0
            }
            
            print(f"\n🗓️  Processing Year {year} - {len(articles)} articles")
            print("=" * 60)
            
            for i, article in enumerate(articles, 1):
                try:
                    post = self.convert_to_post_format(article)
                    title_preview = post['title'][:50] + "..." if len(post['title']) > 50 else post['title']
                    
                    print(f"   📄 {i:3d}/{len(articles)} - {title_preview}")
                    
                    # Check if already downloaded
                    if self.crawler.is_post_downloaded(post['url']):
                        print(f"      ⏭️  Already downloaded")
                        year_results['skipped'] += 1
                        results['total_skipped'] += 1
                        continue
                    
                    # Download the post
                    success = self.crawler.download_post_content(post)
                    
                    if success:
                        print(f"      ✅ Downloaded successfully")
                        year_results['successful'] += 1
                        results['total_successful'] += 1
                        
                        # Update index every 10 posts for real-time progress
                        if year_results['successful'] % 10 == 0:
                            print(f"      🎯 Updating index...")
                            self.crawler.generate_index()
                    else:
                        print(f"      ❌ Download failed")
                        year_results['failed'] += 1
                        results['total_failed'] += 1
                    
                    year_results['processed'] += 1
                    results['total_processed'] += 1
                    
                    # Small delay between downloads
                    time.sleep(config.REQUEST_DELAY)
                    
                except KeyboardInterrupt:
                    raise
                except Exception as e:
                    print(f"      ❌ Error: {e}")
                    year_results['failed'] += 1
                    results['total_failed'] += 1
                    continue
            
            # Year summary
            print(f"\n   📊 Year {year} Summary:")
            print(f"      ✅ Successful: {year_results['successful']}")
            print(f"      ❌ Failed: {year_results['failed']}")
            print(f"      ⏭️  Skipped: {year_results['skipped']}")
            print(f"      📊 Total: {year_results['processed']}")
            
            results['by_year'][year] = year_results
            
            # Update index after each year
            print(f"   🎯 Updating index after year {year}...")
            self.crawler.generate_index()
        
        # Final summary
        end_time = time.time()
        duration = end_time - start_time
        hours = int(duration // 3600)
        minutes = int((duration % 3600) // 60)
        seconds = int(duration % 60)
        
        print("\n" + "=" * 80)
        print("🎉 DOWNLOAD COMPLETED!")
        print("=" * 80)
        print(f"⏱️  Total Time: {hours:02d}:{minutes:02d}:{seconds:02d}")
        print(f"📊 Total Articles: {results['total_processed']}")
        print(f"✅ Successful: {results['total_successful']}")
        print(f"❌ Failed: {results['total_failed']}")
        print(f"⏭️  Skipped: {results['total_skipped']}")
        
        # Generate final index and summary
        print("\n🎯 Generating final index and summary...")
        self.crawler.generate_summary()
        self.crawler.generate_index()
        
        return results
    
    def run_download(self, target_years: List[str] = None) -> Dict:
        """Run the complete download process"""
        print("=" * 80)
        print("🎯 HTML-BASED ARTICLE DOWNLOADER")
        print("=" * 80)
        
        # Load extracted articles
        if not self.load_extracted_articles():
            return {}
        
        print(f"\n📍 Base URL: {config.BASE_URL}")
        print(f"📁 Output Directory: {config.OUTPUT_DIR}")
        print(f"🔧 Test Mode: {config.TEST_MODE}")
        print(f"🖼️  Convert to PNG: {config.CONVERT_TO_PNG}")
        print(f"⚡ Request Delay: {config.REQUEST_DELAY}s")
        
        if config.TEST_MODE:
            print("\n⚠️  WARNING: Test mode is enabled!")
            print("   This will only download a limited number of posts.")
            print("   Set TEST_MODE = False in config.py for full download.")
        
        # Confirm before starting
        total_articles = len(self.all_articles)
        print(f"\n🎯 Ready to download {total_articles} articles")
        
        if target_years:
            target_count = sum(len(self.articles_by_year.get(year, [])) for year in target_years)
            print(f"   📅 Target years: {', '.join(target_years)} ({target_count} articles)")
        
        response = input("\n🤔 Continue with download? (y/N): ")
        if response.lower() != 'y':
            print("❌ Download cancelled.")
            return {}
        
        print("\n🎬 Starting download...")
        print("💡 Processing articles year by year for better organization")
        print("📊 You can interrupt and resume at any time")
        
        try:
            results = self.download_articles_by_year(target_years)
            return results
            
        except KeyboardInterrupt:
            print(f"\n⏹️  Download interrupted by user.")
            print(f"📊 Progress saved - you can resume later!")
            
            # Generate index with current progress
            try:
                print("🎯 Generating index with current progress...")
                self.crawler.generate_index()
                print("✅ Index updated successfully")
            except:
                print("❌ Could not update index")
            
            return {}
            
        except Exception as e:
            print(f"\n❌ Error during download: {e}")
            print("\n🔍 Full traceback:")
            traceback.print_exc()
            return {}

def main():
    """Main function with command line argument support"""
    
    # Parse command line arguments for target years
    target_years = None
    if len(sys.argv) > 1:
        target_years = sys.argv[1].split(',')
        print(f"🎯 Target years from command line: {target_years}")
    
    try:
        downloader = HTMLBasedDownloader()
        results = downloader.run_download(target_years)
        
        if results:
            print(f"\n🚀 Download completed successfully!")
            print(f"📁 Check {config.OUTPUT_DIR}/index.html to browse the archive")
        else:
            print(f"\n❌ Download failed or was cancelled")
            
    except Exception as e:
        print(f"❌ Error in main: {e}")
        traceback.print_exc()

if __name__ == "__main__":
    main()
