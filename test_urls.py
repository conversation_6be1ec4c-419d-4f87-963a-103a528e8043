#!/usr/bin/env python3
"""Test URL extraction from homepage"""

import requests
from bs4 import BeautifulSoup
import config

def test_url_extraction():
    print("🔍 Testing URL extraction from homepage...")
    
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    })
    
    response = session.get(config.BASE_URL)
    if not response:
        print("❌ Failed to fetch homepage")
        return
        
    print(f"✅ Homepage fetched: {response.status_code}")
    
    soup = BeautifulSoup(response.content, 'html.parser')
    
    # Find all archive links
    archive_links = soup.select('a[href*="_archive.html"]')
    print(f"📚 Found {len(archive_links)} archive links")
    
    print("\n🔍 Raw archive links:")
    for i, link in enumerate(archive_links[:10]):  # Show first 10
        href = link.get('href')
        text = link.get_text(strip=True)
        print(f"   {i+1}. href='{href}' text='{text}'")
    
    print("\n🔧 Processed URLs:")
    archive_urls = []
    for link in archive_links[:10]:
        href = link.get('href')
        if href:
            if href.startswith('http'):
                final_url = href
            else:
                href = href.lstrip('/')
                final_url = f"https://gerrysdiamondsettingessays.blogspot.com/{href}"
            
            archive_urls.append(final_url)
            print(f"   {final_url}")
    
    # Test fetching one URL
    if archive_urls:
        test_url = archive_urls[0]
        print(f"\n🧪 Testing fetch of: {test_url}")
        try:
            test_response = session.get(test_url)
            print(f"✅ Test fetch successful: {test_response.status_code}")
        except Exception as e:
            print(f"❌ Test fetch failed: {e}")

if __name__ == "__main__":
    test_url_extraction()
