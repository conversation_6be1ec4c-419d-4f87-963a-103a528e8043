#!/usr/bin/env python3
"""
Test script for Gerry's Diamond Setting Essays Blog Crawler
This script runs a limited test to verify the crawler works correctly.
"""

import sys
import traceback
from pathlib import Path

# Add current directory to path
sys.path.insert(0, str(Path(__file__).parent))

from crawler import BlogCrawler
import config

def main():
    """Run test crawl"""
    print("=" * 60)
    print("Gerry's Diamond Setting Essays Blog Crawler - Test Mode")
    print("=" * 60)
    print()
    
    print(f"Base URL: {config.BASE_URL}")
    print(f"Output Directory: {config.OUTPUT_DIR}")
    print(f"Test Mode: {config.TEST_MODE}")
    print(f"Max Posts (Test): {config.TEST_MAX_POSTS}")
    print(f"Convert to PNG: {config.CONVERT_TO_PNG}")
    print(f"Request Delay: {config.REQUEST_DELAY}s")
    print()
    
    try:
        # Initialize crawler
        print("Initializing crawler...")
        crawler = BlogCrawler()
        
        # Run test crawl
        print("Starting test crawl...")
        crawler.run_test_crawl()
        
        print()
        print("=" * 60)
        print("Test crawl completed successfully!")
        print("=" * 60)
        print()
        print("Check the following directories for results:")
        print(f"  - Posts: {config.ARCHIVE_DIR}")
        print(f"  - Images: {config.IMAGES_DIR}")
        print(f"  - Metadata: {config.METADATA_DIR}")
        print(f"  - Logs: {config.LOG_FILE}")
        print()
        
        # Show directory structure
        print("Directory structure created:")
        show_directory_tree(config.OUTPUT_DIR)
        
    except KeyboardInterrupt:
        print("\nCrawl interrupted by user.")
        sys.exit(1)
    except Exception as e:
        print(f"\nError during crawl: {e}")
        print("\nFull traceback:")
        traceback.print_exc()
        sys.exit(1)

def show_directory_tree(path: Path, prefix: str = "", max_depth: int = 3, current_depth: int = 0):
    """Show directory tree structure"""
    if current_depth > max_depth:
        return
        
    if not path.exists():
        return
        
    items = sorted(path.iterdir(), key=lambda x: (x.is_file(), x.name))
    
    for i, item in enumerate(items):
        is_last = i == len(items) - 1
        current_prefix = "└── " if is_last else "├── "
        print(f"{prefix}{current_prefix}{item.name}")
        
        if item.is_dir() and current_depth < max_depth:
            next_prefix = prefix + ("    " if is_last else "│   ")
            show_directory_tree(item, next_prefix, max_depth, current_depth + 1)

if __name__ == "__main__":
    main()
