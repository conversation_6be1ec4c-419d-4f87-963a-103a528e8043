#!/usr/bin/env python3
"""
Comprehensive Blog Scanner

This script uses multiple methods to find all articles:
1. Scans archive pages (like previous script)
2. Scans main blog pages with pagination
3. Uses the blog archive sidebar to get year-wise counts
4. Cross-references all methods to find missing articles
"""

import requests
import json
import re
import time
from pathlib import Path
from collections import defaultdict
from typing import Dict, List, Set, Tuple
from datetime import datetime
from bs4 import BeautifulSoup
from urllib.parse import urljoin, urlparse

import config
from crawler import BlogCrawler

class ComprehensiveBlogScanner:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        self.all_articles = {}  # URL -> article_info
        self.archive_dir = Path(config.OUTPUT_DIR) / "archive"
        
    def get_blog_archive_counts(self) -> Dict[str, int]:
        """Get article counts from blog archive sidebar"""
        print("📊 Getting article counts from blog archive sidebar...")
        
        response = self.session.get(config.BASE_URL)
        if not response:
            print("❌ Failed to fetch homepage")
            return {}
            
        soup = BeautifulSoup(response.content, 'html.parser')
        
        # Find the blog archive section
        archive_counts = {}
        
        # Look for archive links with counts in parentheses
        archive_links = soup.find_all('a', href=re.compile(r'/search/label/'))
        if not archive_links:
            # Try different selector for archive
            archive_section = soup.find('div', {'id': 'BlogArchive1'})
            if archive_section:
                archive_links = archive_section.find_all('a')
        
        # Also try to find year-based archive links
        year_pattern = re.compile(r'(\d{4})\s*\((\d+)\)')
        
        # Scan all text for year patterns
        page_text = soup.get_text()
        year_matches = year_pattern.findall(page_text)
        
        for year, count in year_matches:
            archive_counts[year] = int(count)
            
        print(f"📊 Found archive counts: {archive_counts}")
        return archive_counts
    
    def scan_main_blog_pages(self, max_pages: int = 50) -> Dict:
        """Scan main blog pages with pagination"""
        print(f"📖 Scanning main blog pages (up to {max_pages} pages)...")
        
        articles_found = {}
        page = 1
        
        while page <= max_pages:
            if page == 1:
                url = config.BASE_URL
            else:
                # Blogspot pagination format
                url = f"{config.BASE_URL}?max-results=20&start={((page-1)*20)+1}"
            
            print(f"📄 Scanning page {page}: {url}")
            
            try:
                response = self.session.get(url)
                if not response:
                    break
                    
                soup = BeautifulSoup(response.content, 'html.parser')
                
                # Find post containers
                post_containers = soup.select(config.SELECTORS["post_container"])
                
                if not post_containers:
                    print(f"   No posts found on page {page}, stopping")
                    break
                
                page_articles = 0
                for container in post_containers:
                    try:
                        title_elem = container.select_one(config.SELECTORS["post_title"])
                        if not title_elem:
                            continue
                            
                        post_url = title_elem.get('href')
                        title = title_elem.get_text(strip=True)
                        
                        # Skip if already found
                        if post_url in articles_found:
                            continue
                        
                        # Extract date
                        date_elem = container.select_one(config.SELECTORS["post_date"])
                        date_str = date_elem.get('title') if date_elem else ""
                        
                        # Extract year from URL
                        year_match = re.search(r'/(\d{4})/', post_url)
                        year = int(year_match.group(1)) if year_match else None
                        
                        # Extract snippet
                        content_elem = container.select_one('.post-body')
                        snippet = ""
                        if content_elem:
                            snippet = content_elem.get_text(strip=True)[:200] + "..."
                        
                        articles_found[post_url] = {
                            'title': title,
                            'url': post_url,
                            'date': date_str,
                            'year': year,
                            'snippet': snippet,
                            'source': f'main_page_{page}'
                        }
                        page_articles += 1
                        
                    except Exception as e:
                        continue
                
                print(f"   Found {page_articles} articles on page {page}")
                
                if page_articles == 0:
                    print(f"   No new articles on page {page}, stopping")
                    break
                    
                page += 1
                time.sleep(config.REQUEST_DELAY)
                
            except Exception as e:
                print(f"⚠️  Error scanning page {page}: {e}")
                break
        
        print(f"📖 Main blog scan complete: {len(articles_found)} articles found")
        return articles_found
    
    def scan_archive_pages(self) -> Dict:
        """Scan archive pages (from previous method)"""
        print("📚 Scanning archive pages...")
        
        response = self.session.get(config.BASE_URL)
        if not response:
            return {}
            
        soup = BeautifulSoup(response.content, 'html.parser')
        archive_links = soup.select('a[href*="_archive.html"]')
        
        archive_urls = []
        for link in archive_links:
            href = link.get('href')
            if href and href.startswith('http'):
                archive_urls.append(href)
        
        archive_urls = sorted(list(set(archive_urls)))
        print(f"📚 Found {len(archive_urls)} archive pages")
        
        articles_found = {}
        
        for i, archive_url in enumerate(archive_urls, 1):
            print(f"📖 Scanning archive {i}/{len(archive_urls)}")
            
            try:
                response = self.session.get(archive_url)
                if not response:
                    continue
                    
                soup = BeautifulSoup(response.content, 'html.parser')
                post_containers = soup.select(config.SELECTORS["post_container"])
                
                for container in post_containers:
                    try:
                        title_elem = container.select_one(config.SELECTORS["post_title"])
                        if not title_elem:
                            continue
                            
                        post_url = title_elem.get('href')
                        title = title_elem.get_text(strip=True)
                        
                        if post_url in articles_found:
                            continue
                        
                        # Extract other info
                        date_elem = container.select_one(config.SELECTORS["post_date"])
                        date_str = date_elem.get('title') if date_elem else ""
                        
                        year_match = re.search(r'/(\d{4})/', post_url)
                        year = int(year_match.group(1)) if year_match else None
                        
                        content_elem = container.select_one('.post-body')
                        snippet = ""
                        if content_elem:
                            snippet = content_elem.get_text(strip=True)[:200] + "..."
                        
                        articles_found[post_url] = {
                            'title': title,
                            'url': post_url,
                            'date': date_str,
                            'year': year,
                            'snippet': snippet,
                            'source': f'archive_{i}'
                        }
                        
                    except Exception as e:
                        continue
                        
            except Exception as e:
                print(f"⚠️  Error processing archive {archive_url}: {e}")
                continue
                
            time.sleep(config.REQUEST_DELAY)
        
        print(f"📚 Archive scan complete: {len(articles_found)} articles found")
        return articles_found
    
    def merge_all_sources(self, main_articles: Dict, archive_articles: Dict) -> Dict:
        """Merge articles from all sources"""
        print("🔄 Merging articles from all sources...")
        
        all_articles = {}
        
        # Add main blog articles
        for url, info in main_articles.items():
            all_articles[url] = info
        
        # Add archive articles (don't overwrite)
        for url, info in archive_articles.items():
            if url not in all_articles:
                all_articles[url] = info
        
        # Group by year for analysis
        by_year = defaultdict(int)
        for info in all_articles.values():
            year = info.get('year', 'Unknown')
            by_year[year] += 1
        
        print(f"🔄 Merged results:")
        print(f"   📄 Total unique articles: {len(all_articles)}")
        print(f"   📅 By year:")
        for year in sorted(by_year.keys()):
            print(f"      {year}: {by_year[year]} articles")
        
        return all_articles
    
    def get_physical_articles(self) -> Dict:
        """Get list of physically downloaded articles"""
        print("📁 Scanning physical archive...")
        
        physical_articles = {}
        
        if not self.archive_dir.exists():
            return physical_articles
        
        for year_dir in self.archive_dir.iterdir():
            if year_dir.is_dir() and year_dir.name.isdigit():
                for html_file in year_dir.glob("*.html"):
                    try:
                        with open(html_file, 'r', encoding='utf-8') as f:
                            content = f.read()
                            
                        soup = BeautifulSoup(content, 'html.parser')
                        original_link = soup.find('a', string='View Original')
                        
                        if original_link and original_link.get('href'):
                            original_url = original_link['href']
                            
                            title_elem = soup.find('h1', class_='post-title')
                            title = title_elem.get_text(strip=True) if title_elem else "Unknown"
                            
                            physical_articles[original_url] = {
                                'title': title,
                                'url': original_url,
                                'year': int(year_dir.name),
                                'local_file': str(html_file)
                            }
                            
                    except Exception as e:
                        continue
        
        by_year = defaultdict(int)
        for info in physical_articles.values():
            by_year[info['year']] += 1
        
        print(f"📁 Physical archive:")
        print(f"   📄 Total files: {len(physical_articles)}")
        print(f"   📅 By year:")
        for year in sorted(by_year.keys()):
            print(f"      {year}: {by_year[year]} files")
        
        return physical_articles
    
    def find_comprehensive_gaps(self, all_live: Dict, physical: Dict) -> Dict:
        """Find missing articles comprehensively"""
        print("🔍 Finding comprehensive gaps...")
        
        live_urls = set(all_live.keys())
        physical_urls = set(physical.keys())
        
        missing_urls = live_urls - physical_urls
        missing_articles = {url: all_live[url] for url in missing_urls}
        
        # Group by year
        missing_by_year = defaultdict(list)
        for article in missing_articles.values():
            year = article.get('year', 'Unknown')
            missing_by_year[year].append(article)
        
        print(f"🔍 Comprehensive gap analysis:")
        print(f"   🌐 Live articles found: {len(live_urls)}")
        print(f"   📁 Physical articles: {len(physical_urls)}")
        print(f"   ❌ Missing articles: {len(missing_urls)}")
        print(f"   📅 Missing by year:")
        for year in sorted(missing_by_year.keys()):
            print(f"      {year}: {len(missing_by_year[year])} articles")
        
        return missing_articles

def main():
    print("=" * 80)
    print("🔍 COMPREHENSIVE BLOG SCAN AND GAP ANALYSIS")
    print("=" * 80)
    
    scanner = ComprehensiveBlogScanner()
    
    # Step 1: Get archive counts from sidebar
    archive_counts = scanner.get_blog_archive_counts()
    
    # Step 2: Scan main blog pages
    main_articles = scanner.scan_main_blog_pages(max_pages=50)
    
    # Step 3: Scan archive pages
    archive_articles = scanner.scan_archive_pages()
    
    # Step 4: Merge all sources
    all_live_articles = scanner.merge_all_sources(main_articles, archive_articles)
    
    # Step 5: Get physical articles
    physical_articles = scanner.get_physical_articles()
    
    # Step 6: Find comprehensive gaps
    missing_articles = scanner.find_comprehensive_gaps(all_live_articles, physical_articles)
    
    # Step 7: Save comprehensive report
    report = {
        'scan_date': datetime.now().isoformat(),
        'archive_sidebar_counts': archive_counts,
        'main_blog_articles': len(main_articles),
        'archive_page_articles': len(archive_articles),
        'total_live_articles': len(all_live_articles),
        'physical_articles': len(physical_articles),
        'missing_articles': len(missing_articles),
        'missing_by_year': {}
    }
    
    # Group missing by year for report
    for article in missing_articles.values():
        year = str(article.get('year', 'Unknown'))
        if year not in report['missing_by_year']:
            report['missing_by_year'][year] = 0
        report['missing_by_year'][year] += 1
    
    # Save report
    report_path = Path(config.OUTPUT_DIR) / "metadata" / "comprehensive_scan_report.json"
    with open(report_path, 'w', encoding='utf-8') as f:
        json.dump(report, f, indent=2)
    
    print(f"\n📋 Comprehensive report saved to: {report_path}")
    
    # Ask about downloading missing articles
    if missing_articles:
        response = input(f"\n🤔 Found {len(missing_articles)} missing articles. Download them? (y/N): ")
        if response.lower() == 'y':
            print("⬇️  Starting download of missing articles...")
            
            crawler = BlogCrawler()
            success_count = 0
            
            # Group by year and download
            missing_by_year = defaultdict(list)
            for article in missing_articles.values():
                year = article.get('year', 2025)
                missing_by_year[year].append(article)
            
            for year in sorted(missing_by_year.keys(), reverse=True):
                articles = missing_by_year[year]
                print(f"\n🗓️  Processing {len(articles)} missing articles from {year}")
                
                for i, article in enumerate(articles, 1):
                    print(f"   📄 {i}/{len(articles)}: {article['title'][:60]}...")
                    
                    post = {
                        'url': article['url'],
                        'title': article['title'],
                        'date': article['date'],
                        'author': 'Gerry Lewy'
                    }
                    
                    success = crawler.download_post_content(post)
                    if success:
                        success_count += 1
                        print(f"   ✅ Downloaded successfully")
                        crawler.generate_index()
                    else:
                        print(f"   ❌ Download failed")
                    
                    time.sleep(config.REQUEST_DELAY)
            
            print(f"\n✅ Download complete: {success_count}/{len(missing_articles)} successful")
    
    print("\n✅ Comprehensive scan complete!")

if __name__ == "__main__":
    main()
