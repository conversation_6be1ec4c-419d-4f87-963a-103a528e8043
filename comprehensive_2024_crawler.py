#!/usr/bin/env python3
"""
Comprehensive 2024 Crawler

Combines all successful methods:
1. Original corrected crawler (153 articles)
2. Investigation results (additional articles from problem dates)
3. Broader search strategies for complete coverage

Goal: Get as close to 227 articles as possible for 2024
"""

import requests
import json
import re
import time
from pathlib import Path
from typing import List, Dict, Set
from datetime import datetime
from bs4 import BeautifulSoup

import config

class Comprehensive2024Crawler:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        self.all_articles = []
        self.seen_urls = set()
        
    def load_previous_results(self) -> List[Dict]:
        """Load articles from previous corrected crawl"""
        print("📋 Loading previous corrected crawl results...")
        
        try:
            results_path = Path(config.OUTPUT_DIR) / "metadata" / "corrected_2024_crawl.json"
            if results_path.exists():
                with open(results_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    articles = data.get('all_articles', [])
                    print(f"   ✅ Loaded {len(articles)} articles from previous crawl")
                    return articles
        except Exception as e:
            print(f"   ❌ Error loading previous results: {e}")
        
        return []
    
    def load_investigation_results(self) -> List[Dict]:
        """Load additional articles from investigation"""
        print("🔍 Loading investigation results...")
        
        try:
            results_path = Path(config.OUTPUT_DIR) / "metadata" / "missing_dates_investigation.json"
            if results_path.exists():
                with open(results_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    
                    articles = []
                    for date_key, result in data.get('results', {}).items():
                        articles.extend(result.get('articles', []))
                    
                    print(f"   ✅ Loaded {len(articles)} additional articles from investigation")
                    return articles
        except Exception as e:
            print(f"   ❌ Error loading investigation results: {e}")
        
        return []
    
    def extract_from_expanded_sidebar(self) -> List[Dict]:
        """Extract ALL articles directly from the expanded sidebar HTML"""
        print("🎯 Extracting ALL articles from expanded 2024 sidebar...")

        # The user provided the expanded sidebar HTML - we need to parse it
        # For now, let's use the live sidebar and expand it programmatically

        try:
            response = self.session.get(config.BASE_URL, timeout=30)
            if response.status_code != 200:
                print(f"❌ HTTP {response.status_code}")
                return []

            soup = BeautifulSoup(response.content, 'html.parser')
            archive_section = soup.select_one('#BlogArchive1')

            if not archive_section:
                print("❌ Archive section not found")
                return []

            # Look for all article links within the 2024 section
            articles = []

            # Find all archive date links that contain 2024
            archive_links = archive_section.select('a.post-count-link')

            for link in archive_links:
                href = link.get('href', '')
                if '2024_' in href and '_archive.html' in href:
                    # This is a 2024 date archive - crawl it for all articles
                    date_match = re.search(r'2024_(\d{2})_(\d{2})_archive\.html', href)
                    if date_match:
                        month = date_match.group(1)
                        day = date_match.group(2)
                        date_key = f"{int(month):02d}/{int(day):02d}"

                        print(f"   📅 Crawling {date_key} archive...")

                        try:
                            archive_response = self.session.get(href, timeout=20)
                            if archive_response.status_code == 200:
                                archive_soup = BeautifulSoup(archive_response.content, 'html.parser')
                                post_containers = archive_soup.select(config.SELECTORS["post_container"])

                                for container in post_containers:
                                    try:
                                        title_elem = container.select_one(config.SELECTORS["post_title"])
                                        if title_elem and title_elem.get('href'):
                                            article_url = title_elem.get('href')
                                            title = title_elem.get_text(strip=True)

                                            # Accept ALL articles from 2024 date archives
                                            # (even if URL shows different year due to publishing quirks)
                                            article = {
                                                'url': article_url,
                                                'title': title,
                                                'archive_date': date_key,
                                                'source': 'expanded_sidebar_archive'
                                            }
                                            articles.append(article)

                                    except Exception as e:
                                        continue

                        except Exception as e:
                            print(f"      ❌ Error crawling {date_key}: {e}")

                        time.sleep(0.3)  # Be respectful

            print(f"   ✅ Total from expanded sidebar: {len(articles)} articles")
            return articles

        except Exception as e:
            print(f"❌ Error extracting from expanded sidebar: {e}")
            return []
    
    def merge_all_sources(self) -> List[Dict]:
        """Merge articles from all sources and remove duplicates"""
        print("\n" + "=" * 80)
        print("🔄 MERGING ALL SOURCES")
        print("=" * 80)
        
        # Load from all sources
        previous_articles = self.load_previous_results()
        investigation_articles = self.load_investigation_results()
        comprehensive_articles = self.extract_from_expanded_sidebar()
        
        # Combine all sources
        all_sources = [
            ("Previous Corrected Crawl", previous_articles),
            ("Investigation Results", investigation_articles),
            ("Expanded Sidebar Extraction", comprehensive_articles)
        ]
        
        final_articles = []
        seen_urls = set()
        
        for source_name, articles in all_sources:
            added_count = 0
            
            for article in articles:
                if article['url'] not in seen_urls:
                    final_articles.append(article)
                    seen_urls.add(article['url'])
                    added_count += 1
            
            print(f"📊 {source_name}: {added_count} new articles (from {len(articles)} total)")
        
        print(f"\n✅ FINAL MERGED TOTAL: {len(final_articles)} unique articles")
        
        return final_articles
    
    def run_comprehensive_crawl(self) -> Dict:
        """Run comprehensive 2024 crawl combining all methods"""
        print("=" * 80)
        print("🎯 COMPREHENSIVE 2024 CRAWLER")
        print("=" * 80)
        print("🎯 Goal: Combine all successful methods to get closest to 227 articles")
        print()
        
        # Merge all sources
        final_articles = self.merge_all_sources()
        
        # Verify all articles are from 2024
        verified_articles = []
        for article in final_articles:
            year_match = re.search(r'/(\d{4})/', article['url'])
            if year_match and int(year_match.group(1)) == 2024:
                verified_articles.append(article)
        
        removed_count = len(final_articles) - len(verified_articles)
        if removed_count > 0:
            print(f"⚠️  Removed {removed_count} non-2024 articles")
        
        # Final summary
        total_found = len(verified_articles)
        target = 227
        
        print("\n" + "=" * 80)
        print("📊 COMPREHENSIVE CRAWL FINAL SUMMARY")
        print("=" * 80)
        print(f"🎯 Target articles: {target}")
        print(f"📊 Found articles: {total_found}")
        print(f"📈 Coverage: {total_found/target*100:.1f}%")
        
        if total_found >= target:
            print("🎉 SUCCESS: Found all expected articles!")
        else:
            missing = target - total_found
            print(f"⚠️  Still missing: {missing} articles ({missing/target*100:.1f}%)")
        
        # Save comprehensive results
        results = {
            'crawl_date': datetime.now().isoformat(),
            'target_year': 2024,
            'target_articles': target,
            'total_found': total_found,
            'coverage_percentage': round(total_found/target*100, 1),
            'all_articles': verified_articles,
            'sources_merged': [
                'corrected_2024_crawl',
                'missing_dates_investigation', 
                'comprehensive_month_search'
            ]
        }
        
        output_path = Path(config.OUTPUT_DIR) / "metadata" / "comprehensive_2024_crawl.json"
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        
        print(f"\n📋 Comprehensive results saved to: {output_path}")
        
        return results

def main():
    crawler = Comprehensive2024Crawler()
    results = crawler.run_comprehensive_crawl()
    
    print(f"\n🎯 FINAL ASSESSMENT:")
    print(f"   📊 Found {results['total_found']}/227 articles ({results['coverage_percentage']}%)")
    
    if results['coverage_percentage'] >= 95:
        print("   🎉 EXCELLENT: Ready for comparison with local files!")
    elif results['coverage_percentage'] >= 85:
        print("   ✅ GOOD: Substantial coverage achieved")
    else:
        print("   🔍 May need additional investigation strategies")

if __name__ == "__main__":
    main()
