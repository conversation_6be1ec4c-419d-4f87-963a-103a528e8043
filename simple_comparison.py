#!/usr/bin/env python3
"""
Simple Article Comparison

Direct comparison between extracted URLs and downloaded filenames
to identify missing articles for download.
"""

import json
import re
from pathlib import Path
from urllib.parse import urlparse
from typing import List, Dict, Set

def load_extracted_articles() -> List[Dict]:
    """Load extracted articles from JSON"""
    extraction_file = Path("output/metadata/complete_html_extraction.json")
    
    if not extraction_file.exists():
        print(f"❌ Extraction file not found: {extraction_file}")
        return []
    
    with open(extraction_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    return data.get('all_articles', [])

def get_downloaded_filenames() -> Set[str]:
    """Get all downloaded HTML filenames"""
    archive_dir = Path("output/archive")
    
    if not archive_dir.exists():
        print(f"❌ Archive directory not found: {archive_dir}")
        return set()
    
    # Get all HTML files
    html_files = list(archive_dir.rglob("*.html"))
    
    # Extract just the filenames (without path and extension)
    filenames = set()
    for html_file in html_files:
        # Remove the date prefix (YYYY_MM_DD_) to get the title part
        filename = html_file.stem
        if re.match(r'^\d{4}_\d{2}_\d{2}_', filename):
            title_part = re.sub(r'^\d{4}_\d{2}_\d{2}_', '', filename)
            filenames.add(title_part.lower())  # Normalize to lowercase
    
    return filenames

def url_to_filename(url: str) -> str:
    """Convert URL to expected filename format"""
    # Extract the path from URL
    parsed = urlparse(url)
    path = parsed.path
    
    # Get the last part (filename without .html)
    if path.endswith('.html'):
        filename = path.split('/')[-1][:-5]  # Remove .html
    else:
        filename = path.split('/')[-1]
    
    # Normalize: replace underscores with hyphens, convert to lowercase
    filename = filename.replace('_', '-').lower()
    
    return filename

def find_missing_articles() -> List[Dict]:
    """Find articles that haven't been downloaded"""
    print("🔍 Loading extracted articles...")
    extracted_articles = load_extracted_articles()
    print(f"✅ Loaded {len(extracted_articles)} extracted articles")
    
    print("🔍 Scanning downloaded files...")
    downloaded_filenames = get_downloaded_filenames()
    print(f"✅ Found {len(downloaded_filenames)} downloaded files")
    
    print("🔍 Comparing extracted vs downloaded...")
    
    missing_articles = []
    found_count = 0
    
    for article in extracted_articles:
        url = article['url']
        expected_filename = url_to_filename(url)
        
        # Check if this filename exists in downloaded files
        if expected_filename in downloaded_filenames:
            found_count += 1
        else:
            missing_articles.append(article)
    
    print(f"📊 Comparison Results:")
    print(f"   📋 Total Extracted: {len(extracted_articles)}")
    print(f"   ✅ Found Downloaded: {found_count}")
    print(f"   ❌ Missing: {len(missing_articles)}")
    
    return missing_articles

def save_missing_report(missing_articles: List[Dict]):
    """Save missing articles report"""
    
    # Group by year
    missing_by_year = {}
    for article in missing_articles:
        year = article.get('year', 'unknown')
        if year not in missing_by_year:
            missing_by_year[year] = []
        missing_by_year[year].append(article)
    
    # Create report
    report = {
        'comparison_date': '2025-07-06',
        'total_missing': len(missing_articles),
        'missing_by_year': missing_by_year,
        'missing_articles': missing_articles
    }
    
    # Save report
    output_path = Path("output/metadata/simple_missing_report.json")
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(report, f, indent=2, ensure_ascii=False)
    
    print(f"\n📋 Missing articles report saved to: {output_path}")
    
    # Print summary
    print("\n" + "=" * 60)
    print("📊 MISSING ARTICLES BY YEAR")
    print("=" * 60)
    
    total_missing = 0
    for year in sorted(missing_by_year.keys(), reverse=True):
        count = len(missing_by_year[year])
        total_missing += count
        print(f"   📅 {year}: {count} missing articles")
    
    print(f"\n   📊 TOTAL MISSING: {total_missing} articles")
    
    # Show some examples
    if missing_articles:
        print(f"\n📋 First 5 missing articles:")
        for i, article in enumerate(missing_articles[:5], 1):
            title = article['title'][:60] + "..." if len(article['title']) > 60 else article['title']
            print(f"   {i}. [{article['year']}] {title}")
    
    return report

def main():
    print("=" * 80)
    print("🎯 SIMPLE ARTICLE COMPARISON")
    print("=" * 80)
    
    # Find missing articles
    missing_articles = find_missing_articles()
    
    if not missing_articles:
        print("\n🎉 All extracted articles have been downloaded!")
        return
    
    # Save report
    report = save_missing_report(missing_articles)
    
    print(f"\n🎯 Next Steps:")
    print(f"   1. Review the missing articles report")
    print(f"   2. Use html_based_downloader.py to download missing articles")
    print(f"   3. Or run the comparison again to verify results")

if __name__ == "__main__":
    main()
