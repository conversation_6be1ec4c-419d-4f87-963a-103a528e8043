#!/usr/bin/env python3
"""
Sidebar Date Extractor

The sidebar structure is:
- Year (e.g., 2024 (227)) - needs to be EXPANDED
- Then shows dates like: 12/26 (2), 12/09 (5), 11/15 (3), etc.
- Each date has a number of articles

This script will:
1. Load the homepage
2. Find and EX<PERSON><PERSON> the 2024 year in the sidebar
3. Extract all date entries like 12/26 (2), 12/09 (5)
4. Sum up to verify we get 227 total articles
5. Use this data to crawl each date with pagination
"""

import requests
import json
import re
import time
from pathlib import Path
from typing import List, Dict, Tuple
from datetime import datetime
from bs4 import BeautifulSoup
from urllib.parse import urljoin
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException

import config

class SidebarDateExtractor:
    def __init__(self, target_year: int = 2024):
        self.target_year = target_year
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        self.date_counts = {}  # date_string -> count
        self.driver = None
        
    def setup_selenium(self):
        """Setup Selenium WebDriver for JavaScript interactions"""
        print("🔧 Setting up Selenium WebDriver...")
        
        chrome_options = Options()
        chrome_options.add_argument('--headless')  # Run in background
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--window-size=1920,1080')
        chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36')
        
        try:
            self.driver = webdriver.Chrome(options=chrome_options)
            print("✅ Selenium WebDriver ready")
            return True
        except Exception as e:
            print(f"❌ Failed to setup Selenium: {e}")
            return False
    
    def extract_expanded_sidebar_dates(self) -> Dict[str, int]:
        """Extract all date entries from expanded sidebar"""
        print(f"📊 Extracting expanded sidebar dates for {self.target_year}...")
        
        if not self.setup_selenium():
            print("❌ Cannot proceed without Selenium")
            return {}
        
        try:
            # Load the homepage
            print("🌐 Loading homepage...")
            self.driver.get(config.BASE_URL)
            
            # Wait for page to load
            WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.ID, "BlogArchive1"))
            )
            
            # Find the year link/element for our target year
            print(f"🔍 Looking for {self.target_year} in sidebar...")
            
            # Try different selectors to find the year
            year_selectors = [
                f"//a[contains(text(), '{self.target_year}')]",
                f"//span[contains(text(), '{self.target_year}')]",
                f"//*[contains(text(), '{self.target_year} (')]",
                f"//a[contains(@href, '{self.target_year}')]"
            ]
            
            year_element = None
            for selector in year_selectors:
                try:
                    elements = self.driver.find_elements(By.XPATH, selector)
                    for elem in elements:
                        if str(self.target_year) in elem.text and '(' in elem.text:
                            year_element = elem
                            print(f"✅ Found year element: {elem.text}")
                            break
                    if year_element:
                        break
                except:
                    continue
            
            if not year_element:
                print("❌ Could not find year element in sidebar")
                # Let's examine the sidebar structure
                print("🔍 Examining sidebar structure...")
                sidebar = self.driver.find_element(By.ID, "BlogArchive1")
                print("Sidebar HTML:")
                print(sidebar.get_attribute('innerHTML')[:1000])
                return {}
            
            # Click to expand the year
            print(f"🖱️  Clicking to expand {self.target_year}...")
            self.driver.execute_script("arguments[0].click();", year_element)
            
            # Wait a moment for expansion
            time.sleep(2)
            
            # Now extract all date entries
            print("📅 Extracting date entries...")
            
            # Get the updated sidebar content
            sidebar = self.driver.find_element(By.ID, "BlogArchive1")
            sidebar_html = sidebar.get_attribute('innerHTML')
            
            # Parse with BeautifulSoup for easier extraction
            soup = BeautifulSoup(sidebar_html, 'html.parser')
            
            # Look for date patterns like "12/26 (2)", "12/09 (5)"
            date_pattern = re.compile(r'(\d{1,2})/(\d{1,2})\s*\((\d+)\)')
            
            # Extract from text content
            sidebar_text = soup.get_text()
            date_matches = date_pattern.findall(sidebar_text)
            
            date_counts = {}
            total_articles = 0
            
            for month_str, day_str, count_str in date_matches:
                month = int(month_str)
                day = int(day_str)
                count = int(count_str)
                
                date_key = f"{month:02d}/{day:02d}"
                date_counts[date_key] = count
                total_articles += count
                
                print(f"   📅 {date_key}: {count} articles")
            
            print(f"✅ Extracted {len(date_counts)} dates with {total_articles} total articles")
            
            # Verify against expected count
            if total_articles == 227:
                print("🎉 Perfect match! Found exactly 227 articles as expected")
            else:
                print(f"⚠️  Count mismatch: Found {total_articles}, expected 227")
            
            return date_counts
            
        except Exception as e:
            print(f"❌ Error extracting sidebar dates: {e}")
            return {}
        
        finally:
            if self.driver:
                self.driver.quit()
    
    def crawl_date_with_pagination(self, date_str: str, expected_count: int) -> List[Dict]:
        """Crawl articles for a specific date using pagination"""
        print(f"📅 Crawling {date_str} - Expected: {expected_count} articles")
        
        month, day = date_str.split('/')
        articles = []
        
        # Build date-specific URLs
        date_urls = [
            f"https://gerrysdiamondsettingessays.blogspot.com/search?updated-min={self.target_year}-{month}-{day}T00:00:00-05:00&updated-max={self.target_year}-{month}-{day}T23:59:59-05:00",
            f"https://gerrysdiamondsettingessays.blogspot.com/{self.target_year}/{month}/{day}/",
            f"https://gerrysdiamondsettingessays.blogspot.com/{self.target_year}_{month}_{day}_archive.html"
        ]
        
        for url_idx, base_url in enumerate(date_urls, 1):
            print(f"   🔗 Method {url_idx}: {base_url[:80]}...")
            
            start_index = 1
            max_results = 50  # Higher limit for date-specific searches
            consecutive_empty = 0
            method_articles = []
            
            while consecutive_empty < 2:
                if "search?" in base_url:
                    paginated_url = f"{base_url}&start-index={start_index}&max-results={max_results}"
                else:
                    if start_index == 1:
                        paginated_url = base_url
                    else:
                        paginated_url = f"{base_url}?start-index={start_index}&max-results={max_results}"
                
                try:
                    response = self.session.get(paginated_url, timeout=20)
                    
                    if response.status_code == 200:
                        soup = BeautifulSoup(response.content, 'html.parser')
                        post_containers = soup.select(config.SELECTORS["post_container"])
                        
                        if post_containers:
                            page_articles = 0
                            for container in post_containers:
                                try:
                                    title_elem = container.select_one(config.SELECTORS["post_title"])
                                    if title_elem and title_elem.get('href'):
                                        url = title_elem.get('href')
                                        title = title_elem.get_text(strip=True)
                                        
                                        # Verify it's from target year
                                        year_match = re.search(r'/(\d{4})/', url)
                                        if year_match and int(year_match.group(1)) == self.target_year:
                                            article = {
                                                'url': url,
                                                'title': title,
                                                'date': date_str,
                                                'method': f'method{url_idx}'
                                            }
                                            
                                            # Avoid duplicates
                                            if not any(a['url'] == url for a in method_articles):
                                                method_articles.append(article)
                                                page_articles += 1
                                                print(f"      ✅ {len(method_articles):2d}: {title[:50]}...")
                                except:
                                    continue
                            
                            if page_articles == 0:
                                consecutive_empty += 1
                            else:
                                consecutive_empty = 0
                        else:
                            consecutive_empty += 1
                    else:
                        consecutive_empty += 1
                        
                except Exception as e:
                    consecutive_empty += 1
                    print(f"      ❌ Error: {e}")
                
                start_index += max_results
                time.sleep(0.3)
                
                if start_index > 200:  # Safety limit
                    break
            
            # Add unique articles from this method
            for article in method_articles:
                if not any(a['url'] == article['url'] for a in articles):
                    articles.append(article)
            
            print(f"      ✅ Method {url_idx}: {len(method_articles)} articles")
            
            # If we found enough, stop trying other methods
            if len(articles) >= expected_count:
                break
        
        return articles
    
    def run_complete_date_extraction(self) -> Dict:
        """Run complete date-based extraction"""
        print("=" * 80)
        print(f"📅 SIDEBAR DATE EXTRACTOR FOR {self.target_year}")
        print("=" * 80)
        print("🎯 Goal: Extract ALL dates from expanded sidebar and crawl each date")
        print()
        
        # Step 1: Extract date counts from expanded sidebar
        self.date_counts = self.extract_expanded_sidebar_dates()
        
        if not self.date_counts:
            print("❌ Could not extract date counts from sidebar")
            return {}
        
        total_expected = sum(self.date_counts.values())
        print(f"📊 Total expected articles: {total_expected}")
        
        # Step 2: Crawl each date
        all_articles = []
        dates_ok = 0
        dates_missing = 0
        
        for date_str in sorted(self.date_counts.keys(), reverse=True):  # Latest first
            expected_count = self.date_counts[date_str]
            articles = self.crawl_date_with_pagination(date_str, expected_count)
            
            found_count = len(articles)
            
            if found_count >= expected_count:
                status = "✅ OK"
                dates_ok += 1
            else:
                status = f"⚠️  MISSING {expected_count - found_count}"
                dates_missing += 1
            
            all_articles.extend(articles)
            print(f"   📊 {date_str}: {found_count}/{expected_count} articles - {status}")
        
        # Step 3: Summary
        total_found = len(all_articles)
        
        print("\n" + "=" * 80)
        print("📊 DATE EXTRACTION SUMMARY")
        print("=" * 80)
        print(f"📅 Total dates: {len(self.date_counts)}")
        print(f"📊 Expected articles: {total_expected}")
        print(f"📊 Found articles: {total_found}")
        print(f"✅ Dates OK: {dates_ok}")
        print(f"⚠️  Dates missing: {dates_missing}")
        
        if total_found >= total_expected:
            print("🎉 SUCCESS: Found all expected articles!")
        else:
            print(f"⚠️  MISSING: {total_expected - total_found} articles")
        
        # Step 4: Save results
        results = {
            'extraction_date': datetime.now().isoformat(),
            'target_year': self.target_year,
            'total_expected': total_expected,
            'total_found': total_found,
            'dates_ok': dates_ok,
            'dates_missing': dates_missing,
            'date_counts': self.date_counts,
            'all_articles': all_articles
        }
        
        output_path = Path(config.OUTPUT_DIR) / "metadata" / f"sidebar_date_extraction_{self.target_year}.json"
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        
        print(f"\n📋 Results saved to: {output_path}")
        
        return results

def main():
    target_year = 2024
    
    extractor = SidebarDateExtractor(target_year)
    results = extractor.run_complete_date_extraction()
    
    if results:
        print(f"\n🎯 SUMMARY:")
        print(f"   📊 Found {results['total_found']} articles out of {results['total_expected']} expected")
        print(f"   📅 Processed {len(results['date_counts'])} dates")
        
        if results['total_found'] >= results['total_expected']:
            print("   ✅ Ready for duplicate checking and local comparison!")
        else:
            print("   🔍 Some dates still need investigation")

if __name__ == "__main__":
    main()
