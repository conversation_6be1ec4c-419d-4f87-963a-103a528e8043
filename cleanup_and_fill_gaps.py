#!/usr/bin/env python3
"""
Cleanup and Gap Filling Script

This script:
1. Removes duplicate files (keeping the most recent)
2. Cleans up the index to only include existing files
3. <PERSON>ans the live blog to find missing articles
4. Downloads missing articles systematically
"""

import os
import json
import re
import time
from pathlib import Path
from collections import defaultdict
from typing import Dict, List, Set, Tuple
from datetime import datetime
from bs4 import BeautifulSoup
import requests

import config
from crawler import BlogCrawler

class ArchiveCleanupManager:
    def __init__(self):
        self.archive_dir = Path(config.OUTPUT_DIR) / "archive"
        self.index_file = Path(config.OUTPUT_DIR) / "index.html"
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        
        # Load audit results
        audit_file = Path(config.OUTPUT_DIR) / "metadata" / "physical_audit_report.json"
        with open(audit_file, 'r', encoding='utf-8') as f:
            self.audit_data = json.load(f)
    
    def remove_duplicates(self):
        """Remove duplicate files, keeping the most recent version"""
        print("🧹 Removing duplicate files...")
        
        duplicates = self.audit_data['duplicates']['title_duplicates']
        removed_count = 0
        
        for dup_group in duplicates:
            title = dup_group['title']
            files = dup_group['files']
            
            if len(files) <= 1:
                continue
                
            print(f"📝 Processing duplicates for: {title}")
            
            # Sort files by modification time (keep newest)
            file_stats = []
            for file_path in files:
                try:
                    stat = os.stat(file_path)
                    file_stats.append((file_path, stat.st_mtime))
                except:
                    continue
            
            if len(file_stats) <= 1:
                continue
                
            # Sort by modification time (newest first)
            file_stats.sort(key=lambda x: x[1], reverse=True)
            
            # Keep the newest, remove the rest
            keep_file = file_stats[0][0]
            remove_files = [f[0] for f in file_stats[1:]]
            
            print(f"   ✅ Keeping: {Path(keep_file).name}")
            
            for remove_file in remove_files:
                try:
                    os.remove(remove_file)
                    print(f"   🗑️  Removed: {Path(remove_file).name}")
                    removed_count += 1
                except Exception as e:
                    print(f"   ❌ Failed to remove {remove_file}: {e}")
        
        print(f"🧹 Removed {removed_count} duplicate files")
        return removed_count
    
    def rebuild_clean_index(self):
        """Rebuild index with only existing physical files"""
        print("📋 Rebuilding clean index...")
        
        # Scan current physical files
        physical_articles = []
        
        for year_dir in sorted(self.archive_dir.iterdir()):
            if year_dir.is_dir() and year_dir.name.isdigit():
                year = year_dir.name
                html_files = list(year_dir.glob("*.html"))
                
                for html_file in html_files:
                    try:
                        with open(html_file, 'r', encoding='utf-8') as f:
                            content = f.read()
                            
                        soup = BeautifulSoup(content, 'html.parser')
                        
                        # Extract article info
                        title_elem = soup.find('h1', class_='post-title')
                        title = title_elem.get_text(strip=True) if title_elem else "Unknown Title"
                        
                        original_link = soup.find('a', string='View Original')
                        original_url = original_link['href'] if original_link else ""
                        
                        date_elem = soup.find('span', string=lambda x: x and 'Date:' in x)
                        date = date_elem.get_text().replace('Date:', '').strip() if date_elem else ""
                        
                        # Create relative path
                        relative_path = f"archive/{year}/{html_file.name}"
                        
                        physical_articles.append({
                            'title': title,
                            'author': 'Gerry Lewy',
                            'date': date,
                            'url': original_url,
                            'local_path': html_file,
                            'relative_path': relative_path,
                            'year': int(year)
                        })
                        
                    except Exception as e:
                        print(f"⚠️  Error processing {html_file}: {e}")
                        continue
        
        # Use crawler to generate clean index
        crawler = BlogCrawler()
        crawler.all_posts_info = physical_articles
        crawler.posts_downloaded = len(physical_articles)
        crawler.images_downloaded = 0  # We'll count later if needed
        
        crawler.generate_index()
        
        print(f"📋 Rebuilt index with {len(physical_articles)} articles")
        return len(physical_articles)
    
    def scan_live_blog_systematically(self) -> Dict[str, Dict]:
        """Scan live blog systematically to get all articles"""
        print("🌐 Scanning live blog systematically...")
        
        # Get all archive URLs
        response = self.session.get(config.BASE_URL)
        if not response:
            print("❌ Failed to fetch blog homepage")
            return {}
            
        soup = BeautifulSoup(response.content, 'html.parser')
        archive_links = soup.select('a[href*="_archive.html"]')
        archive_urls = [f"https://gerrysdiamondsettingessays.blogspot.com{link['href']}" 
                       for link in archive_links if link.get('href')]
        
        print(f"📚 Found {len(archive_urls)} archive pages")
        
        all_live_articles = {}
        
        for i, archive_url in enumerate(archive_urls, 1):
            print(f"📖 Scanning archive {i}/{len(archive_urls)}")
            
            try:
                response = self.session.get(archive_url)
                if not response:
                    continue
                    
                soup = BeautifulSoup(response.content, 'html.parser')
                post_containers = soup.select(config.SELECTORS["post_container"])
                
                for container in post_containers:
                    try:
                        title_elem = container.select_one(config.SELECTORS["post_title"])
                        if not title_elem:
                            continue
                            
                        post_url = title_elem.get('href')
                        title = title_elem.get_text(strip=True)
                        
                        # Extract date
                        date_elem = container.select_one(config.SELECTORS["post_date"])
                        date_str = date_elem.get('title') if date_elem else ""
                        
                        # Extract year from URL
                        year_match = re.search(r'/(\d{4})/', post_url)
                        year = int(year_match.group(1)) if year_match else None
                        
                        # Extract snippet
                        content_elem = container.select_one('.post-body')
                        snippet = ""
                        if content_elem:
                            snippet = content_elem.get_text(strip=True)[:200] + "..."
                        
                        all_live_articles[post_url] = {
                            'title': title,
                            'url': post_url,
                            'date': date_str,
                            'year': year,
                            'snippet': snippet,
                            'archive_source': archive_url
                        }
                        
                    except Exception as e:
                        continue
                        
            except Exception as e:
                print(f"⚠️  Error processing {archive_url}: {e}")
                continue
                
            time.sleep(config.REQUEST_DELAY)
        
        print(f"🌐 Found {len(all_live_articles)} total articles on live blog")
        return all_live_articles
    
    def find_missing_articles(self, live_articles: Dict) -> Dict:
        """Find articles that exist on live blog but not in physical archive"""
        print("🔍 Finding missing articles...")
        
        # Get URLs of physical articles
        physical_urls = set()
        
        for year_dir in self.archive_dir.iterdir():
            if year_dir.is_dir() and year_dir.name.isdigit():
                for html_file in year_dir.glob("*.html"):
                    try:
                        with open(html_file, 'r', encoding='utf-8') as f:
                            content = f.read()
                            
                        soup = BeautifulSoup(content, 'html.parser')
                        original_link = soup.find('a', string='View Original')
                        if original_link and original_link.get('href'):
                            physical_urls.add(original_link['href'])
                            
                    except:
                        continue
        
        # Find missing
        live_urls = set(live_articles.keys())
        missing_urls = live_urls - physical_urls
        
        missing_articles = {url: live_articles[url] for url in missing_urls}
        
        print(f"📊 Analysis:")
        print(f"   🌐 Live articles: {len(live_urls)}")
        print(f"   📁 Physical articles: {len(physical_urls)}")
        print(f"   ❌ Missing articles: {len(missing_urls)}")
        
        # Group by year
        missing_by_year = defaultdict(list)
        for article in missing_articles.values():
            year = article.get('year', 'Unknown')
            missing_by_year[year].append(article)
        
        print(f"\n📅 Missing by year:")
        for year in sorted(missing_by_year.keys()):
            print(f"   {year}: {len(missing_by_year[year])} articles")
        
        return missing_articles
    
    def download_missing_articles(self, missing_articles: Dict):
        """Download missing articles systematically"""
        if not missing_articles:
            print("✅ No missing articles to download!")
            return
            
        print(f"⬇️  Downloading {len(missing_articles)} missing articles...")
        
        crawler = BlogCrawler()
        
        # Group by year and process latest first
        missing_by_year = defaultdict(list)
        for article in missing_articles.values():
            year = article.get('year', 2025)  # Default to current year
            missing_by_year[year].append(article)
        
        success_count = 0
        
        for year in sorted(missing_by_year.keys(), reverse=True):
            articles = missing_by_year[year]
            print(f"\n🗓️  Processing {len(articles)} missing articles from {year}")
            
            for i, article in enumerate(articles, 1):
                print(f"   📄 {i}/{len(articles)}: {article['title'][:60]}...")
                
                post = {
                    'url': article['url'],
                    'title': article['title'],
                    'date': article['date'],
                    'author': 'Gerry Lewy'
                }
                
                success = crawler.download_post_content(post)
                if success:
                    success_count += 1
                    print(f"   ✅ Downloaded successfully")
                else:
                    print(f"   ❌ Download failed")
                
                # Update index after each successful download
                if success:
                    crawler.generate_index()
                
                time.sleep(config.REQUEST_DELAY)
        
        print(f"\n✅ Download complete: {success_count}/{len(missing_articles)} successful")

def main():
    print("=" * 80)
    print("🧹 ARCHIVE CLEANUP AND GAP FILLING")
    print("=" * 80)

    try:
        manager = ArchiveCleanupManager()
        print("✅ Manager initialized successfully")
    except Exception as e:
        print(f"❌ Error initializing manager: {e}")
        return
    
    # Step 1: Remove duplicates
    removed = manager.remove_duplicates()
    
    # Step 2: Rebuild clean index
    clean_count = manager.rebuild_clean_index()
    
    # Step 3: Scan live blog
    live_articles = manager.scan_live_blog_systematically()
    
    # Step 4: Find missing articles
    missing_articles = manager.find_missing_articles(live_articles)
    
    # Step 5: Ask user about downloading missing articles
    if missing_articles:
        print(f"\n🤔 Found {len(missing_articles)} missing articles.")
        response = input("Download them now? (y/N): ")
        if response.lower() == 'y':
            manager.download_missing_articles(missing_articles)
        else:
            print("📋 Missing articles analysis complete. Run again to download.")
    
    print("\n✅ Cleanup and analysis complete!")

if __name__ == "__main__":
    main()
