<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TEXT ONLY, for "Princess-Cut" Diamond Setting.</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #fafafa;
            padding: 20px;
        }

        .container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .post-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px;
            text-align: center;
        }

        .post-title {
            font-size: 2.2em;
            font-weight: 700;
            margin-bottom: 15px;
            line-height: 1.2;
        }

        .post-meta {
            font-size: 0.95em;
            opacity: 0.9;
            display: flex;
            justify-content: center;
            gap: 20px;
            flex-wrap: wrap;
        }

        .post-meta a {
            color: #fff;
            text-decoration: underline;
        }

        .post-content {
            padding: 40px;
            font-size: 1.1em;
            line-height: 1.8;
        }

        /* Font normalization - override all inline styles */
        .post-content * {
            font-family: inherit !important;
            font-size: inherit !important;
            line-height: inherit !important;
        }

        .post-content p {
            margin-bottom: 1.2em;
            font-size: 1.1em !important;
        }

        .post-content div {
            font-size: 1.1em !important;
        }

        .post-content span {
            font-size: inherit !important;
        }

        .post-content img {
            max-width: 100%;
            height: auto;
            border-radius: 8px;
            margin: 20px 0;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
        }

        .post-content h1, .post-content h2, .post-content h3 {
            margin: 1.5em 0 0.8em 0;
            color: #2c3e50;
            font-size: 1.4em !important;
            font-weight: 600 !important;
        }

        .post-content strong, .post-content b {
            color: #2c3e50;
            font-weight: 600 !important;
        }

        .post-content em, .post-content i {
            color: #555;
            font-style: italic !important;
        }

        .separator {
            margin: 2em 0;
        }

        .back-link {
            position: fixed;
            top: 20px;
            left: 20px;
            background: #667eea;
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: 500;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;
        }

        .back-link:hover {
            background: #5a6fd8;
            transform: translateY(-2px);
        }

        @media (max-width: 768px) {
            .container {
                margin: 10px;
                border-radius: 8px;
            }

            .post-header {
                padding: 30px 20px;
            }

            .post-title {
                font-size: 1.8em;
            }

            .post-content {
                padding: 30px 20px;
            }

            .post-meta {
                flex-direction: column;
                gap: 10px;
            }
        }
    </style>
</head>
<body>
    <a href="../../../index.html" class="back-link">← Back to Index</a>

    <div class="container">
        <div class="post-header">
            <h1 class="post-title">TEXT ONLY, for "Princess-Cut" Diamond Setting.</h1>
            <div class="post-meta">
                <span>By: Posted byGerry Lewy</span>
                <span>Date: 2023-02-21T16:39:00-05:00</span>
                <span><a href="https://gerrysdiamondsettingessays.blogspot.com/2023/02/text-only-for-princess-cut-diamond.html" target="_blank">View Original</a></span>
            </div>
        </div>
        <div class="post-content">
            <div class="post-body entry-content" id="post-body-1878640682260471713" itemprop="description articleBody">
<p></p><div class="separator" style="clear: both; text-align: center;"><br/><div style="text-align: justify;">    <i><b><span style="font-size: medium;">In this tutorial essay there are many very detailed facts for setting a Princess Diamond. There are 3 pages of notes for you to read through. My blog has photos just on this topic that will greatly assist you in the reading of this text essay.    </span>        </b></i></div></div><div class="separator" style="clear: both; text-align: right;"><br/></div>================================================<br/><div class="separator" style="clear: both; text-align: center;"><a href="https://blogger.googleusercontent.com/img/b/R29vZ2xl/AVvXsEjDqae3DF-Rq-KVfThhpWddihPTOKMiXnJZPw_ounEuRU_2Kpz0pmGk-UpsFa0OqWBEPOxgGk3q4yKd0iEneBH6cbFkLhFS_u_J71M8oaORpGDl8_-imo53FyPtjydwXZV_8btrzUBNz_X9dgNvlIfUhsq38Jm8vFwwoJHMktrG4Osq-DlV0cre-wY4/s1830/Princess%20Stone%20Setting,%20%231%20001.bmp" style="margin-left: 1em; margin-right: 1em;"><img border="0" data-original-height="1830" data-original-width="1606" height="640" loading="lazy" src="../../images/unknown/TEXT-ONLY-for-Princess-Cut-Diamond-Setting._img_001.png" width="562"/></a></div>-----------------------------------------------------------------------------------<br/><div class="separator" style="clear: both; text-align: center;"><a href="https://blogger.googleusercontent.com/img/b/R29vZ2xl/AVvXsEgPdZcA7V0gekiNyz1zJVLUq1o6bcZ-gNG6Lpk_vd2-iogYF7rcITO0jWkXj07Xa86x4hWp1WeJIKpGPpYQnfTQHvmg6PYhN1kkVjW5sSGD7XD3xhfSduX4wvAgc2mjB3WzxU_qhegy0Vew3ei8MoyXnNf1o5hmsGFcxE3PLY4eRnJl04HFZpgmbtty/s1803/Princess%20Stone%20Setting%20%232%20001.bmp" style="margin-left: 1em; margin-right: 1em;"><img border="0" data-original-height="1803" data-original-width="1468" height="640" loading="lazy" src="../../images/unknown/TEXT-ONLY-for-Princess-Cut-Diamond-Setting._img_002.png" width="522"/></a></div>-----------------------------------------------------------------------------------------<br/><div class="separator" style="clear: both; text-align: center;"><a href="https://blogger.googleusercontent.com/img/b/R29vZ2xl/AVvXsEjVh2Q9vRpvdShiy3KwWyGq2xmOTm4zDgC8S8R6kBf_XW7GHPB7BVTnTN08KIdmTQMWafY-q65RHcVEg7TfwSjdwN4R8IXlkoKD1ZKM_oVO24IHO7zjxO3Bgmw9-I2KvpPo2GeMsEPBhm7oi1OxRHAMM48e7kFUbSFhxGSCZNfMReohFmCRWL7oklH3/s1401/Princess%20Stone%20Setting%20%233%20001.bmp" style="margin-left: 1em; margin-right: 1em;"><img border="0" data-original-height="976" data-original-width="1401" height="446" loading="lazy" src="../../images/unknown/TEXT-ONLY-for-Princess-Cut-Diamond-Setting._img_003.png" width="640"/></a></div><br/><div class="separator" style="clear: both; text-align: center;"><br/></div><br/> <p></p>
<div style="clear: both;"></div>
</div>
        </div>
    </div>
</body>
</html>