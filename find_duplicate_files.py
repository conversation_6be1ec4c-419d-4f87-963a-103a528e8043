#!/usr/bin/env python3
"""
Find exact duplicate files and identify extra files not in master index
"""

import json
import re
from pathlib import Path
from collections import defaultdict

class DuplicateFinder:
    def __init__(self):
        self.archive_dir = Path("output/archive")
        self.master_file = Path("html.txt")
        
    def get_master_articles(self):
        """Get master article list with URLs"""
        with open(self.master_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        pattern = r'<li><a href="(https://gerrysdiamondsettingessays\.blogspot\.com/([0-9]{4})/[0-9]{2}/[^"]+\.html)"[^>]*>([^<]+)</a></li>'
        matches = re.findall(pattern, content)
        
        master_urls = set()
        for url, year, title in matches:
            master_urls.add(url)
        
        return master_urls
    
    def extract_url_from_file(self, file_path):
        """Extract original URL from HTML file content"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Look for the original URL in the HTML content
            url_match = re.search(r'https://gerrysdiamondsettingessays\.blogspot\.com/[0-9]{4}/[0-9]{2}/[^"\s<>]+\.html', content)
            
            if url_match:
                return url_match.group(0)
            return None
            
        except Exception as e:
            print(f"   ❌ Error reading {file_path.name}: {e}")
            return None
    
    def find_duplicates_and_extras(self):
        """Find duplicate files and files not in master index"""
        print("=" * 80)
        print("🔍 FINDING DUPLICATES AND EXTRA FILES")
        print("=" * 80)
        
        master_urls = self.get_master_articles()
        print(f"📋 Master index contains {len(master_urls)} unique URLs")
        
        # Track files by their original URL
        url_to_files = defaultdict(list)
        files_without_urls = []
        extra_files = []
        
        print("📁 Scanning all physical files...")
        
        for year_dir in self.archive_dir.iterdir():
            if year_dir.is_dir() and year_dir.name.isdigit():
                for html_file in year_dir.glob("*.html"):
                    original_url = self.extract_url_from_file(html_file)
                    
                    if original_url:
                        url_to_files[original_url].append(html_file)
                        
                        # Check if this URL is in master index
                        if original_url not in master_urls:
                            extra_files.append({
                                'file': html_file,
                                'url': original_url,
                                'year': year_dir.name
                            })
                    else:
                        files_without_urls.append(html_file)
        
        # Find duplicates (same URL, multiple files)
        duplicates = {url: files for url, files in url_to_files.items() if len(files) > 1}
        
        print(f"\n📊 ANALYSIS RESULTS:")
        print(f"   🔗 Files with extractable URLs: {sum(len(files) for files in url_to_files.values())}")
        print(f"   ❓ Files without URLs: {len(files_without_urls)}")
        print(f"   🔄 Duplicate URLs: {len(duplicates)}")
        print(f"   ➕ Extra files (not in master): {len(extra_files)}")
        
        if duplicates:
            print(f"\n🔄 DUPLICATE FILES:")
            for url, files in duplicates.items():
                print(f"\n   URL: {url}")
                for i, file_path in enumerate(files, 1):
                    size = file_path.stat().st_size
                    print(f"      {i}. {file_path.name} ({size:,} bytes) in {file_path.parent.name}/")
        
        if extra_files:
            print(f"\n➕ EXTRA FILES (not in master index):")
            for extra in extra_files:
                size = extra['file'].stat().st_size
                print(f"   • {extra['file'].name} ({size:,} bytes)")
                print(f"     URL: {extra['url']}")
                print(f"     Year: {extra['year']}")
                print()
        
        if files_without_urls:
            print(f"\n❓ FILES WITHOUT EXTRACTABLE URLs:")
            for file_path in files_without_urls:
                size = file_path.stat().st_size
                print(f"   • {file_path.name} ({size:,} bytes) in {file_path.parent.name}/")
        
        # Save detailed report
        report = {
            'total_files_scanned': sum(len(files) for files in url_to_files.values()) + len(files_without_urls),
            'master_urls_count': len(master_urls),
            'duplicates': {
                url: [str(f) for f in files] for url, files in duplicates.items()
            },
            'extra_files': [
                {
                    'file': str(extra['file']),
                    'url': extra['url'],
                    'year': extra['year']
                }
                for extra in extra_files
            ],
            'files_without_urls': [str(f) for f in files_without_urls]
        }
        
        report_path = Path("output/metadata/duplicate_analysis_report.json")
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        print(f"\n💾 Detailed report saved to: {report_path}")
        
        # Summary
        total_issues = len(duplicates) + len(extra_files) + len(files_without_urls)
        if total_issues == 0:
            print(f"\n🎉 SUCCESS: No duplicates or extra files found!")
        else:
            print(f"\n⚠️  SUMMARY: Found {total_issues} issues to investigate")
            if duplicates:
                print(f"   - {len(duplicates)} URLs have duplicate files")
            if extra_files:
                print(f"   - {len(extra_files)} files not in master index")
            if files_without_urls:
                print(f"   - {len(files_without_urls)} files without extractable URLs")
        
        print("=" * 80)

if __name__ == "__main__":
    finder = DuplicateFinder()
    finder.find_duplicates_and_extras()
