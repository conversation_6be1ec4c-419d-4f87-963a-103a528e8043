#!/usr/bin/env python3
"""
Correct Article Comparison

Properly match extracted articles with downloaded files by examining
the actual content and titles, not just URL patterns.
"""

import json
import re
from pathlib import Path
from typing import List, Dict, Set
from bs4 import BeautifulSoup

def load_extracted_articles() -> List[Dict]:
    """Load extracted articles from JSON"""
    extraction_file = Path("output/metadata/complete_html_extraction.json")
    
    with open(extraction_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    return data.get('all_articles', [])

def get_downloaded_articles_info() -> List[Dict]:
    """Get information about downloaded articles by reading their HTML content"""
    archive_dir = Path("output/archive")
    downloaded_info = []
    
    # Get all HTML files
    html_files = list(archive_dir.rglob("*.html"))
    print(f"📊 Scanning {len(html_files)} downloaded HTML files...")
    
    for i, html_file in enumerate(html_files, 1):
        if i % 100 == 0:
            print(f"   📄 Processed {i}/{len(html_files)} files...")
            
        try:
            # Read the HTML file
            with open(html_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Parse with BeautifulSoup to extract title
            soup = BeautifulSoup(content, 'html.parser')
            
            # Try to find the title
            title = None
            
            # Look for title in <title> tag
            title_tag = soup.find('title')
            if title_tag:
                title = title_tag.get_text(strip=True)
            
            # If no title found, try h1
            if not title:
                h1_tag = soup.find('h1')
                if h1_tag:
                    title = h1_tag.get_text(strip=True)
            
            # If still no title, use filename
            if not title:
                title = html_file.stem
            
            # Clean up title
            title = title.replace('Gerry\'s Diamond Setting Essays: ', '')
            title = title.strip()
            
            # Extract year from file path
            year = html_file.parent.name
            
            downloaded_info.append({
                'file_path': str(html_file),
                'filename': html_file.name,
                'title': title,
                'year': year,
                'title_normalized': normalize_title(title)
            })
            
        except Exception as e:
            print(f"   ❌ Error reading {html_file}: {e}")
            continue
    
    print(f"✅ Processed {len(downloaded_info)} downloaded articles")
    return downloaded_info

def normalize_title(title: str) -> str:
    """Normalize title for comparison"""
    # Remove common prefixes/suffixes
    title = re.sub(r'^Gerry\'s Diamond Setting Essays:\s*', '', title, flags=re.IGNORECASE)
    
    # Remove special characters and normalize spacing
    title = re.sub(r'[^\w\s]', ' ', title)
    title = re.sub(r'\s+', ' ', title)
    title = title.strip().lower()
    
    return title

def find_matches_and_missing(extracted_articles: List[Dict], downloaded_info: List[Dict]) -> tuple:
    """Find matches between extracted and downloaded articles"""
    
    print("🔍 Matching extracted articles with downloaded files...")
    
    # Create lookup for downloaded articles
    downloaded_lookup = {}
    for info in downloaded_info:
        key = f"{info['year']}:{info['title_normalized']}"
        downloaded_lookup[key] = info
    
    matches = []
    missing = []
    
    for article in extracted_articles:
        # Normalize the extracted article title
        normalized_title = normalize_title(article['title'])
        key = f"{article['year']}:{normalized_title}"
        
        # Try exact match first
        if key in downloaded_lookup:
            matches.append({
                'extracted': article,
                'downloaded': downloaded_lookup[key],
                'match_type': 'exact'
            })
        else:
            # Try fuzzy matching within the same year
            year_matches = [info for info in downloaded_info if info['year'] == article['year']]
            
            best_match = None
            best_score = 0
            
            for info in year_matches:
                # Simple word overlap scoring
                extracted_words = set(normalized_title.split())
                downloaded_words = set(info['title_normalized'].split())
                
                if len(extracted_words) > 0:
                    overlap = len(extracted_words & downloaded_words)
                    score = overlap / len(extracted_words)
                    
                    if score > 0.7 and score > best_score:  # 70% word overlap threshold
                        best_match = info
                        best_score = score
            
            if best_match:
                matches.append({
                    'extracted': article,
                    'downloaded': best_match,
                    'match_type': 'fuzzy',
                    'score': best_score
                })
            else:
                missing.append(article)
    
    return matches, missing

def save_detailed_report(matches: List[Dict], missing: List[Dict], downloaded_info: List[Dict]):
    """Save detailed comparison report"""
    
    # Group missing by year
    missing_by_year = {}
    for article in missing:
        year = article.get('year', 'unknown')
        if year not in missing_by_year:
            missing_by_year[year] = []
        missing_by_year[year].append(article)
    
    # Create comprehensive report
    report = {
        'comparison_date': '2025-07-06',
        'summary': {
            'total_extracted': len(matches) + len(missing),
            'total_downloaded': len(downloaded_info),
            'total_matches': len(matches),
            'total_missing': len(missing),
            'exact_matches': len([m for m in matches if m['match_type'] == 'exact']),
            'fuzzy_matches': len([m for m in matches if m['match_type'] == 'fuzzy'])
        },
        'missing_by_year': missing_by_year,
        'missing_articles': missing,
        'matches': matches[:10],  # Save first 10 matches as examples
        'validation': {
            'logic_check': f"Downloaded({len(downloaded_info)}) + Missing({len(missing)}) should approximately equal Extracted({len(matches) + len(missing)})",
            'is_valid': abs((len(downloaded_info) + len(missing)) - (len(matches) + len(missing))) <= 10
        }
    }
    
    # Save report
    output_path = Path("output/metadata/correct_comparison_report.json")
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(report, f, indent=2, ensure_ascii=False)
    
    print(f"\n📋 Detailed comparison report saved to: {output_path}")
    
    return report

def main():
    print("=" * 80)
    print("🎯 CORRECT ARTICLE COMPARISON")
    print("=" * 80)
    
    # Load extracted articles
    print("📂 Loading extracted articles...")
    extracted_articles = load_extracted_articles()
    print(f"✅ Loaded {len(extracted_articles)} extracted articles")
    
    # Get downloaded articles info
    print("\n📂 Analyzing downloaded articles...")
    downloaded_info = get_downloaded_articles_info()
    
    # Find matches and missing
    print(f"\n🔍 Comparing {len(extracted_articles)} extracted vs {len(downloaded_info)} downloaded...")
    matches, missing = find_matches_and_missing(extracted_articles, downloaded_info)
    
    # Print results
    print("\n" + "=" * 60)
    print("📊 COMPARISON RESULTS")
    print("=" * 60)
    print(f"   📋 Total Extracted: {len(extracted_articles)}")
    print(f"   📥 Total Downloaded: {len(downloaded_info)}")
    print(f"   ✅ Matched Articles: {len(matches)}")
    print(f"   ❌ Missing Articles: {len(missing)}")
    print(f"   📊 Match Rate: {len(matches)/len(extracted_articles)*100:.1f}%")
    
    # Validate logic
    expected_missing = len(extracted_articles) - len(downloaded_info)
    print(f"\n🔍 Logic Validation:")
    print(f"   📊 Expected Missing (max): {expected_missing}")
    print(f"   📊 Actual Missing: {len(missing)}")
    print(f"   ✅ Logic Check: {'PASS' if len(missing) <= expected_missing + 10 else 'FAIL'}")
    
    # Save detailed report
    report = save_detailed_report(matches, missing, downloaded_info)
    
    # Show missing by year
    if missing:
        print(f"\n📊 Missing Articles by Year:")
        missing_by_year = {}
        for article in missing:
            year = article.get('year', 'unknown')
            missing_by_year[year] = missing_by_year.get(year, 0) + 1
        
        for year in sorted(missing_by_year.keys(), reverse=True):
            print(f"   📅 {year}: {missing_by_year[year]} missing")
    
    print(f"\n🎯 Summary:")
    if len(missing) == 0:
        print("   🎉 All extracted articles have been downloaded!")
    elif len(missing) <= expected_missing + 10:
        print(f"   ✅ Found {len(missing)} missing articles (within expected range)")
        print(f"   🚀 Ready to download missing articles")
    else:
        print(f"   ⚠️  Found {len(missing)} missing articles (more than expected)")
        print(f"   🔍 May need to review matching logic")

if __name__ == "__main__":
    main()
