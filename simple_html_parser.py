#!/usr/bin/env python3
"""
Simple HTML Parser

Extract all articles from the html.txt file using a straightforward approach.
"""

import json
import re
from pathlib import Path
from datetime import datetime
from bs4 import BeautifulSoup

def parse_html_file():
    """Parse the html.txt file to extract all articles"""
    
    print("🎯 Parsing html.txt file...")
    
    # Load HTML content
    try:
        with open('html.txt', 'r', encoding='utf-8') as f:
            html_content = f.read()
        print(f"✅ Loaded HTML file ({len(html_content)} characters)")
    except Exception as e:
        print(f"❌ Error loading html.txt: {e}")
        return None
    
    # Parse with BeautifulSoup
    soup = BeautifulSoup(html_content, 'html.parser')
    
    # Find all <ul class="posts"> sections
    posts_sections = soup.select('ul.posts')
    print(f"📊 Found {len(posts_sections)} posts sections")
    
    articles_by_year = {}
    all_articles = []
    
    for i, posts_section in enumerate(posts_sections, 1):
        # Find the date link for this posts section
        date_link = posts_section.find_previous('a', class_='post-count-link')
        if not date_link:
            continue
            
        date_text = date_link.get_text(strip=True)
        href = date_link.get('href', '')
        
        # Skip main year links (they don't have _archive.html)
        if '_archive.html' not in href:
            continue
        
        # Extract year from URL
        year_match = re.search(r'/(\d{4})_', href)
        if not year_match:
            continue
            
        year = year_match.group(1)
        
        # Find post count
        count_span = posts_section.find_previous('span', class_='post-count')
        expected_count = 0
        if count_span:
            count_match = re.search(r'\((\d+)\)', count_span.get_text())
            if count_match:
                expected_count = int(count_match.group(1))
        
        # Extract all article links
        article_links = posts_section.select('li a[href]')
        
        print(f"   📅 {year}/{date_text}: {len(article_links)}/{expected_count} articles")
        
        for link in article_links:
            article_href = link.get('href')
            title = link.get_text(strip=True)
            
            # Clean up HTML entities
            title = title.replace('&gt;', '>').replace('&lt;', '<').replace('&amp;', '&')
            
            article = {
                'url': article_href,
                'title': title,
                'year': year,
                'archive_date': date_text,
                'expected_count': expected_count,
                'source': 'simple_html_parser'
            }
            
            all_articles.append(article)
            
            # Group by year
            if year not in articles_by_year:
                articles_by_year[year] = []
            articles_by_year[year].append(article)
    
    return articles_by_year, all_articles

def validate_and_save_results(articles_by_year, all_articles):
    """Validate and save the extraction results"""
    
    expected_totals = {
        '2025': 101,
        '2024': 227,
        '2023': 133,
        '2022': 66,
        '2021': 13,
        '2020': 1
    }
    
    print("\n" + "=" * 60)
    print("📊 VALIDATION RESULTS")
    print("=" * 60)
    
    total_expected = sum(expected_totals.values())
    total_found = len(all_articles)
    
    for year in sorted(expected_totals.keys(), reverse=True):
        expected = expected_totals[year]
        found = len(articles_by_year.get(year, []))
        coverage = (found / expected * 100) if expected > 0 else 0
        status = "✅" if found >= expected else "⚠️"
        
        print(f"   {status} {year}: {found:3d}/{expected:3d} articles ({coverage:5.1f}%)")
    
    overall_coverage = (total_found / total_expected * 100)
    print(f"\n   📊 TOTAL: {total_found:3d}/{total_expected:3d} articles ({overall_coverage:.1f}%)")
    
    # Save results
    results = {
        'extraction_date': datetime.now().isoformat(),
        'source_file': 'html.txt',
        'expected_totals': expected_totals,
        'articles_by_year': articles_by_year,
        'all_articles': all_articles,
        'summary': {
            'total_years': len(articles_by_year),
            'total_articles': len(all_articles),
            'years_processed': sorted(articles_by_year.keys(), reverse=True),
            'overall_coverage': round(overall_coverage, 1)
        }
    }
    
    # Create output directory
    output_dir = Path("output/metadata")
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # Save complete results
    output_path = output_dir / "complete_html_extraction.json"
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    print(f"\n📋 Results saved to: {output_path}")
    
    # Save year-specific files
    for year, articles in articles_by_year.items():
        year_path = output_dir / f"articles_{year}.json"
        year_data = {
            'year': year,
            'extraction_date': datetime.now().isoformat(),
            'expected_count': expected_totals.get(year, 0),
            'actual_count': len(articles),
            'articles': articles
        }
        
        with open(year_path, 'w', encoding='utf-8') as f:
            json.dump(year_data, f, indent=2, ensure_ascii=False)
        
        print(f"   📋 {year}: {len(articles)} articles → {year_path}")
    
    return results

def main():
    print("=" * 80)
    print("🎯 SIMPLE HTML PARSER")
    print("=" * 80)
    print("📂 Parsing html.txt to extract all article links")
    print()
    
    # Parse HTML
    result = parse_html_file()
    if not result:
        print("❌ Parsing failed!")
        return
    
    articles_by_year, all_articles = result
    
    if not articles_by_year:
        print("❌ No articles found!")
        return
    
    # Validate and save
    results = validate_and_save_results(articles_by_year, all_articles)
    
    print("\n" + "=" * 80)
    print("🎯 EXTRACTION COMPLETE")
    print("=" * 80)
    
    if results['summary']['overall_coverage'] >= 95:
        print("🎉 EXCELLENT: Ready for downloading!")
        print(f"✅ Found {results['summary']['total_articles']} articles")
        print("\n🚀 Next step: Run html_based_downloader.py to download all articles")
    else:
        print("⚠️  Some articles may be missing, but proceeding with download")
        print(f"📊 Found {results['summary']['total_articles']} articles")

if __name__ == "__main__":
    main()
