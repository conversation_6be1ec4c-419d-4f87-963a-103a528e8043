#!/usr/bin/env python3
"""
Download the 13 specific missing articles identified by validation
"""

import requests
import sqlite3
import json
import re
import time
from pathlib import Path
from datetime import datetime
from urllib.parse import urljoin, urlparse
from bs4 import BeautifulSoup
import config

class MissingArticleDownloader:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
        
        # The 13 missing articles from validation report
        self.missing_articles = [
            {
                "url": "https://gerrysdiamondsettingessays.blogspot.com/2024/09/links-to-videos-that-are-seen-on-youtube.html",
                "title": "WAX-CARVING TO A SILVER PENDANT => Video link = 53...",
                "year": "2024"
            },
            {
                "url": "https://gerrysdiamondsettingessays.blogspot.com/2024/09/square-on-4-claw-setting-bench-magazine.html",
                "title": "\"Square-On\" 4-claw setting - Bench magazine",
                "year": "2024"
            },
            {
                "url": "https://gerrysdiamondsettingessays.blogspot.com/2024/09/marquise-setting-bench-magazine.html",
                "title": "Marquise Setting - (Bench magazine)",
                "year": "2024"
            },
            {
                "url": "https://gerrysdiamondsettingessays.blogspot.com/2023/12/insanely-difficult-diamond-setting.html",
                "title": "Insanely difficult Diamond Setting projects. (15 p...",
                "year": "2023"
            },
            {
                "url": "https://gerrysdiamondsettingessays.blogspot.com/2023/09/channel-setting-in-26-photos.html",
                "title": "\"Channel Setting\" in 26 photos.",
                "year": "2023"
            },
            {
                "url": "https://gerrysdiamondsettingessays.blogspot.com/2023/09/half-round-burs-for-setting-cabochon.html",
                "title": "\"Half-round\" burs for setting Cabochon stones. (11...",
                "year": "2023"
            },
            {
                "url": "https://gerrysdiamondsettingessays.blogspot.com/2022/12/setting-baguettes.html",
                "title": "Setting Baguettes",
                "year": "2022"
            },
            {
                "url": "https://gerrysdiamondsettingessays.blogspot.com/2018/04/bezel-setting.html",
                "title": "Bezel Setting",
                "year": "2018"
            },
            {
                "url": "https://gerrysdiamondsettingessays.blogspot.com/2018/04/gypsy-setting-magazine-article.html",
                "title": "Gypsy Setting, \"BENCH\" magazine essay!",
                "year": "2018"
            },
            {
                "url": "https://gerrysdiamondsettingessays.blogspot.com/2018/04/gypsy-or-as-its-also-known-as-flush.html",
                "title": "\"Princess, (Square Stone) setting\"",
                "year": "2018"
            },
            {
                "url": "https://gerrysdiamondsettingessays.blogspot.com/2018/04/polishing-papers-for-gravers.html",
                "title": "Polishing Papers for Gravers!",
                "year": "2018"
            },
            {
                "url": "https://gerrysdiamondsettingessays.blogspot.com/2018/04/important-diamond-setting-requirements.html",
                "title": "Important Diamond Setting Diagrams!",
                "year": "2018"
            },
            {
                "url": "https://gerrysdiamondsettingessays.blogspot.com/2018/04/in-bench-magazine-some-many-years-ago.html",
                "title": "BENCH magazine \"Bead Setting'",
                "year": "2018"
            }
        ]
        
        print(f"🎯 Targeting {len(self.missing_articles)} missing articles for download")

    def sanitize_filename(self, title):
        """Convert title to safe filename"""
        safe_title = re.sub(r'[<>:"/\\|?*]', '-', title)
        safe_title = re.sub(r'-+', '-', safe_title).strip('-')
        if len(safe_title) > 100:
            safe_title = safe_title[:100]
        return safe_title

    def download_image(self, img_url, article_title, img_index):
        """Download and convert image to PNG"""
        try:
            response = self.session.get(img_url, timeout=30)
            response.raise_for_status()
            
            # Create filename
            safe_title = self.sanitize_filename(article_title)
            img_filename = f"{safe_title}_img_{img_index:03d}.png"
            img_path = Path("output/images/unknown") / img_filename
            
            # Ensure directory exists
            img_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Save as PNG
            with open(img_path, 'wb') as f:
                f.write(response.content)
            
            return f"../../images/unknown/{img_filename}"
            
        except Exception as e:
            print(f"   ⚠️  Failed to download image {img_url}: {e}")
            return img_url  # Return original URL if download fails

    def process_images_in_content(self, content, article_title):
        """Download images and replace URLs with local paths"""
        soup = BeautifulSoup(content, 'html.parser')
        img_index = 1
        
        # Process img tags
        for img in soup.find_all('img'):
            src = img.get('src')
            if src and 'blogspot.com' in src:
                # Download image and get local path
                local_path = self.download_image(src, article_title, img_index)
                img['src'] = local_path
                img_index += 1
        
        # Process anchor tags that link to images
        for a in soup.find_all('a'):
            href = a.get('href')
            if href and 'blogspot.com' in href and any(ext in href.lower() for ext in ['.jpg', '.jpeg', '.png', '.gif']):
                # Download image and get local path
                local_path = self.download_image(href, article_title, img_index)
                a['href'] = local_path
                img_index += 1
        
        return str(soup)

    def download_article(self, article):
        """Download a single article"""
        url = article['url']
        title = article['title']
        year = article['year']
        
        print(f"\n📄 Downloading: {title}")
        print(f"   🔗 URL: {url}")
        
        try:
            # Download the article
            response = self.session.get(url, timeout=30)
            response.raise_for_status()
            
            # Process images in content
            processed_content = self.process_images_in_content(response.text, title)
            
            # Create filename
            safe_title = self.sanitize_filename(title)
            filename = f"{year}_01_01_{safe_title}.html"
            
            # Ensure year directory exists
            year_dir = Path("output/archive") / year
            year_dir.mkdir(parents=True, exist_ok=True)
            
            # Save the article
            file_path = year_dir / filename
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(processed_content)
            
            # Save to database
            self.save_to_database(url, title, year, processed_content)
            
            print(f"   ✅ Saved: {file_path}")
            return True
            
        except Exception as e:
            print(f"   ❌ Failed to download {url}: {e}")
            return False

    def save_to_database(self, url, title, year, content):
        """Save article to database"""
        try:
            conn = sqlite3.connect(config.DATABASE_PATH)
            cursor = conn.cursor()
            
            # Check if article already exists
            cursor.execute("SELECT id FROM posts WHERE url = ?", (url,))
            if cursor.fetchone():
                # Update existing
                cursor.execute("""
                    UPDATE posts 
                    SET title = ?, content = ?, year = ?, updated_at = ?
                    WHERE url = ?
                """, (title, content, year, datetime.now().isoformat(), url))
            else:
                # Insert new
                cursor.execute("""
                    INSERT INTO posts (url, title, content, year, author, created_at, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                """, (url, title, content, year, "Gerry Lewy", datetime.now().isoformat(), datetime.now().isoformat()))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            print(f"   ⚠️  Database save error: {e}")

    def run(self):
        """Download all missing articles"""
        print("=" * 80)
        print("🎯 DOWNLOADING 13 MISSING ARTICLES")
        print("=" * 80)
        
        successful = 0
        failed = 0
        
        for i, article in enumerate(self.missing_articles, 1):
            print(f"\n[{i}/{len(self.missing_articles)}]", end=" ")
            
            if self.download_article(article):
                successful += 1
            else:
                failed += 1
            
            # Small delay between downloads
            time.sleep(2)
        
        print(f"\n" + "=" * 80)
        print(f"📊 DOWNLOAD SUMMARY:")
        print(f"   ✅ Successful: {successful}")
        print(f"   ❌ Failed: {failed}")
        print(f"   📁 Total: {len(self.missing_articles)}")
        print("=" * 80)

if __name__ == "__main__":
    downloader = MissingArticleDownloader()
    downloader.run()
