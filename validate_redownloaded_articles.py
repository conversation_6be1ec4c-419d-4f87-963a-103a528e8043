#!/usr/bin/env python3
"""
Validate Re-Downloaded Articles

Comprehensive validation script to check the 156 re-downloaded articles:
1. Verify all articles were downloaded correctly
2. Check image links are properly replaced (local PNG paths)
3. Validate images exist and are proper PNG files
4. Check for duplicates in database
5. Update index files
6. Generate validation report
"""

import json
import sqlite3
import re
from pathlib import Path
from typing import Dict, List, Set, Tuple
from bs4 import BeautifulSoup
import sys

# Add current directory to path
sys.path.insert(0, str(Path(__file__).parent))
import config

class ArticleValidator:
    def __init__(self):
        self.report = {
            'validation_timestamp': None,
            'total_expected': 0,
            'files_found': 0,
            'files_missing': [],
            'image_validation': {
                'total_images_expected': 0,
                'images_found': 0,
                'images_missing': [],
                'invalid_images': []
            },
            'link_validation': {
                'articles_with_fixed_links': 0,
                'articles_with_broken_links': [],
                'online_links_remaining': []
            },
            'database_validation': {
                'total_db_entries': 0,
                'duplicates_found': [],
                'missing_from_db': []
            },
            'index_updates': {
                'years_updated': [],
                'total_articles_per_year': {}
            }
        }
    
    def load_missing_articles(self) -> List[Dict]:
        """Load the list of articles that should have been re-downloaded"""
        report_file = Path("output/metadata/correct_comparison_report.json")
        
        if not report_file.exists():
            print(f"❌ Missing articles report not found: {report_file}")
            return []
        
        with open(report_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        return data.get('missing_articles', [])
    
    def validate_file_downloads(self, missing_articles: List[Dict]) -> None:
        """Check if all expected files were downloaded"""
        print("📁 Validating file downloads...")
        
        self.report['total_expected'] = len(missing_articles)
        archive_dir = Path("output/archive")
        
        for article in missing_articles:
            year = article.get('year', 'unknown')
            title = article['title']
            
            # Generate expected filename (same logic as crawler)
            safe_title = re.sub(r'[<>:"/\\|?*]', '-', title)
            safe_title = re.sub(r'-+', '-', safe_title).strip('-')
            if len(safe_title) > 100:
                safe_title = safe_title[:100]
            
            expected_filename = f"{year}_01_01_{safe_title}.html"
            expected_path = archive_dir / year / expected_filename
            
            if expected_path.exists():
                self.report['files_found'] += 1
                print(f"   ✅ Found: {expected_filename}")
            else:
                self.report['files_missing'].append({
                    'url': article['url'],
                    'title': title,
                    'expected_path': str(expected_path)
                })
                print(f"   ❌ Missing: {expected_filename}")
    
    def validate_image_links(self, missing_articles: List[Dict]) -> None:
        """Check if image links are properly replaced with local paths"""
        print("\n🖼️  Validating image link replacements...")
        
        archive_dir = Path("output/archive")
        images_dir = Path("output/images")
        
        for article in missing_articles:
            year = article.get('year', 'unknown')
            title = article['title']
            
            # Find the HTML file
            safe_title = re.sub(r'[<>:"/\\|?*]', '-', title)
            safe_title = re.sub(r'-+', '-', safe_title).strip('-')
            if len(safe_title) > 100:
                safe_title = safe_title[:100]
            
            html_file = archive_dir / year / f"{year}_01_01_{safe_title}.html"
            
            if not html_file.exists():
                continue
            
            # Parse HTML and check image links
            try:
                with open(html_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                soup = BeautifulSoup(content, 'html.parser')
                
                # Check img src tags
                img_tags = soup.find_all('img')
                anchor_tags = soup.find_all('a')
                
                online_img_srcs = []
                online_anchor_hrefs = []
                local_images_referenced = []
                
                for img in img_tags:
                    src = img.get('src', '')
                    if src.startswith('http'):
                        online_img_srcs.append(src)
                    elif src.startswith('../../images/'):
                        local_images_referenced.append(src)
                
                for a in anchor_tags:
                    href = a.get('href', '')
                    if 'blogger.googleusercontent.com' in href or 'blogspot.com' in href:
                        online_anchor_hrefs.append(href)
                
                # Validate local images exist
                missing_local_images = []
                for local_path in local_images_referenced:
                    # Convert relative path to absolute
                    img_filename = local_path.split('/')[-1]
                    actual_img_path = images_dir / "unknown" / img_filename
                    
                    if not actual_img_path.exists():
                        missing_local_images.append(img_filename)
                        self.report['image_validation']['images_missing'].append(str(actual_img_path))
                    else:
                        self.report['image_validation']['images_found'] += 1
                
                # Report issues
                if online_img_srcs or online_anchor_hrefs:
                    self.report['link_validation']['articles_with_broken_links'].append({
                        'file': str(html_file),
                        'online_img_srcs': online_img_srcs,
                        'online_anchor_hrefs': online_anchor_hrefs
                    })
                    print(f"   ❌ {html_file.name}: {len(online_img_srcs)} online img srcs, {len(online_anchor_hrefs)} online anchor hrefs")
                else:
                    self.report['link_validation']['articles_with_fixed_links'] += 1
                    print(f"   ✅ {html_file.name}: All links properly localized")
                
                if missing_local_images:
                    print(f"   ⚠️  {html_file.name}: {len(missing_local_images)} referenced images missing")
                
                self.report['image_validation']['total_images_expected'] += len(local_images_referenced)
                
            except Exception as e:
                print(f"   ❌ Error parsing {html_file.name}: {e}")
    
    def validate_database_integrity(self, missing_articles: List[Dict]) -> None:
        """Check database for duplicates and missing entries"""
        print("\n🗄️  Validating database integrity...")
        
        try:
            conn = sqlite3.connect(config.DATABASE_PATH)
            cursor = conn.cursor()
            
            # Get all posts
            cursor.execute("SELECT url, title, COUNT(*) as count FROM posts GROUP BY url")
            results = cursor.fetchall()
            
            self.report['database_validation']['total_db_entries'] = len(results)
            
            # Check for duplicates
            duplicates = [(url, title, count) for url, title, count in results if count > 1]
            self.report['database_validation']['duplicates_found'] = duplicates
            
            if duplicates:
                print(f"   ⚠️  Found {len(duplicates)} duplicate entries:")
                for url, title, count in duplicates[:5]:  # Show first 5
                    print(f"      - {title[:50]}... ({count} copies)")
            else:
                print(f"   ✅ No duplicates found in database")
            
            # Check if re-downloaded articles are in database
            db_urls = {url for url, _, _ in results}
            missing_from_db = []
            
            for article in missing_articles:
                if article['url'] not in db_urls:
                    missing_from_db.append(article['url'])
            
            self.report['database_validation']['missing_from_db'] = missing_from_db
            
            if missing_from_db:
                print(f"   ❌ {len(missing_from_db)} re-downloaded articles missing from database")
            else:
                print(f"   ✅ All re-downloaded articles found in database")
            
            conn.close()
            
        except Exception as e:
            print(f"   ❌ Database validation error: {e}")
    
    def update_indexes(self) -> None:
        """Update index files with current article counts"""
        print("\n📋 Updating index files...")
        
        archive_dir = Path("output/archive")
        
        if not archive_dir.exists():
            print("   ❌ Archive directory not found")
            return
        
        # Count articles by year
        year_counts = {}
        for year_dir in archive_dir.iterdir():
            if year_dir.is_dir() and year_dir.name.isdigit():
                html_files = list(year_dir.glob("*.html"))
                year_counts[year_dir.name] = len(html_files)
                self.report['index_updates']['years_updated'].append(year_dir.name)
        
        self.report['index_updates']['total_articles_per_year'] = year_counts
        
        # Generate main index
        index_content = self.generate_main_index(year_counts)
        main_index_path = Path("output/archive/index.html")
        
        with open(main_index_path, 'w', encoding='utf-8') as f:
            f.write(index_content)
        
        print(f"   ✅ Updated main index: {len(year_counts)} years")
        
        # Show current totals
        total_articles = sum(year_counts.values())
        print(f"   📊 Total articles in archive: {total_articles}")
        
        for year in sorted(year_counts.keys(), reverse=True):
            print(f"      📅 {year}: {year_counts[year]} articles")
    
    def generate_main_index(self, year_counts: Dict[str, int]) -> str:
        """Generate HTML content for main index"""
        total = sum(year_counts.values())
        
        html = f"""<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gerry's Diamond Setting Essays - Complete Archive ({total} articles)</title>
    <style>
        body {{ font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 40px; background: #f5f5f5; }}
        .container {{ max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }}
        h1 {{ color: #2c3e50; text-align: center; margin-bottom: 30px; }}
        .year-section {{ margin: 20px 0; padding: 15px; background: #f8f9fa; border-radius: 5px; }}
        .year-title {{ font-size: 1.2em; font-weight: bold; color: #34495e; margin-bottom: 10px; }}
        .stats {{ text-align: center; margin: 20px 0; padding: 15px; background: #e8f4f8; border-radius: 5px; }}
    </style>
</head>
<body>
    <div class="container">
        <h1>📚 Gerry's Diamond Setting Essays</h1>
        <div class="stats">
            <strong>Complete Archive: {total} Articles</strong><br>
            <em>Last Updated: {self.get_current_timestamp()}</em>
        </div>
"""
        
        for year in sorted(year_counts.keys(), reverse=True):
            count = year_counts[year]
            html += f"""
        <div class="year-section">
            <div class="year-title">📅 {year} ({count} articles)</div>
            <a href="{year}/index.html">View {year} Articles →</a>
        </div>"""
        
        html += """
    </div>
</body>
</html>"""
        
        return html
    
    def get_current_timestamp(self) -> str:
        """Get current timestamp for reports"""
        from datetime import datetime
        return datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    def generate_validation_report(self) -> None:
        """Generate and save validation report"""
        print("\n📊 Generating validation report...")
        
        self.report['validation_timestamp'] = self.get_current_timestamp()
        
        # Save JSON report
        report_path = Path("output/metadata/redownload_validation_report.json")
        report_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(self.report, f, indent=2, ensure_ascii=False)
        
        print(f"   ✅ Validation report saved: {report_path}")
        
        # Print summary
        print(f"\n🎉 VALIDATION SUMMARY")
        print(f"=" * 50)
        print(f"📁 Files: {self.report['files_found']}/{self.report['total_expected']} downloaded")
        print(f"🖼️  Images: {self.report['image_validation']['images_found']} found, {len(self.report['image_validation']['images_missing'])} missing")
        print(f"🔗 Links: {self.report['link_validation']['articles_with_fixed_links']} articles with fixed links")
        print(f"🗄️  Database: {self.report['database_validation']['total_db_entries']} entries, {len(self.report['database_validation']['duplicates_found'])} duplicates")
        
        if self.report['files_missing']:
            print(f"❌ Missing files: {len(self.report['files_missing'])}")
        
        if self.report['link_validation']['articles_with_broken_links']:
            print(f"❌ Articles with online links: {len(self.report['link_validation']['articles_with_broken_links'])}")

def main():
    print("=" * 80)
    print("🔍 ARTICLE RE-DOWNLOAD VALIDATION")
    print("=" * 80)
    print("This script will validate the 156 re-downloaded articles:")
    print("✅ Check all files were downloaded")
    print("🖼️  Verify image links are properly localized")
    print("🗄️  Check database integrity and duplicates")
    print("📋 Update index files")
    print("=" * 80)
    
    validator = ArticleValidator()
    
    # Load expected articles
    missing_articles = validator.load_missing_articles()
    
    if not missing_articles:
        print("❌ No missing articles data found. Please run correct_comparison.py first.")
        return
    
    print(f"📋 Validating {len(missing_articles)} re-downloaded articles...")
    
    # Run all validations
    validator.validate_file_downloads(missing_articles)
    validator.validate_image_links(missing_articles)
    validator.validate_database_integrity(missing_articles)
    validator.update_indexes()
    validator.generate_validation_report()
    
    print(f"\n✅ Validation complete! Check the report for details.")

if __name__ == "__main__":
    main()
