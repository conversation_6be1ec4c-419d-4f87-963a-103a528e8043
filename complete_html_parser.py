#!/usr/bin/env python3
"""
Complete HTML Parser for All Years

Parse the complete expanded sidebar HTML to extract ALL articles from ALL years.

Expected totals from the HTML:
- 2025: 101 articles
- 2024: 227 articles  
- 2023: 133 articles
- 2022: 66 articles
- 2021: 13 articles
- 2020: 1 article
Total: 541 articles
"""

import json
import re
from pathlib import Path
from typing import List, Dict, Tuple
from datetime import datetime
from bs4 import BeautifulSoup

import config

class CompleteHTMLParser:
    def __init__(self, html_file_path: str = "html.txt"):
        self.html_file_path = html_file_path
        self.expected_totals = {
            '2025': 101,
            '2024': 227,
            '2023': 133,
            '2022': 66,
            '2021': 13,
            '2020': 1
        }
        self.total_expected = sum(self.expected_totals.values())
        
    def load_html(self) -> str:
        """Load the HTML content from file"""
        try:
            with open(self.html_file_path, 'r', encoding='utf-8') as f:
                return f.read()
        except Exception as e:
            print(f"❌ Error loading HTML file: {e}")
            return ""
    
    def extract_all_articles(self) -> Dict[str, List[Dict]]:
        """Extract all articles from all years"""
        print("🎯 Parsing complete expanded sidebar HTML...")
        print(f"📂 Loading from: {self.html_file_path}")
        
        html_content = self.load_html()
        if not html_content:
            return {}
        
        soup = BeautifulSoup(html_content, 'html.parser')
        
        # Find all <ul class="posts"> sections
        posts_sections = soup.select('ul.posts')
        print(f"📊 Found {len(posts_sections)} posts sections")
        
        articles_by_year = {}
        all_articles = []
        
        for posts_section in posts_sections:
            # Find the date for this posts section
            date_link = posts_section.find_previous('a', class_='post-count-link')
            if not date_link:
                continue
                
            date_text = date_link.get_text(strip=True)
            href = date_link.get('href', '')
            
            # Skip main year links (they don't have archive.html)
            if '_archive.html' not in href:
                continue
            
            # Extract year from URL
            year_match = re.search(r'/(\d{4})_', href)
            if not year_match:
                continue
                
            year = year_match.group(1)
            
            # Find the post count for this date
            count_span = posts_section.find_previous('span', class_='post-count')
            expected_count = 0
            if count_span:
                count_match = re.search(r'\((\d+)\)', count_span.get_text())
                if count_match:
                    expected_count = int(count_match.group(1))
            
            # Extract all article links from this posts section
            article_links = posts_section.select('li a[href]')
            
            print(f"   📅 {year}/{date_text}: {len(article_links)}/{expected_count} articles")
            
            for link in article_links:
                article_href = link.get('href')
                title = link.get_text(strip=True)
                
                # Clean up HTML entities
                title = title.replace('&gt;', '>').replace('&lt;', '<').replace('&amp;', '&')
                
                article = {
                    'url': article_href,
                    'title': title,
                    'year': year,
                    'archive_date': date_text,
                    'expected_count': expected_count,
                    'source': 'complete_html_parser'
                }
                
                all_articles.append(article)
                
                # Group by year
                if year not in articles_by_year:
                    articles_by_year[year] = []
                articles_by_year[year].append(article)
        
        return articles_by_year, all_articles
    
    def validate_extraction(self, articles_by_year: Dict[str, List[Dict]]) -> Dict:
        """Validate the extraction against expected totals"""
        print("\n" + "=" * 80)
        print("📊 VALIDATION SUMMARY")
        print("=" * 80)
        
        validation_results = {}
        total_found = 0
        
        for year in sorted(self.expected_totals.keys(), reverse=True):
            expected = self.expected_totals[year]
            found = len(articles_by_year.get(year, []))
            total_found += found
            
            coverage = (found / expected * 100) if expected > 0 else 0
            status = "✅" if found >= expected else "⚠️"
            
            print(f"   {status} {year}: {found:3d}/{expected:3d} articles ({coverage:5.1f}%)")
            
            validation_results[year] = {
                'expected': expected,
                'found': found,
                'coverage_percentage': round(coverage, 1),
                'status': 'complete' if found >= expected else 'incomplete'
            }
        
        print(f"\n   📊 TOTAL: {total_found:3d}/{self.total_expected:3d} articles ({total_found/self.total_expected*100:.1f}%)")
        
        validation_results['overall'] = {
            'expected': self.total_expected,
            'found': total_found,
            'coverage_percentage': round(total_found/self.total_expected*100, 1),
            'status': 'complete' if total_found >= self.total_expected else 'incomplete'
        }
        
        return validation_results
    
    def save_results(self, articles_by_year: Dict[str, List[Dict]], all_articles: List[Dict], validation: Dict):
        """Save the extraction results"""
        
        # Prepare results
        results = {
            'extraction_date': datetime.now().isoformat(),
            'source_file': self.html_file_path,
            'expected_totals': self.expected_totals,
            'validation': validation,
            'articles_by_year': articles_by_year,
            'all_articles': all_articles,
            'summary': {
                'total_years': len(articles_by_year),
                'total_articles': len(all_articles),
                'years_processed': sorted(articles_by_year.keys(), reverse=True)
            }
        }
        
        # Save complete results
        output_path = Path(config.OUTPUT_DIR) / "metadata" / "complete_html_extraction.json"
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        
        print(f"\n📋 Complete results saved to: {output_path}")
        
        # Save year-specific files for easier processing
        for year, articles in articles_by_year.items():
            year_path = Path(config.OUTPUT_DIR) / "metadata" / f"articles_{year}.json"
            year_data = {
                'year': year,
                'extraction_date': datetime.now().isoformat(),
                'expected_count': self.expected_totals.get(year, 0),
                'actual_count': len(articles),
                'articles': articles
            }
            
            with open(year_path, 'w', encoding='utf-8') as f:
                json.dump(year_data, f, indent=2, ensure_ascii=False)
            
            print(f"   📋 {year} articles saved to: {year_path}")
        
        return results
    
    def run_complete_extraction(self) -> Dict:
        """Run the complete extraction process"""
        print("=" * 80)
        print("🎯 COMPLETE HTML EXTRACTION - ALL YEARS")
        print("=" * 80)
        print(f"🎯 Target: {self.total_expected} articles across {len(self.expected_totals)} years")
        print()
        
        # Extract articles
        articles_by_year, all_articles = self.extract_all_articles()
        
        if not articles_by_year:
            print("❌ No articles extracted!")
            return {}
        
        # Validate extraction
        validation = self.validate_extraction(articles_by_year)
        
        # Save results
        results = self.save_results(articles_by_year, all_articles, validation)
        
        # Final assessment
        print("\n" + "=" * 80)
        print("🎯 FINAL ASSESSMENT")
        print("=" * 80)
        
        overall = validation['overall']
        if overall['coverage_percentage'] >= 99:
            print("   🎉 EXCELLENT: Nearly complete extraction!")
            print("   ✅ Ready for downloading all articles")
        elif overall['coverage_percentage'] >= 95:
            print("   ✅ VERY GOOD: Substantial coverage achieved")
            print("   ✅ Ready for downloading with minor gaps")
        elif overall['coverage_percentage'] >= 90:
            print("   ⚠️  GOOD: Most articles found, some gaps remain")
        else:
            print("   🔍 NEEDS INVESTIGATION: Significant gaps found")
        
        print(f"   📊 Final count: {overall['found']}/{overall['expected']} articles ({overall['coverage_percentage']}%)")
        
        return results

def main():
    try:
        print("🚀 Starting complete HTML parser...")
        parser = CompleteHTMLParser()
        print("✅ Parser initialized")

        results = parser.run_complete_extraction()
        print("✅ Extraction completed")

        if results:
            print(f"\n🚀 NEXT STEP: Use the extracted article lists to download all {results['summary']['total_articles']} articles!")
        else:
            print("\n❌ Extraction failed. Please check the HTML file and try again.")

    except Exception as e:
        print(f"❌ Error in main: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
