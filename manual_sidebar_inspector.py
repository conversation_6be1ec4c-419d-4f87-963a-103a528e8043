#!/usr/bin/env python3
"""
Manual Sidebar Inspector

Let's first examine the sidebar HTML structure manually to understand
how the year expansion works and what the actual structure looks like.
"""

import requests
import json
import re
from pathlib import Path
from bs4 import BeautifulSoup

import config

class ManualSidebarInspector:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
    
    def inspect_sidebar_structure(self):
        """Inspect the sidebar HTML structure"""
        print("🔍 Inspecting sidebar structure...")
        
        try:
            response = self.session.get(config.BASE_URL, timeout=30)
            if response.status_code != 200:
                print(f"❌ HTTP {response.status_code}")
                return
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Find the archive section
            archive_section = soup.select_one('#BlogArchive1')
            if not archive_section:
                print("❌ Archive section not found")
                return
            
            print("✅ Found archive section")
            
            # Save the raw HTML for inspection
            archive_html = archive_section.prettify()
            
            output_path = Path(config.OUTPUT_DIR) / "metadata" / "sidebar_raw.html"
            output_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(archive_html)
            
            print(f"📄 Raw sidebar HTML saved to: {output_path}")
            
            # Extract and display the text content
            archive_text = archive_section.get_text()
            
            text_output_path = Path(config.OUTPUT_DIR) / "metadata" / "sidebar_text.txt"
            with open(text_output_path, 'w', encoding='utf-8') as f:
                f.write(archive_text)
            
            print(f"📄 Sidebar text saved to: {text_output_path}")
            
            # Look for year patterns
            print("\n🔍 Looking for year patterns...")
            year_pattern = re.compile(r'(\d{4})\s*\((\d+)\)')
            year_matches = year_pattern.findall(archive_text)
            
            for year, count in year_matches:
                print(f"   📅 Year {year}: {count} articles")
            
            # Look for date patterns
            print("\n🔍 Looking for date patterns...")
            date_pattern = re.compile(r'(\d{1,2})/(\d{1,2})\s*\((\d+)\)')
            date_matches = date_pattern.findall(archive_text)
            
            print(f"Found {len(date_matches)} date entries:")
            for month, day, count in date_matches[:10]:  # Show first 10
                print(f"   📅 {month}/{day}: {count} articles")
            
            if len(date_matches) > 10:
                print(f"   ... and {len(date_matches) - 10} more")
            
            # Look for links that might expand years
            print("\n🔍 Looking for expandable links...")
            links = archive_section.find_all('a')
            
            for link in links:
                href = link.get('href', '')
                text = link.get_text(strip=True)
                
                if '2024' in text or '2024' in href:
                    print(f"   🔗 Link: {text} -> {href}")
            
            # Look for JavaScript or onclick handlers
            print("\n🔍 Looking for JavaScript handlers...")
            elements_with_onclick = archive_section.find_all(attrs={"onclick": True})
            
            for elem in elements_with_onclick:
                onclick = elem.get('onclick', '')
                text = elem.get_text(strip=True)
                print(f"   🖱️  Element: {text} -> onclick: {onclick}")
            
            # Look for toggle elements
            toggle_elements = archive_section.find_all(class_=re.compile(r'toggle|expand|collapse'))
            for elem in toggle_elements:
                print(f"   🔄 Toggle element: {elem.get('class')} -> {elem.get_text(strip=True)}")
            
            return archive_section
            
        except Exception as e:
            print(f"❌ Error: {e}")
            return None
    
    def try_direct_year_url(self, year=2024):
        """Try accessing the year directly via URL"""
        print(f"\n🔗 Trying direct year URL for {year}...")
        
        year_urls = [
            f"https://gerrysdiamondsettingessays.blogspot.com/{year}/",
            f"https://gerrysdiamondsettingessays.blogspot.com/search/label/{year}",
            f"https://gerrysdiamondsettingessays.blogspot.com/search?updated-min={year}-01-01T00:00:00-05:00&updated-max={year+1}-01-01T00:00:00-05:00&max-results=500"
        ]
        
        for i, url in enumerate(year_urls, 1):
            print(f"   🔗 Method {i}: {url}")
            
            try:
                response = self.session.get(url, timeout=20)
                
                if response.status_code == 200:
                    soup = BeautifulSoup(response.content, 'html.parser')
                    post_containers = soup.select(config.SELECTORS["post_container"])
                    
                    print(f"      📄 Found {len(post_containers)} post containers")
                    
                    # Count articles from this year
                    year_articles = 0
                    for container in post_containers:
                        try:
                            title_elem = container.select_one(config.SELECTORS["post_title"])
                            if title_elem and title_elem.get('href'):
                                url_href = title_elem.get('href')
                                year_match = re.search(r'/(\d{4})/', url_href)
                                if year_match and int(year_match.group(1)) == year:
                                    year_articles += 1
                        except:
                            continue
                    
                    print(f"      ✅ Articles from {year}: {year_articles}")
                    
                    if year_articles > 0:
                        print(f"      🎯 This method works! Found {year_articles} articles")
                else:
                    print(f"      ❌ HTTP {response.status_code}")
                    
            except Exception as e:
                print(f"      ❌ Error: {e}")
    
    def analyze_pagination_limits(self, year=2024):
        """Analyze pagination limits for the year"""
        print(f"\n📊 Analyzing pagination limits for {year}...")
        
        base_url = f"https://gerrysdiamondsettingessays.blogspot.com/search?updated-min={year}-01-01T00:00:00-05:00&updated-max={year+1}-01-01T00:00:00-05:00"
        
        # Test different max-results values
        max_results_tests = [20, 50, 100, 200, 500, 1000]
        
        for max_results in max_results_tests:
            test_url = f"{base_url}&max-results={max_results}"
            print(f"   📊 Testing max-results={max_results}...")
            
            try:
                response = self.session.get(test_url, timeout=20)
                
                if response.status_code == 200:
                    soup = BeautifulSoup(response.content, 'html.parser')
                    post_containers = soup.select(config.SELECTORS["post_container"])
                    
                    year_articles = 0
                    for container in post_containers:
                        try:
                            title_elem = container.select_one(config.SELECTORS["post_title"])
                            if title_elem and title_elem.get('href'):
                                url_href = title_elem.get('href')
                                year_match = re.search(r'/(\d{4})/', url_href)
                                if year_match and int(year_match.group(1)) == year:
                                    year_articles += 1
                        except:
                            continue
                    
                    print(f"      ✅ Found {year_articles} articles with max-results={max_results}")
                    
                    # If we got fewer articles than requested, we might have hit the limit
                    if year_articles < max_results:
                        print(f"      🎯 Possible limit reached: {year_articles} < {max_results}")
                else:
                    print(f"      ❌ HTTP {response.status_code}")
                    
            except Exception as e:
                print(f"      ❌ Error: {e}")

def main():
    inspector = ManualSidebarInspector()
    
    print("=" * 80)
    print("🔍 MANUAL SIDEBAR INSPECTOR")
    print("=" * 80)
    
    # Step 1: Inspect sidebar structure
    archive_section = inspector.inspect_sidebar_structure()
    
    # Step 2: Try direct year URLs
    inspector.try_direct_year_url(2024)
    
    # Step 3: Analyze pagination limits
    inspector.analyze_pagination_limits(2024)
    
    print("\n🎯 NEXT STEPS:")
    print("   📄 Check the saved HTML and text files to understand sidebar structure")
    print("   🔗 Use the working URL method to extract all articles")
    print("   📊 Apply appropriate pagination limits")

if __name__ == "__main__":
    main()
