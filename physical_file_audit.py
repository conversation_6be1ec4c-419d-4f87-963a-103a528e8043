#!/usr/bin/env python3
"""
Physical audit of downloaded files vs master index
"""

import json
import re
from pathlib import Path
from collections import defaultdict

class PhysicalFileAuditor:
    def __init__(self):
        self.archive_dir = Path("output/archive")
        self.master_file = Path("html.txt")
        
    def get_physical_files(self):
        """Get all physical HTML files in archive"""
        physical_files = {}
        year_counts = defaultdict(int)
        
        if not self.archive_dir.exists():
            print("❌ Archive directory not found!")
            return {}, {}
        
        print("📁 Scanning physical files...")
        
        for year_dir in self.archive_dir.iterdir():
            if year_dir.is_dir() and year_dir.name.isdigit():
                year = year_dir.name
                html_files = list(year_dir.glob("*.html"))
                year_counts[year] = len(html_files)
                
                for html_file in html_files:
                    physical_files[html_file.name] = {
                        'path': html_file,
                        'year': year,
                        'size': html_file.stat().st_size
                    }
        
        return physical_files, year_counts
    
    def get_master_index(self):
        """Extract articles from master html.txt file"""
        if not self.master_file.exists():
            print("❌ Master html.txt file not found!")
            return [], {}
        
        print("📋 Reading master index...")
        
        with open(self.master_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Extract article links using regex
        pattern = r'<li><a href="(https://gerrysdiamondsettingessays\.blogspot\.com/([0-9]{4})/[0-9]{2}/[^"]+\.html)"[^>]*>([^<]+)</a></li>'
        matches = re.findall(pattern, content)
        
        master_articles = []
        year_counts = defaultdict(int)
        
        for url, year, title in matches:
            # Clean up HTML entities in title
            import html
            clean_title = html.unescape(title.strip())
            
            master_articles.append({
                'url': url,
                'title': clean_title,
                'year': year
            })
            year_counts[year] += 1
        
        return master_articles, year_counts
    
    def compare_counts(self):
        """Compare physical files with master index"""
        print("=" * 80)
        print("🔍 PHYSICAL FILE AUDIT")
        print("=" * 80)
        
        # Get physical files
        physical_files, physical_year_counts = self.get_physical_files()
        total_physical = len(physical_files)
        
        # Get master index
        master_articles, master_year_counts = self.get_master_index()
        total_master = len(master_articles)
        
        print(f"\n📊 SUMMARY COUNTS:")
        print(f"   📁 Physical files: {total_physical}")
        print(f"   📋 Master index:   {total_master}")
        print(f"   📈 Difference:     {total_physical - total_master}")
        
        print(f"\n📅 YEAR-BY-YEAR COMPARISON:")
        all_years = sorted(set(list(physical_year_counts.keys()) + list(master_year_counts.keys())))
        
        for year in all_years:
            physical_count = physical_year_counts.get(year, 0)
            master_count = master_year_counts.get(year, 0)
            diff = physical_count - master_count
            status = "✅" if diff == 0 else "⚠️" if diff > 0 else "❌"
            
            print(f"   {status} {year}: Physical={physical_count:3d} | Master={master_count:3d} | Diff={diff:+3d}")
        
        # Check for duplicates or issues
        print(f"\n🔍 FILE ANALYSIS:")
        
        # Check for very small files (potential issues)
        small_files = [f for f, info in physical_files.items() if info['size'] < 1000]
        if small_files:
            print(f"   ⚠️  Small files (<1KB): {len(small_files)}")
            for f in small_files[:5]:  # Show first 5
                print(f"      - {f} ({physical_files[f]['size']} bytes)")
        
        # Check for duplicate-looking filenames
        titles = [f.split('_', 3)[-1].replace('.html', '') if '_' in f else f.replace('.html', '') 
                 for f in physical_files.keys()]
        title_counts = defaultdict(int)
        for title in titles:
            title_counts[title] += 1
        
        duplicates = {title: count for title, count in title_counts.items() if count > 1}
        if duplicates:
            print(f"   ⚠️  Potential duplicates: {len(duplicates)}")
            for title, count in list(duplicates.items())[:5]:  # Show first 5
                print(f"      - '{title}' appears {count} times")
        
        # Save detailed report
        report = {
            'audit_timestamp': str(Path().resolve()),
            'total_physical_files': total_physical,
            'total_master_articles': total_master,
            'difference': total_physical - total_master,
            'year_comparison': {
                year: {
                    'physical': physical_year_counts.get(year, 0),
                    'master': master_year_counts.get(year, 0),
                    'difference': physical_year_counts.get(year, 0) - master_year_counts.get(year, 0)
                }
                for year in all_years
            },
            'small_files': small_files,
            'potential_duplicates': duplicates
        }
        
        report_path = Path("output/metadata/physical_audit_report.json")
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        print(f"\n💾 Detailed report saved to: {report_path}")
        
        # Overall status
        if total_physical == total_master:
            print(f"\n🎉 SUCCESS: Physical files match master index perfectly!")
        elif total_physical > total_master:
            print(f"\n⚠️  WARNING: {total_physical - total_master} more physical files than expected")
        else:
            print(f"\n❌ ERROR: {total_master - total_physical} files missing from physical archive")
        
        print("=" * 80)

if __name__ == "__main__":
    auditor = PhysicalFileAuditor()
    auditor.compare_counts()
