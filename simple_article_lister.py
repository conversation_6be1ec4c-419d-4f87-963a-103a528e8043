#!/usr/bin/env python3
"""
Simple Article Lister

This script does ONE thing only:
- Extract ALL article links from the blog for 2024
- List each article with its title and URL
- No comparisons, no duplicates checking, no local file checking
- Just pure extraction to see what's actually on the blog

Goal: Get the complete list of 227 articles for 2024 as shown in sidebar
"""

import requests
import json
import re
import time
from pathlib import Path
from typing import List, Dict
from datetime import datetime
from bs4 import BeautifulSoup
from urllib.parse import urljoin

import config

class SimpleArticleLister:
    def __init__(self, target_year: int = 2024):
        self.target_year = target_year
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        self.all_articles = []  # List of {url, title, source_method}
        
    def extract_from_year_search(self) -> List[Dict]:
        """Method 1: Extract from year search URL"""
        print(f"🔍 Method 1: Year search for {self.target_year}")
        
        articles = []
        search_url = f"https://gerrysdiamondsettingessays.blogspot.com/search?updated-min={self.target_year}-01-01T00:00:00-05:00&updated-max={self.target_year+1}-01-01T00:00:00-05:00&max-results=500"
        
        try:
            print(f"   🔗 Fetching: {search_url}")
            response = self.session.get(search_url, timeout=30)
            
            if response.status_code == 200:
                soup = BeautifulSoup(response.content, 'html.parser')
                post_containers = soup.select(config.SELECTORS["post_container"])
                
                print(f"   📄 Found {len(post_containers)} post containers")
                
                for i, container in enumerate(post_containers, 1):
                    try:
                        title_elem = container.select_one(config.SELECTORS["post_title"])
                        if title_elem and title_elem.get('href'):
                            url = title_elem.get('href')
                            title = title_elem.get_text(strip=True)
                            
                            # Verify it's from target year
                            year_match = re.search(r'/(\d{4})/', url)
                            if year_match and int(year_match.group(1)) == self.target_year:
                                article = {
                                    'url': url,
                                    'title': title,
                                    'source_method': 'year_search',
                                    'order': i
                                }
                                articles.append(article)
                                print(f"   📝 {i:3d}: {title[:60]}...")
                    except Exception as e:
                        print(f"   ❌ Error processing container {i}: {e}")
                        continue
            else:
                print(f"   ❌ HTTP {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ Error: {e}")
        
        print(f"   ✅ Method 1 found: {len(articles)} articles")
        return articles
    
    def extract_from_monthly_archives(self) -> List[Dict]:
        """Method 2: Extract from monthly archive pages"""
        print(f"📅 Method 2: Monthly archives for {self.target_year}")
        
        articles = []
        article_counter = 0
        
        for month in range(1, 13):
            print(f"   📅 Month {month:02d}...")
            
            # Try different monthly archive URL formats
            monthly_urls = [
                f"https://gerrysdiamondsettingessays.blogspot.com/{self.target_year}_{month:02d}_01_archive.html",
                f"https://gerrysdiamondsettingessays.blogspot.com/{self.target_year}/{month:02d}/",
                f"https://gerrysdiamondsettingessays.blogspot.com/search?updated-min={self.target_year}-{month:02d}-01T00:00:00-05:00&updated-max={self.target_year}-{month:02d}-31T23:59:59-05:00&max-results=100",
            ]
            
            month_articles = []
            
            for url_format, archive_url in enumerate(monthly_urls, 1):
                try:
                    print(f"      🔗 Format {url_format}: {archive_url}")
                    response = self.session.get(archive_url, timeout=20)
                    
                    if response.status_code == 200:
                        soup = BeautifulSoup(response.content, 'html.parser')
                        post_containers = soup.select(config.SELECTORS["post_container"])
                        
                        if post_containers:
                            print(f"         📄 Found {len(post_containers)} containers")
                            
                            for container in post_containers:
                                try:
                                    title_elem = container.select_one(config.SELECTORS["post_title"])
                                    if title_elem and title_elem.get('href'):
                                        url = title_elem.get('href')
                                        title = title_elem.get_text(strip=True)
                                        
                                        # Verify it's from target year
                                        year_match = re.search(r'/(\d{4})/', url)
                                        if year_match and int(year_match.group(1)) == self.target_year:
                                            article_counter += 1
                                            article = {
                                                'url': url,
                                                'title': title,
                                                'source_method': f'monthly_m{month:02d}_f{url_format}',
                                                'order': article_counter
                                            }
                                            month_articles.append(article)
                                            print(f"         📝 {article_counter:3d}: {title[:50]}...")
                                except:
                                    continue
                            
                            # If we found articles with this format, don't try other formats for this month
                            if month_articles:
                                break
                        else:
                            print(f"         📄 No containers found")
                    else:
                        print(f"         ❌ HTTP {response.status_code}")
                        
                except Exception as e:
                    print(f"         ❌ Error: {e}")
                    continue
                    
                time.sleep(0.5)  # Be respectful
            
            articles.extend(month_articles)
            print(f"      ✅ Month {month:02d}: {len(month_articles)} articles")
        
        print(f"   ✅ Method 2 found: {len(articles)} articles")
        return articles
    
    def extract_from_year_page(self) -> List[Dict]:
        """Method 3: Extract from year page"""
        print(f"📄 Method 3: Year page for {self.target_year}")
        
        articles = []
        year_url = f"https://gerrysdiamondsettingessays.blogspot.com/{self.target_year}/"
        
        try:
            print(f"   🔗 Fetching: {year_url}")
            response = self.session.get(year_url, timeout=30)
            
            if response.status_code == 200:
                soup = BeautifulSoup(response.content, 'html.parser')
                post_containers = soup.select(config.SELECTORS["post_container"])
                
                print(f"   📄 Found {len(post_containers)} post containers")
                
                for i, container in enumerate(post_containers, 1):
                    try:
                        title_elem = container.select_one(config.SELECTORS["post_title"])
                        if title_elem and title_elem.get('href'):
                            url = title_elem.get('href')
                            title = title_elem.get_text(strip=True)
                            
                            # Verify it's from target year
                            year_match = re.search(r'/(\d{4})/', url)
                            if year_match and int(year_match.group(1)) == self.target_year:
                                article = {
                                    'url': url,
                                    'title': title,
                                    'source_method': 'year_page',
                                    'order': i
                                }
                                articles.append(article)
                                print(f"   📝 {i:3d}: {title[:60]}...")
                    except Exception as e:
                        print(f"   ❌ Error processing container {i}: {e}")
                        continue
            else:
                print(f"   ❌ HTTP {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ Error: {e}")
        
        print(f"   ✅ Method 3 found: {len(articles)} articles")
        return articles
    
    def run_complete_extraction(self) -> Dict:
        """Run all extraction methods and combine results"""
        print("=" * 80)
        print(f"📋 SIMPLE ARTICLE LISTER FOR {self.target_year}")
        print("=" * 80)
        print(f"🎯 Goal: Extract ALL article links from the blog")
        print(f"📊 Expected: 227 articles (from sidebar)")
        print()
        
        # Run all extraction methods
        method1_articles = self.extract_from_year_search()
        method2_articles = self.extract_from_monthly_archives()
        method3_articles = self.extract_from_year_page()
        
        # Combine all articles (with duplicates for now)
        all_articles = method1_articles + method2_articles + method3_articles
        
        # Analysis
        print("\n" + "=" * 80)
        print("📊 EXTRACTION RESULTS")
        print("=" * 80)
        
        print(f"🔍 Method 1 (Year Search): {len(method1_articles)} articles")
        print(f"📅 Method 2 (Monthly Archives): {len(method2_articles)} articles")
        print(f"📄 Method 3 (Year Page): {len(method3_articles)} articles")
        print(f"📋 Total Articles Found: {len(all_articles)} articles")
        print(f"📊 Expected vs Found: 227 expected, {len(all_articles)} found")
        
        if len(all_articles) < 227:
            print(f"⚠️  MISSING: {227 - len(all_articles)} articles not found!")
        elif len(all_articles) > 227:
            print(f"➕ EXTRA: {len(all_articles) - 227} more than expected (likely duplicates)")
        else:
            print("✅ PERFECT MATCH!")
        
        # Save results
        results = {
            'extraction_date': datetime.now().isoformat(),
            'target_year': self.target_year,
            'expected_count': 227,
            'total_found': len(all_articles),
            'method_counts': {
                'year_search': len(method1_articles),
                'monthly_archives': len(method2_articles),
                'year_page': len(method3_articles)
            },
            'all_articles': all_articles
        }
        
        # Save to file
        output_path = Path(config.OUTPUT_DIR) / "metadata" / f"article_list_{self.target_year}.json"
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        
        print(f"\n📋 Complete article list saved to: {output_path}")
        
        # Also save a simple text list for easy review
        text_output_path = Path(config.OUTPUT_DIR) / "metadata" / f"article_list_{self.target_year}.txt"
        with open(text_output_path, 'w', encoding='utf-8') as f:
            f.write(f"Article List for {self.target_year}\n")
            f.write(f"Generated: {datetime.now().isoformat()}\n")
            f.write(f"Total Found: {len(all_articles)} articles\n")
            f.write("=" * 80 + "\n\n")
            
            for i, article in enumerate(all_articles, 1):
                f.write(f"{i:3d}. {article['title']}\n")
                f.write(f"     URL: {article['url']}\n")
                f.write(f"     Source: {article['source_method']}\n\n")
        
        print(f"📄 Text list saved to: {text_output_path}")
        
        return results

def main():
    # Focus on 2024
    target_year = 2024
    
    lister = SimpleArticleLister(target_year)
    results = lister.run_complete_extraction()
    
    print(f"\n🎯 SUMMARY:")
    print(f"   📊 Found {results['total_found']} articles for {target_year}")
    print(f"   📋 Saved complete list for review")
    print(f"   🔍 Next step: Review the list for duplicates and quality")

if __name__ == "__main__":
    main()
