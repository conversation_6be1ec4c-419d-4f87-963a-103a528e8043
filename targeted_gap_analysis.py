#!/usr/bin/env python3
"""
Targeted Gap Analysis

Based on the blog archive sidebar showing:
- 2025: 101 articles (we have 86) - Missing 15
- 2024: 227 articles (we have 174) - Missing 53  
- 2023: 133 articles (we have 105) - Missing 28
- 2022: 66 articles (we have 30) - Missing 36
- 2021: 13 articles (we have 11) - Missing 2
- 2020: 1 article (we have 1) - OK
- 2019: 50 articles (we have 46) - Missing 4
- 2018: 77 articles (we have 56) - Missing 21

Total expected: ~668, we have: 509, missing: ~159
"""

import os
import json
from pathlib import Path
from collections import defaultdict
from datetime import datetime

import config

def analyze_physical_vs_expected():
    """Analyze physical files vs expected counts from blog sidebar"""
    
    # Expected counts from blog archive sidebar (from user's screenshot)
    expected_counts = {
        2025: 101,
        2024: 227,
        2023: 133,
        2022: 66,
        2021: 13,
        2020: 1,
        2019: 50,
        2018: 77
    }
    
    print("=" * 80)
    print("🎯 TARGETED GAP ANALYSIS")
    print("=" * 80)
    print("Based on blog archive sidebar counts vs physical files")
    print()
    
    # Count physical files
    archive_dir = Path(config.OUTPUT_DIR) / "archive"
    physical_counts = defaultdict(int)
    
    if archive_dir.exists():
        for year_dir in archive_dir.iterdir():
            if year_dir.is_dir() and year_dir.name.isdigit():
                year = int(year_dir.name)
                html_files = list(year_dir.glob("*.html"))
                physical_counts[year] = len(html_files)
    
    # Calculate gaps
    total_expected = sum(expected_counts.values())
    total_physical = sum(physical_counts.values())
    total_missing = total_expected - total_physical
    
    print(f"📊 SUMMARY:")
    print(f"   🌐 Expected (from blog): {total_expected} articles")
    print(f"   📁 Physical files: {total_physical} articles")
    print(f"   ❌ Missing: {total_missing} articles")
    print()
    
    print(f"📅 YEAR-BY-YEAR ANALYSIS:")
    print(f"{'Year':<6} {'Expected':<10} {'Physical':<10} {'Missing':<10} {'Status'}")
    print("-" * 50)
    
    missing_by_year = {}
    
    for year in sorted(expected_counts.keys(), reverse=True):
        expected = expected_counts[year]
        physical = physical_counts.get(year, 0)
        missing = expected - physical
        
        if missing > 0:
            status = f"❌ -{missing}"
            missing_by_year[year] = missing
        elif missing < 0:
            status = f"➕ +{abs(missing)}"
        else:
            status = "✅ OK"
        
        print(f"{year:<6} {expected:<10} {physical:<10} {missing:<10} {status}")
    
    print()
    
    # Priority recommendations
    print("🎯 PRIORITY RECOMMENDATIONS:")
    
    # Sort by number of missing articles (highest first)
    priority_years = sorted(missing_by_year.items(), key=lambda x: x[1], reverse=True)
    
    for i, (year, missing_count) in enumerate(priority_years[:3], 1):
        print(f"   {i}. {year}: {missing_count} missing articles (highest priority)")
    
    print()
    
    # Generate targeted download strategy
    print("📋 SUGGESTED DOWNLOAD STRATEGY:")
    print("   1. Focus on years with most missing articles first")
    print("   2. Use year-specific archive pages for targeted scanning")
    print("   3. Cross-reference with main blog pagination")
    print()
    
    # Save analysis
    analysis = {
        'analysis_date': datetime.now().isoformat(),
        'expected_counts': expected_counts,
        'physical_counts': dict(physical_counts),
        'missing_by_year': missing_by_year,
        'total_expected': total_expected,
        'total_physical': total_physical,
        'total_missing': total_missing,
        'priority_years': [year for year, _ in priority_years]
    }
    
    report_path = Path(config.OUTPUT_DIR) / "metadata" / "targeted_gap_analysis.json"
    report_path.parent.mkdir(parents=True, exist_ok=True)
    
    with open(report_path, 'w', encoding='utf-8') as f:
        json.dump(analysis, f, indent=2)
    
    print(f"📋 Analysis saved to: {report_path}")
    
    return missing_by_year

def generate_year_specific_urls(missing_by_year):
    """Generate year-specific URLs to scan for missing articles"""
    
    print("\n🔗 YEAR-SPECIFIC SCAN URLS:")
    print("Use these URLs to manually verify or scan specific years:")
    print()
    
    base_url = "https://gerrysdiamondsettingessays.blogspot.com"
    
    for year in sorted(missing_by_year.keys(), reverse=True):
        missing_count = missing_by_year[year]
        print(f"📅 {year} (missing {missing_count} articles):")
        
        # Generate potential URLs for this year
        year_urls = [
            f"{base_url}/search?updated-min={year}-01-01T00:00:00-05:00&updated-max={year+1}-01-01T00:00:00-05:00&max-results=50",
            f"{base_url}/{year}/",
            f"{base_url}/search/label/{year}",
        ]
        
        # Add monthly archive URLs
        for month in range(1, 13):
            month_str = f"{month:02d}"
            year_urls.append(f"{base_url}/{year}_{month_str}_01_archive.html")
        
        for url in year_urls[:5]:  # Show first 5 URLs
            print(f"   {url}")
        print()

def main():
    missing_by_year = analyze_physical_vs_expected()
    
    if missing_by_year:
        generate_year_specific_urls(missing_by_year)
        
        print("🤔 NEXT STEPS:")
        print("   1. Run comprehensive_blog_scan.py to find missing articles")
        print("   2. Or manually check the year-specific URLs above")
        print("   3. Focus on high-priority years (2024, 2022, 2023) first")
        print("   4. Use the main crawler to download missing articles")
    else:
        print("✅ No missing articles detected!")

if __name__ == "__main__":
    main()
