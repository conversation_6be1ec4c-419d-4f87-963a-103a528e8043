#!/usr/bin/env python3
"""
Test Single Article Download

Test the author field fix with a single article before running full parallel download.
"""

import json
from pathlib import Path
from crawler import BlogCrawler

def test_single_download():
    print("=" * 60)
    print("🧪 TESTING SINGLE ARTICLE DOWNLOAD")
    print("=" * 60)
    
    # Load missing articles
    report_file = Path('output/metadata/correct_comparison_report.json')
    if not report_file.exists():
        print("❌ Comparison report not found")
        return
    
    with open(report_file, 'r') as f:
        data = json.load(f)
    
    missing_articles = data.get('missing_articles', [])
    if not missing_articles:
        print("❌ No missing articles found")
        return
    
    # Test with first missing article
    test_article = missing_articles[0]
    print(f"🎯 Testing with: {test_article['title']}")
    print(f"📍 URL: {test_article['url']}")
    print(f"📅 Year: {test_article['year']}")
    
    # Create crawler
    crawler = BlogCrawler()
    
    # Convert to post format (with author field fix)
    post = {
        'url': test_article['url'],
        'title': test_article['title'],
        'year': test_article.get('year', 'unknown'),
        'author': '<PERSON>',  # Default author
        'date': f"{test_article.get('year', 'unknown')}-01-01",  # Default date
        'source': 'test_single_download'
    }
    
    print(f"📝 Post data: {post}")
    
    # Test download
    print("\n🚀 Starting download test...")
    try:
        success = crawler.download_post_content(post)
        if success:
            print("✅ SUCCESS: Article downloaded successfully!")
            print(f"📁 Saved to: {success}")
        else:
            print("❌ FAILED: Article download failed")
    except Exception as e:
        print(f"❌ ERROR: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_single_download()
