<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON>'s Diamond Setting Essays - Complete Archive</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .header {
            text-align: center;
            padding: 60px 20px;
            color: white;
        }

        .header h1 {
            font-size: 3em;
            font-weight: 700;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
            max-width: 600px;
            margin: 0 auto;
        }

        .stats {
            display: flex;
            justify-content: center;
            gap: 40px;
            margin-top: 30px;
            flex-wrap: wrap;
        }

        .stat {
            text-align: center;
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 10px;
            backdrop-filter: blur(10px);
        }

        .stat-number {
            font-size: 2em;
            font-weight: bold;
            display: block;
        }

        .stat-label {
            font-size: 0.9em;
            opacity: 0.8;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px 20px 0 0;
            min-height: 60vh;
            box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);
        }

        .search-bar {
            padding: 30px;
            border-bottom: 1px solid #eee;
            text-align: center;
        }

        .search-input {
            width: 100%;
            max-width: 500px;
            padding: 15px 20px;
            font-size: 1.1em;
            border: 2px solid #ddd;
            border-radius: 25px;
            outline: none;
            transition: border-color 0.3s ease;
        }

        .search-input:focus {
            border-color: #667eea;
        }

        .year-summary-section {
            padding: 30px;
            background: white;
            margin-bottom: 20px;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .year-summary-section h2 {
            color: #2c3e50;
            margin-bottom: 20px;
            text-align: center;
        }

        .year-summary-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
        }

        .year-summary {
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 15px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .year-summary:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        .year-summary.active {
            background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
        }

        .year-label {
            font-size: 1.2em;
        }

        .controls {
            display: flex;
            gap: 20px;
            align-items: center;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }

        .filter-controls {
            display: flex;
            gap: 10px;
        }

        .year-filter, .sort-select {
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 8px;
            font-size: 1em;
        }

        .posts-container {
            padding: 30px;
        }

        .year-section {
            margin-bottom: 40px;
        }

        .year-header {
            color: #2c3e50;
            font-size: 1.8em;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 3px solid #667eea;
        }

        .posts-grid {
            display: grid;
            gap: 20px;
        }

        .post-item {
            display: flex;
            align-items: center;
            padding: 20px;
            border: 1px solid #eee;
            border-radius: 12px;
            transition: all 0.3s ease;
            background: #fafafa;
        }

        .post-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            border-color: #667eea;
        }

        .post-number {
            font-size: 1.2em;
            font-weight: bold;
            color: #667eea;
            margin-right: 20px;
            min-width: 40px;
        }

        .post-details {
            flex: 1;
        }

        .post-details h3 {
            margin-bottom: 8px;
        }

        .post-details h3 a {
            color: #2c3e50;
            text-decoration: none;
            font-weight: 600;
            font-size: 1.1em;
        }

        .post-details h3 a:hover {
            color: #667eea;
        }

        .post-meta {
            display: flex;
            gap: 20px;
            font-size: 0.9em;
            color: #666;
            flex-wrap: wrap;
        }

        .footer {
            text-align: center;
            padding: 40px;
            color: #666;
            border-top: 1px solid #eee;
        }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 2.2em;
            }

            .stats {
                gap: 20px;
            }

            .stat {
                padding: 15px;
            }

            .container {
                margin: 10px;
                border-radius: 15px;
            }

            .post-item {
                flex-direction: column;
                align-items: flex-start;
                text-align: left;
            }

            .post-number {
                margin-bottom: 10px;
            }

            .post-meta {
                flex-direction: column;
                gap: 5px;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Gerry's Diamond Setting Essays</h1>
        <p>Complete offline archive of professional diamond setting tutorials and techniques</p>

        <div class="stats">
            <div class="stat">
                <span class="stat-number">0</span>
                <span class="stat-label">Essays</span>
            </div>
            <div class="stat">
                <span class="stat-number">0</span>
                <span class="stat-label">Images</span>
            </div>
            <div class="stat">
                <span class="stat-number">100%</span>
                <span class="stat-label">Offline</span>
            </div>
        </div>
    </div>

    <div class="container">
        <div class="year-summary-section">
            <h2>Posts by Year</h2>
            <div class="year-summary-grid">
                
            </div>
        </div>

        <div class="controls">
            <div class="search-bar">
                <input type="text" class="search-input" placeholder="Search essays by title..." id="searchInput">
            </div>
            <div class="filter-controls">
                <select id="yearFilter" class="year-filter">
                    <option value="">All Years</option>
                </select>
                <select id="sortBy" class="sort-select">
                    <option value="title">Sort by Title</option>
                    <option value="year">Sort by Year</option>
                    <option value="date">Sort by Date</option>
                </select>
            </div>
        </div>

        <div class="posts-container" id="postsContainer">
            <div class="posts-grid">
                
            </div>
        </div>

        <div class="footer">
            <p>Archive generated on July 06, 2025</p>
            <p>Original blog: <a href="https://gerrysdiamondsettingessays.blogspot.com" target="_blank">gerrysdiamondsettingessays.blogspot.com</a></p>
        </div>
    </div>

    <script>
        // Initialize functionality
        document.addEventListener('DOMContentLoaded', function() {
            // Populate year filter
            const yearFilter = document.getElementById('yearFilter');
            const years = [...new Set(Array.from(document.querySelectorAll('.post-item')).map(post => post.dataset.year))].sort((a, b) => b - a);
            years.forEach(year => {
                if (year && year !== 'None') {
                    const option = document.createElement('option');
                    option.value = year;
                    option.textContent = year;
                    yearFilter.appendChild(option);
                }
            });

            // Search functionality
            document.getElementById('searchInput').addEventListener('input', filterPosts);

            // Year filter functionality
            yearFilter.addEventListener('change', filterPosts);

            // Sort functionality
            document.getElementById('sortBy').addEventListener('change', sortPosts);

            // Year summary click functionality
            document.querySelectorAll('.year-summary').forEach(summary => {
                summary.addEventListener('click', function() {
                    const year = this.dataset.year;
                    yearFilter.value = year;
                    filterPosts();

                    // Update active state
                    document.querySelectorAll('.year-summary').forEach(s => s.classList.remove('active'));
                    this.classList.add('active');
                });
            });
        });

        function filterPosts() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            const selectedYear = document.getElementById('yearFilter').value;
            const posts = document.querySelectorAll('.post-item');

            posts.forEach(post => {
                const title = post.querySelector('h3 a').textContent.toLowerCase();
                const postYear = post.dataset.year;

                const matchesSearch = title.includes(searchTerm);
                const matchesYear = !selectedYear || postYear === selectedYear;

                if (matchesSearch && matchesYear) {
                    post.style.display = 'flex';
                } else {
                    post.style.display = 'none';
                }
            });

            // Update post numbers
            let visibleIndex = 1;
            posts.forEach(post => {
                if (post.style.display !== 'none') {
                    post.querySelector('.post-number').textContent = String(visibleIndex).padStart(2, '0');
                    visibleIndex++;
                }
            });
        }

        function sortPosts() {
            const sortBy = document.getElementById('sortBy').value;
            const postsContainer = document.querySelector('.posts-grid');
            const posts = Array.from(document.querySelectorAll('.post-item'));

            posts.sort((a, b) => {
                if (sortBy === 'title') {
                    return a.querySelector('h3 a').textContent.localeCompare(b.querySelector('h3 a').textContent);
                } else if (sortBy === 'year') {
                    return b.dataset.year - a.dataset.year;
                } else if (sortBy === 'date') {
                    const dateA = a.querySelector('.date').textContent;
                    const dateB = b.querySelector('.date').textContent;
                    return dateB.localeCompare(dateA);
                }
            });

            // Re-append sorted posts
            posts.forEach(post => postsContainer.appendChild(post));

            // Update post numbers
            posts.forEach((post, index) => {
                post.querySelector('.post-number').textContent = String(index + 1).padStart(2, '0');
            });
        }
    </script>
</body>
</html>