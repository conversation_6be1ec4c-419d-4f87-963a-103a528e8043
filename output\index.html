<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON>'s Diamond Setting Essays - Complete Archive</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .header {
            text-align: center;
            padding: 60px 20px;
            color: white;
        }

        .header h1 {
            font-size: 3em;
            font-weight: 700;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
            max-width: 600px;
            margin: 0 auto;
        }

        .stats {
            display: flex;
            justify-content: center;
            gap: 40px;
            margin-top: 30px;
            flex-wrap: wrap;
        }

        .stat {
            text-align: center;
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 10px;
            backdrop-filter: blur(10px);
        }

        .stat-number {
            font-size: 2em;
            font-weight: bold;
            display: block;
        }

        .stat-label {
            font-size: 0.9em;
            opacity: 0.8;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px 20px 0 0;
            min-height: 60vh;
            box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);
        }

        .search-bar {
            padding: 30px;
            border-bottom: 1px solid #eee;
            text-align: center;
        }

        .search-input {
            width: 100%;
            max-width: 500px;
            padding: 15px 20px;
            font-size: 1.1em;
            border: 2px solid #ddd;
            border-radius: 25px;
            outline: none;
            transition: border-color 0.3s ease;
        }

        .search-input:focus {
            border-color: #667eea;
        }

        .posts-grid {
            padding: 30px;
            display: grid;
            gap: 20px;
        }

        .post-item {
            display: flex;
            align-items: center;
            padding: 20px;
            border: 1px solid #eee;
            border-radius: 12px;
            transition: all 0.3s ease;
            background: #fafafa;
        }

        .post-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            border-color: #667eea;
        }

        .post-number {
            font-size: 1.2em;
            font-weight: bold;
            color: #667eea;
            margin-right: 20px;
            min-width: 40px;
        }

        .post-details {
            flex: 1;
        }

        .post-details h3 {
            margin-bottom: 8px;
        }

        .post-details h3 a {
            color: #2c3e50;
            text-decoration: none;
            font-weight: 600;
            font-size: 1.1em;
        }

        .post-details h3 a:hover {
            color: #667eea;
        }

        .post-meta {
            display: flex;
            gap: 20px;
            font-size: 0.9em;
            color: #666;
            flex-wrap: wrap;
        }

        .footer {
            text-align: center;
            padding: 40px;
            color: #666;
            border-top: 1px solid #eee;
        }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 2.2em;
            }

            .stats {
                gap: 20px;
            }

            .stat {
                padding: 15px;
            }

            .container {
                margin: 10px;
                border-radius: 15px;
            }

            .post-item {
                flex-direction: column;
                align-items: flex-start;
                text-align: left;
            }

            .post-number {
                margin-bottom: 10px;
            }

            .post-meta {
                flex-direction: column;
                gap: 5px;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Gerry's Diamond Setting Essays</h1>
        <p>Complete offline archive of professional diamond setting tutorials and techniques</p>

        <div class="stats">
            <div class="stat">
                <span class="stat-number">0</span>
                <span class="stat-label">Essays</span>
            </div>
            <div class="stat">
                <span class="stat-number">48</span>
                <span class="stat-label">Images</span>
            </div>
            <div class="stat">
                <span class="stat-number">100%</span>
                <span class="stat-label">Offline</span>
            </div>
        </div>
    </div>

    <div class="container">
        <div class="search-bar">
            <input type="text" class="search-input" placeholder="Search essays by title..." id="searchInput">
        </div>

        <div class="posts-grid" id="postsGrid">
            
        </div>

        <div class="footer">
            <p>Archive generated on July 05, 2025</p>
            <p>Original blog: <a href="https://gerrysdiamondsettingessays.blogspot.com" target="_blank">gerrysdiamondsettingessays.blogspot.com</a></p>
        </div>
    </div>

    <script>
        // Simple search functionality
        document.getElementById('searchInput').addEventListener('input', function(e) {
            const searchTerm = e.target.value.toLowerCase();
            const posts = document.querySelectorAll('.post-item');

            posts.forEach(post => {
                const title = post.querySelector('h3 a').textContent.toLowerCase();
                if (title.includes(searchTerm)) {
                    post.style.display = 'flex';
                } else {
                    post.style.display = 'none';
                }
            });
        });
    </script>
</body>
</html>