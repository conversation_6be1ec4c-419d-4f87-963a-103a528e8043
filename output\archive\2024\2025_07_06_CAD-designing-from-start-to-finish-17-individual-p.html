<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CAD designing, from 'start to finish' - 17 individual photos.</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #fafafa;
            padding: 20px;
        }

        .container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .post-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px;
            text-align: center;
        }

        .post-title {
            font-size: 2.2em;
            font-weight: 700;
            margin-bottom: 15px;
            line-height: 1.2;
        }

        .post-meta {
            font-size: 0.95em;
            opacity: 0.9;
            display: flex;
            justify-content: center;
            gap: 20px;
            flex-wrap: wrap;
        }

        .post-meta a {
            color: #fff;
            text-decoration: underline;
        }

        .post-content {
            padding: 40px;
            font-size: 1.1em;
            line-height: 1.8;
        }

        /* Font normalization - override all inline styles */
        .post-content * {
            font-family: inherit !important;
            font-size: inherit !important;
            line-height: inherit !important;
        }

        .post-content p {
            margin-bottom: 1.2em;
            font-size: 1.1em !important;
        }

        .post-content div {
            font-size: 1.1em !important;
        }

        .post-content span {
            font-size: inherit !important;
        }

        .post-content img {
            max-width: 100%;
            height: auto;
            border-radius: 8px;
            margin: 20px 0;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
        }

        .post-content h1, .post-content h2, .post-content h3 {
            margin: 1.5em 0 0.8em 0;
            color: #2c3e50;
            font-size: 1.4em !important;
            font-weight: 600 !important;
        }

        .post-content strong, .post-content b {
            color: #2c3e50;
            font-weight: 600 !important;
        }

        .post-content em, .post-content i {
            color: #555;
            font-style: italic !important;
        }

        .separator {
            margin: 2em 0;
        }

        .back-link {
            position: fixed;
            top: 20px;
            left: 20px;
            background: #667eea;
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: 500;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;
        }

        .back-link:hover {
            background: #5a6fd8;
            transform: translateY(-2px);
        }

        @media (max-width: 768px) {
            .container {
                margin: 10px;
                border-radius: 8px;
            }

            .post-header {
                padding: 30px 20px;
            }

            .post-title {
                font-size: 1.8em;
            }

            .post-content {
                padding: 30px 20px;
            }

            .post-meta {
                flex-direction: column;
                gap: 10px;
            }
        }
    </style>
</head>
<body>
    <a href="../../../index.html" class="back-link">← Back to Index</a>

    <div class="container">
        <div class="post-header">
            <h1 class="post-title">CAD designing, from 'start to finish' - 17 individual photos.</h1>
            <div class="post-meta">
                <span>By: Posted byGerry Lewy</span>
                <span>Date: 2024-06-12T17:28:00-04:00</span>
                <span><a href="https://gerrysdiamondsettingessays.blogspot.com/2024/06/cad-designing-from-start-to-finish-9.html" target="_blank">View Original</a></span>
            </div>
        </div>
        <div class="post-content">
            <div class="post-body entry-content" id="post-body-5860490545889983424" itemprop="description articleBody">
<p></p><div class="separator" style="clear: both; text-align: center;"><span style="font-size: large;"><br/></span></div><div class="separator" style="clear: both; text-align: justify;"><i><b><span style="font-size: large;">    "C.A.D." is an abbreviation for "<u>C</u>omputer <u>A</u>ided <u>D</u>esigning".</span></b></i></div><div class="separator" style="clear: both; text-align: justify;"><i><b><span style="font-size: large;"><br/></span></b></i></div><div class="separator" style="clear: both; text-align: justify;"><i><b><span style="font-size: medium;"> If you go to the computer website "<u>Gem Reporter</u>" you will see how this CAD program helps the jeweller, model-maker and the CAD computer operator.</span></b></i></div><div class="separator" style="clear: both; text-align: justify;"><i><b><span style="font-size: medium;"> This program will give you information of the number of diamonds needed, dimensions of each stone and the total carat weights. "Is this a WOW program"?</span></b></i></div><div class="separator" style="clear: both; text-align: justify;"><i><b><span style="font-size: medium;"> In the lower screen it will explain the Karat gold required with Gram or Dwt's. There is nothing remaining in deciding facts in manufacturing a diamond ring. Where was this remarkable program many, many decades ago?</span></b></i></div><div class="separator" style="clear: both; text-align: justify;"><i><b><span style="font-size: medium;"><br/></span></b></i></div><div class="separator" style="clear: both; text-align: center;"><a href="../../images/unknown/CAD-designing-from-start-to-finish-17-individual-p_img_001.png" style="margin-left: 1em; margin-right: 1em;"><img alt="" data-original-height="1124" data-original-width="2069" height="347" loading="lazy" src="../../images/unknown/CAD-designing-from-start-to-finish-17-individual-p_img_001.png" width="640"/></a></div><div class="separator" style="clear: both; text-align: justify;"><span style="font-size: medium; text-align: left;"><i><b> This "CAD" program will also advise the 'model-maker' and caster where the 'casting gates' should be located. All that remains is to cast the metal of  choice.</b></i></span></div><div class="separator" style="clear: both; text-align: center;"><a href="https://blogger.googleusercontent.com/img/b/R29vZ2xl/AVvXsEiLbW0uF9EZvuRx4VrQbQ35zRAMzcD_ADRZmrgiGSqg6pWD9eqijaBpKAz4D4TLtYPM4vYnXk6A6N11NlqKGQLQ4FTvCC0CtAUK5v7iUs1y12TAKLJs3QW7pHUsV3r09bb-2fyXFESgXiA-2Z-dgc-b3m3eXtfpfppRBEOTWPKY1zlvvAtuM0bJyNw7x58/s2861/CAD%20&amp;%20Renduring%20001.bmp" style="margin-left: 1em; margin-right: 1em;"><img border="0" data-original-height="2861" data-original-width="2185" height="640" loading="lazy" src="../../images/unknown/CAD-designing-from-start-to-finish-17-individual-p_img_002.png" width="489"/></a></div><div class="separator" style="clear: both; text-align: justify;"><i><b><span style="font-size: medium;"> All the operator must do is to insert the gems needed and this program will make the best available pattern in creating that ring.</span></b></i></div><div class="separator" style="clear: both; text-align: justify;"><i><b><span style="font-size: medium;">Here is the simulated ring as it could be seen if it was a completed &amp; finished ring.</span></b></i></div><div class="separator" style="clear: both; text-align: justify;"><i><b><div class="separator" style="clear: both; text-align: center;"><a href="https://blogger.googleusercontent.com/img/b/R29vZ2xl/AVvXsEhqW_aO7Xn_nfTD4MtZ9hAv5pAXXBJruLecPLDIIkaJq68EUgLPoxl4aQPZ2RxKgAc4t7KwRZSXdV1fYGwgkjm0bktGlRf1NyH7oiQ1vpHZDYW3-EKUWksPFIhEyEgtavrvcPaoiyBRS9h0LgOAUGXxVb6wLSAzfm6KSwmCSju0nfWz7iDKFCK4l5eDR5k/s2770/CAD-ready%204%20casting%20001.bmp" style="margin-left: 1em; margin-right: 1em;"><img border="0" data-original-height="2770" data-original-width="2003" height="640" loading="lazy" src="../../images/unknown/CAD-designing-from-start-to-finish-17-individual-p_img_003.png" width="462"/></a></div> This is my design, it is named "Waterfall" as it looks like water falling down the edge of the top of the ring.<br/><div class="separator" style="clear: both; text-align: center;"><a href="https://blogger.googleusercontent.com/img/b/R29vZ2xl/AVvXsEissrnf2RZTzcV6jpgYGZ47ASXJSwNYLY7qfOChhs27dlDDf1t1WEHHskKn3jdjF90Wt75M9xMboozG278WBVUxZIniNh-KlIRvpvkw3eOI-4ZkmjA3GCmxzWqnfEDBnO_7aT6cDoelbggYdy9zG9k8MEp2DeZrzsS10a3cpYVZsCerYhpRfFWji7itRmo/s2448/CAD-'Waterfall'%20001.bmp" style="margin-left: 1em; margin-right: 1em;"><img border="0" data-original-height="2448" data-original-width="1614" height="640" loading="lazy" src="../../images/unknown/CAD-designing-from-start-to-finish-17-individual-p_img_004.png" width="422"/></a></div><br/><span style="font-size: medium;"><br/></span></b></i></div><br/><div class="separator" style="clear: both; text-align: center;"><a href="https://blogger.googleusercontent.com/img/b/R29vZ2xl/AVvXsEicsulM7QI8U9b0NWPHcs0GNft9hRV6jIiHT_YfbKuPioMy8ZScqyZ_Y8XxHnlUMrvBq_7E9YcIJwRfJ4Cb4h7CmFkg42Jz8RiVDKK8eP6aYQbZBCkOe-yCXxAVFhCJLOU62m1CKMjjBTjF7YbtOEo4yKkrdpzhVSu36mTIOrs_3vtGOcaTeWO2dvrEIyY/s2069/CAD-%20beginning%20001.bmp" style="margin-left: 1em; margin-right: 1em;"><img border="0" data-original-height="1124" data-original-width="2069" height="348" loading="lazy" src="../../images/unknown/CAD-designing-from-start-to-finish-17-individual-p_img_005.png" width="640"/></a></div><div style="text-align: justify;">  <i><b><span style="font-size: medium;">I visually enhanced the perimeter border by "Bright-Cutting" with my engraving tools, namely the Flat and Onglette gravers.</span></b></i></div><p></p><div class="separator" style="clear: both; text-align: center;"><a href="https://blogger.googleusercontent.com/img/b/R29vZ2xl/AVvXsEgzkOjm5yUvEJahxr5glSYajsatZ9BZufNSWHohQNIi6IM0vsqt1hhaFMh5yWvB0b82lkfM70oAod57ROaAr8PLmw9BDv5RJgcSVv0fiGwQTR83b_JXmPxoOgSM3DZ52vxL0QqfjGyKvWTFGg6pwzT8X74W2SAQnnfi7JwzqQ9zCsRLIDZ-knAaAgwDKvY/s800/DSC00129.JPG" style="margin-left: 1em; margin-right: 1em;"><img border="0" data-original-height="600" data-original-width="800" height="480" loading="lazy" src="../../images/unknown/CAD-designing-from-start-to-finish-17-individual-p_img_006.png" width="640"/></a></div><div class="separator" style="clear: both; text-align: center;"><br/></div><div style="text-align: justify;"> <i><b><span style="font-size: medium;"> This pendant was created solely by CAD. There wasn't any need to sit and create "Beads and Bezels" by hand. What a tedious and days long chore that must have been?</span></b></i></div><div style="text-align: justify;"><i><b><span style="font-size: medium;"> It made the job even for the stone-setter a delightful task, I know this for a fact when I was setting the stones.</span></b></i></div><div class="separator" style="clear: both; text-align: center;"><br/></div><br/><div class="separator" style="clear: both; text-align: center;"><a href="https://blogger.googleusercontent.com/img/b/R29vZ2xl/AVvXsEjc5eQTvMS-M5Q1wIaTcjnNxOb3jnyvN7J8pb-_9W04-77YB3PiLdMAPph0UCxXbU9MBBZlA0pBc-WfjoefiHwVsYK0tj38MN451kbpbPMkU3eoD3e-rnafb9jtsEQjYsigGk1zlQM_lI3xv1nxDq6WqwWQQexlT_965EhTMW8SxF00n1oEv6L_MAEbXEc/s800/DSC00131.JPG" style="margin-left: 1em; margin-right: 1em;"><img border="0" data-original-height="600" data-original-width="800" height="480" loading="lazy" src="../../images/unknown/CAD-designing-from-start-to-finish-17-individual-p_img_007.png" width="640"/></a></div><div><br/></div><div><br/></div><div style="text-align: justify;">   <i><b><span style="font-size: medium;">This 'CAD' program illustrates where the Beads &amp; Bezels will be then situated.</span></b></i></div><div class="separator" style="clear: both; text-align: center;"><a href="https://blogger.googleusercontent.com/img/b/R29vZ2xl/AVvXsEjAs3vtxpidMn734wwJeLxDa3bW6_UUXzhyc9sXI4Co_c0qYEeiAPiypmTCvAKA83A0Xxo_Z8Jp9-os8UAD3q1JIWNTRegBnZDSa2eV9TNaJoWzWJ4K7WzDIpW7GPdv1NK0TTAfObI_NYphBbUkMuMLb7ar0lmvzzZ8c7Ng33XrcyUAJI967TQIX4XUf0Q/s800/PrespectiveKiteDiamond1.jpg" style="margin-left: 1em; margin-right: 1em;"><img border="0" data-original-height="501" data-original-width="800" height="400" loading="lazy" src="../../images/unknown/CAD-designing-from-start-to-finish-17-individual-p_img_008.png" width="640"/></a></div><div><br/></div><div style="text-align: justify;"> <i><b><span style="font-size: medium;">From a little CAD drawing on the computer screen, the operator can decide along with the client where the beads would be located. </span></b></i></div><div style="text-align: justify;"><i><b><span style="font-size: medium;"> Be forewarned, that the 'CAD operator' must have a working knowledge of Diamond Setting and jewellery manufacturing. There are many horror stories in my library of those people who didn't have such a jewellery background and how they blamed the setter for their own lack of training.</span></b></i></div><div class="separator" style="clear: both; text-align: center;"><a href="https://blogger.googleusercontent.com/img/b/R29vZ2xl/AVvXsEje3uXSvFPY7s4UBol6MQVOuHQG5c78sFl5b-A18WKE3zze6USrMMCm1clDolW5IB99I9omtUXR3LhSV74ag873xkfoH5Ds3j13ggAiJGHNtax1dkC7kWVm2ePTF5MY8EBPIilIq5Rx3_BjbPi7wXLOaMddZcRc1aaot-nBSiXEJDswgR3PZC1PHa6hRt8/s651/SkiPendant1.jpg" style="margin-left: 1em; margin-right: 1em;"><img border="0" data-original-height="651" data-original-width="438" height="640" loading="lazy" src="../../images/unknown/CAD-designing-from-start-to-finish-17-individual-p_img_009.png" width="430"/></a></div><div class="separator" style="clear: both; text-align: center;"><br/></div><div class="separator" style="clear: both; text-align: justify;"><i><b><span style="font-size: medium;"> Here is the finished pendant being made ready to be worn for many years and admired.</span></b></i></div><br/><div class="separator" style="clear: both; text-align: center;"><a href="https://blogger.googleusercontent.com/img/b/R29vZ2xl/AVvXsEgY6h5ftHmDR1D5KL-pigAjrJ0FtqGRBHIMNs4FWQkRPNgkrOGx6nqcQQvh9fyz6DEXY65ZNJ19n7r2cTy-rBrU8twRFzGdWNwNR6RnSCuoiODZKz50ZDcYPWbUeylurvceAMljSjETsi1Qz-N7bAI7lir2Uz_gtser6VbW5InPmT2saVGMOnpUuq-MkJY/s800/DSC00140.JPG" style="margin-left: 1em; margin-right: 1em;"><img border="0" data-original-height="600" data-original-width="800" height="480" loading="lazy" src="../../images/unknown/CAD-designing-from-start-to-finish-17-individual-p_img_010.png" width="640"/></a></div><br/><div style="text-align: justify;"> <b><span style="font-size: medium;">This was one of the most complex projects that I have ever had. The time spent in setting these stones was not the issue, it was the complexity of getting the stones set.</span></b></div><div class="separator" style="clear: both; text-align: center;"><a href="https://blogger.googleusercontent.com/img/b/R29vZ2xl/AVvXsEjFVKpfjiUSb5QCubKJkfUEw9pWxjOOH6z3g_A5qNzu3AX1UQznyguXHMODr7D72OdcpDoWQeZ_12IbLpz_fNRt3AWw3gGeK-oHFAyCb6pi8PeRj11Ggm3AsnBg4L48yC_MIYOn2dErE_LPfQYLp6WiZ2gxqduSO5uo-KDdMxkIFAsFKrZprGi3QRTeUm8/s1024/DSC00003.JPG" style="margin-left: 1em; margin-right: 1em;"><img border="0" data-original-height="768" data-original-width="1024" height="480" loading="lazy" src="../../images/unknown/CAD-designing-from-start-to-finish-17-individual-p_img_011.png" width="640"/></a></div><br/><div class="separator" style="clear: both; text-align: center;"><a href="https://blogger.googleusercontent.com/img/b/R29vZ2xl/AVvXsEjwjYXGpCVWvZgRQgyacfKy0e_IIv8LGaN0ZGuUdCuLYGUO1knWDYVaIAuKFZ_RTXC5i7pO70WtVfhHFq6727fXUV89-TjsLag65F0iL4Dy12UZtBKgo2mSeduOA1YlDJzwhr54vKpK75VoQlDRZkx_BgeJ2sTU9YFsHpC5Z2GevQmG4mBBoEK6c2uc904/s1024/DSC00004.JPG" style="margin-left: 1em; margin-right: 1em;"><img border="0" data-original-height="768" data-original-width="1024" height="480" loading="lazy" src="../../images/unknown/CAD-designing-from-start-to-finish-17-individual-p_img_012.png" width="640"/></a></div><div><br/></div><div style="text-align: justify;"> <i><b><span style="font-size: medium;">Here is a close-up of all of the stone-setting. As you can see there were vertical as well as many 45-degree angles that were on the side of the 'face'. </span></b></i></div><div class="separator" style="clear: both; text-align: center;"><a href="https://blogger.googleusercontent.com/img/b/R29vZ2xl/AVvXsEhWAY92RLgrprx1ATqgaKzZk1dICesqidWMDwAOuoJeQq_gPWIZdOEwbx2BBjkZeSijjIolJHNc5oAKPqhHNgU6wUE-j3BkgB3KBCoBgOeCc-lHFotjcS-ZBbrb3JDRpfggTvFLybA9SvUEbrM1KjerLwE1yinCNdF4FZfiErVkI-5RI9x3Nu7PohHfstg/s1600/DSC00007.JPG" style="margin-left: 1em; margin-right: 1em;"><img border="0" data-original-height="1200" data-original-width="1600" height="480" loading="lazy" src="../../images/unknown/CAD-designing-from-start-to-finish-17-individual-p_img_013.png" width="640"/></a></div><div><br/></div><div style="text-align: justify;"><i><b><span style="font-size: medium;"> Imagine making sure that all of the stones were level and every stone was securely tightened. WOW! My eyes were tired after all of this intricate stone setting. Only 2 mini-claws were holding EACH stone.</span></b></i></div><div class="separator" style="clear: both; text-align: center;"><a href="https://blogger.googleusercontent.com/img/b/R29vZ2xl/AVvXsEhERMEeXvo7w25mFtN2F-YSMKk8pGlT7iPUVVpQUgj6HSfLtu7Vrn6xawlG0lCoy36x8eWs3zZuUWAARRqFNS9vckrqkCUlM8XHFl2LYELkpjQPZb435QJZobXWrASJAsWObQ8Dq6aOt5lnQYh7KJdZ0oktjLZKs_I3RQBX8qeEltRwAu0mdTXWyIsVoIA/s1600/DSC00008.JPG" style="margin-left: 1em; margin-right: 1em;"><img border="0" data-original-height="1200" data-original-width="1600" height="480" loading="lazy" src="../../images/unknown/CAD-designing-from-start-to-finish-17-individual-p_img_014.png" width="640"/></a></div><br/><div style="text-align: justify;">  <i><b><span style="font-size: medium;">The time spent in the setting was a minor issue, but I was thinking who would enjoy these photos? I DID!</span></b></i></div>
<div style="clear: both;"></div>
</div>
        </div>
    </div>
</body>
</html>