<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>"Computer Aided Designing" (aka CAD) for easier Diamond Setting!</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #fafafa;
            padding: 20px;
        }

        .container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .post-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px;
            text-align: center;
        }

        .post-title {
            font-size: 2.2em;
            font-weight: 700;
            margin-bottom: 15px;
            line-height: 1.2;
        }

        .post-meta {
            font-size: 0.95em;
            opacity: 0.9;
            display: flex;
            justify-content: center;
            gap: 20px;
            flex-wrap: wrap;
        }

        .post-meta a {
            color: #fff;
            text-decoration: underline;
        }

        .post-content {
            padding: 40px;
            font-size: 1.1em;
            line-height: 1.8;
        }

        .post-content img {
            max-width: 100%;
            height: auto;
            border-radius: 8px;
            margin: 20px 0;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
        }

        .post-content p {
            margin-bottom: 1.2em;
        }

        .post-content h1, .post-content h2, .post-content h3 {
            margin: 1.5em 0 0.8em 0;
            color: #2c3e50;
        }

        .post-content strong, .post-content b {
            color: #2c3e50;
            font-weight: 600;
        }

        .post-content em, .post-content i {
            color: #555;
        }

        .separator {
            margin: 2em 0;
        }

        .back-link {
            position: fixed;
            top: 20px;
            left: 20px;
            background: #667eea;
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: 500;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;
        }

        .back-link:hover {
            background: #5a6fd8;
            transform: translateY(-2px);
        }

        @media (max-width: 768px) {
            .container {
                margin: 10px;
                border-radius: 8px;
            }

            .post-header {
                padding: 30px 20px;
            }

            .post-title {
                font-size: 1.8em;
            }

            .post-content {
                padding: 30px 20px;
            }

            .post-meta {
                flex-direction: column;
                gap: 10px;
            }
        }
    </style>
</head>
<body>
    <a href="../../../index.html" class="back-link">← Back to Index</a>

    <div class="container">
        <div class="post-header">
            <h1 class="post-title">"Computer Aided Designing" (aka CAD) for easier Diamond Setting!</h1>
            <div class="post-meta">
                <span>By: Posted byGerry Lewy</span>
                <span>Date: 2019-04-02T20:06:00-04:00</span>
                <span><a href="https://gerrysdiamondsettingessays.blogspot.com/2019/04/computer-aided-designing-aka-cad-for.html" target="_blank">View Original</a></span>
            </div>
        </div>
        <div class="post-content">
            <div class="post-body entry-content" id="post-body-762774455864176942" itemprop="description articleBody">
<div dir="ltr" style="text-align: left;" trbidi="on">
<br/>
 <b>When I was learning to be a Diamond Setter back in early 1960's,</b> <i>a 'model-maker' would sit for <b>2-3 DAYS</b> and hand-sculpture <u>one single (bracelet) charm</u>. Times have changed and now it possible to make the same creation in only a few minutes! <b>All of this using a basic C.A.D. program! </b></i><br/>
<br/>
 <b><i>In my inventory of CAD related photographs that I've been collecting on this topic, I going to share these with you now!</i></b><br/>
<b><i><br/></i></b>
 <i><b>The following pieces could not have been made by hand, if so, these would have taken many days just for one piece.</b> From the computer being programmed, these can be formed with a '3-D printer' overnight, the next morning they are ready for metal casting!</i><br/>
<i><br/></i>
<br/>
<h3 style="text-align: left;">
<i><b>Tools that are needed:</b></i><i><b> 1)Your "Computer Aided Design" program!</b></i><i><b> 2)Your willingness to explore different styles!</b></i></h3>
<b style="text-align: justify;"><i>Instead of "Rough-Cutting" a pattern for setting, then "Bright-Cutting" afterwards!</i></b><span style="text-align: justify;"> </span><i style="text-align: justify;">C.A.D. has allowed 'us' to overcome many tedious tasks and now creating some wonderful patterns! </i><br/>
<i style="text-align: justify;"> </i><i><b>Scale of Difficulty; This all depends on the complexity of the pattern you need.</b></i><br/>
<i style="text-align: justify;"><b><br/></b></i>
<i style="text-align: justify;"><b>                                           Here are a few of them!..ENJOY!</b></i><br/>
<div style="text-align: left;">
<i><b><br/></b></i></div>
<div style="text-align: left;">
<i><b> This little project used the customers own Pearl and 16 "Princess-Cut" diamonds using CAD!</b></i></div>
<i style="text-align: justify;"><b>                                           </b></i><br/>
<div class="separator" style="clear: both; text-align: center;">
</div>
<div class="separator" style="clear: both; text-align: center;">
<a href="../../../images/unknown/Computer-Aided-Designing-(aka-CAD)-for-easier-Diam_img_001.png" imageanchor="1" style="margin-left: 1em; margin-right: 1em;"><img border="0" data-original-height="768" data-original-width="1024" height="480" loading="lazy" src="../../../images/unknown/Computer-Aided-Designing-(aka-CAD)-for-easier-Diam_img_001.png" width="640"/></a></div>
<div class="separator" style="clear: both; text-align: center;">
<br/></div>
<div class="separator" style="clear: both; text-align: center;">
<br/></div>
<div class="separator" style="clear: both; text-align: center;">
<a href="../../../images/unknown/Computer-Aided-Designing-(aka-CAD)-for-easier-Diam_img_002.png" imageanchor="1" style="margin-left: 1em; margin-right: 1em;"><img border="0" data-original-height="480" data-original-width="640" height="480" loading="lazy" src="../../../images/unknown/Computer-Aided-Designing-(aka-CAD)-for-easier-Diam_img_002.png" width="640"/></a></div>
<br/>
<div class="separator" style="clear: both; text-align: center;">
<a href="../../../images/unknown/Computer-Aided-Designing-(aka-CAD)-for-easier-Diam_img_003.png" imageanchor="1" style="margin-left: 1em; margin-right: 1em;"><img border="0" data-original-height="480" data-original-width="640" height="480" loading="lazy" src="../../../images/unknown/Computer-Aided-Designing-(aka-CAD)-for-easier-Diam_img_003.png" width="640"/></a></div>
<br/>
<div class="separator" style="clear: both; text-align: center;">
<a href="https://blogger.googleusercontent.com/img/b/R29vZ2xl/AVvXsEi20ARkCUkAZtU8xV43h7Fq-AUY7BAnhGi6dI6UCVn685HYLn2GWBT8S9s7Yd9zppkiGEwNs9DSMCiC-voO4wbiiZlNmohOyeb76T200ZusR3FXRlQYtftJqRALmHx6jj8uQFXhxvxplW8/s1600/GWR3.jpg" imageanchor="1" style="margin-left: 1em; margin-right: 1em;"><img border="0" data-original-height="480" data-original-width="640" height="300" loading="lazy" src="../../../images/unknown/Computer-Aided-Designing-(aka-CAD)-for-easier-Diam_img_004.png" width="400"/></a></div>
<br/>
<div class="separator" style="clear: both; text-align: center;">
<a href="../../../images/unknown/Computer-Aided-Designing-(aka-CAD)-for-easier-Diam_img_005.png" imageanchor="1" style="margin-left: 1em; margin-right: 1em;"><img border="0" data-original-height="480" data-original-width="640" height="480" loading="lazy" src="../../../images/unknown/Computer-Aided-Designing-(aka-CAD)-for-easier-Diam_img_005.png" width="640"/></a></div>
<br/>
<div class="separator" style="clear: both; text-align: center;">
<a href="https://blogger.googleusercontent.com/img/b/R29vZ2xl/AVvXsEgJ3GkZWF4vskTCRWNL1DI2eK_x7Mu19gqFKXS0EGyP9ZTJMy9rFnWa0ysi2Jebk6HjT8xdNUXheYj7mb3rdVFNsngkev2q8i7wNVKCdL8nKX0_ZaGV8wQPzTShxqnPRKxiAKwhafLvf7I/s1600/GWR5.jpg" imageanchor="1" style="margin-left: 1em; margin-right: 1em;"><img border="0" data-original-height="480" data-original-width="640" height="300" loading="lazy" src="../../../images/unknown/Computer-Aided-Designing-(aka-CAD)-for-easier-Diam_img_006.png" width="400"/></a></div>
<br/>
<div class="separator" style="clear: both; text-align: center;">
<a href="https://blogger.googleusercontent.com/img/b/R29vZ2xl/AVvXsEjD7NocN_nRG7R2EpAVmP3Xzguh4HSydhgyynLwjmy9tHcyCPyAT_Z5sgmsEW1ELGuHw3A1uDwEwMBtrzxqv0JcOtJqvVC3zNaubfgQbMpZ1zqcOJIWDVrlhYL9VOUgEn-hTv5YFS69ClI/s1600/GWR6.jpg" imageanchor="1" style="margin-left: 1em; margin-right: 1em;"><img border="0" data-original-height="480" data-original-width="640" height="300" loading="lazy" src="../../../images/unknown/Computer-Aided-Designing-(aka-CAD)-for-easier-Diam_img_007.png" width="400"/></a></div>
<br/>
<div class="separator" style="clear: both; text-align: center;">
<a href="../../../images/unknown/Computer-Aided-Designing-(aka-CAD)-for-easier-Diam_img_008.png" imageanchor="1" style="margin-left: 1em; margin-right: 1em;"><img border="0" data-original-height="480" data-original-width="640" height="480" loading="lazy" src="../../../images/unknown/Computer-Aided-Designing-(aka-CAD)-for-easier-Diam_img_008.png" width="640"/></a></div>
<div class="separator" style="clear: both; text-align: justify;">
<br/></div>
<div class="separator" style="clear: both; text-align: justify;">
<br/></div>
<div class="separator" style="clear: both; text-align: center;">
</div>
<br/>
<div class="separator" style="clear: both; text-align: center;">
<a href="../../../images/unknown/Computer-Aided-Designing-(aka-CAD)-for-easier-Diam_img_009.png" imageanchor="1" style="margin-left: 1em; margin-right: 1em;"><img border="0" data-original-height="1600" data-original-width="1222" height="640" loading="lazy" src="../../../images/unknown/Computer-Aided-Designing-(aka-CAD)-for-easier-Diam_img_009.png" width="488"/></a></div>
 <b><i>This is a photo of the 'program menu' that shows the gram weight, number of stones, total carat weight with different karats that could be needed and the stone sizes. </i></b><i>This detailed menu makes pricing so much easier, no more errors and guesswork! </i><br/>
<div class="separator" style="clear: both; text-align: center;">
</div>
<br/>
<div class="separator" style="clear: both; text-align: center;">
<a href="../../../images/unknown/Computer-Aided-Designing-(aka-CAD)-for-easier-Diam_img_010.png" imageanchor="1" style="margin-left: 1em; margin-right: 1em;"><img border="0" data-original-height="870" data-original-width="1600" height="347" loading="lazy" src="../../../images/unknown/Computer-Aided-Designing-(aka-CAD)-for-easier-Diam_img_010.png" width="640"/></a></div>
<div class="separator" style="clear: both; text-align: justify;">
 <b><i>If you wanted an "Azure' inside-pattern, so be it, done!</i></b> <i>All measurements &amp; designing was done in a few minutes. I decided on 33 stones for each of the two rings named "<b>Waterfall</b>", as it looks like '<b>water falling off the sides of the ring</b>'..:&gt;)</i></div>
<br/>
<div class="separator" style="clear: both; text-align: center;">
<a href="../../../images/unknown/Computer-Aided-Designing-(aka-CAD)-for-easier-Diam_img_011.png" imageanchor="1" style="margin-left: 1em; margin-right: 1em;"><img border="0" data-original-height="1600" data-original-width="1055" height="640" loading="lazy" src="../../../images/unknown/Computer-Aided-Designing-(aka-CAD)-for-easier-Diam_img_011.png" width="420"/></a></div>
<br/>
<div class="separator" style="clear: both; text-align: center;">
<a href="../../../images/unknown/Computer-Aided-Designing-(aka-CAD)-for-easier-Diam_img_012.png" imageanchor="1" style="margin-left: 1em; margin-right: 1em;"><img border="0" data-original-height="1600" data-original-width="1158" height="640" loading="lazy" src="../../../images/unknown/Computer-Aided-Designing-(aka-CAD)-for-easier-Diam_img_012.png" width="462"/></a></div>
 <b><i>This was a section for an unusual necklace that my student client won "First Place, with Honours" in a school in Toronto.</i></b> <i>I was pleased to teach her the detailed techniques in using stone placements in using CAD, <b>she works now for Tiffany's in London, England!</b>...BTW, I did all of the stone setting!</i><br/>
<div class="separator" style="clear: both; text-align: center;">
</div>
<br/>
<div class="separator" style="clear: both; text-align: center;">
<a href="https://blogger.googleusercontent.com/img/b/R29vZ2xl/AVvXsEh_ccXpkcjLqrNyWtyVY4CdJXQ9eFsm0yW5TvPqi26YVclVQn9UkjGtdqvoFgalUwm-jSOoRVtwWycBSI-kKSsD33mqee2d73GAD2uhG34M8qqn54_rEHygornduJ5dkgcoeA4maN7bHsY/s1600/DSC00004.JPG" imageanchor="1" style="margin-left: 1em; margin-right: 1em;"><img border="0" data-original-height="768" data-original-width="1024" height="300" loading="lazy" src="../../../images/unknown/Computer-Aided-Designing-(aka-CAD)-for-easier-Diam_img_013.png" width="400"/></a></div>
<br/>
<div class="separator" style="clear: both; text-align: center;">
<a href="https://blogger.googleusercontent.com/img/b/R29vZ2xl/AVvXsEj28QJcfRUcOytnkxdP7lN0pGQfbfQUQBhL7BbHGDBbaw8jpZaADRAjNst_pIy_PDvemYciVgPJ4sOGcn77xCiETCwKo20jGOB2ygtM6VnP8rUHr2AC_h-c7ZZMGi0rc4F-xm-KsQFQIyE/s1600/DSC00006.JPG" imageanchor="1" style="margin-left: 1em; margin-right: 1em;"><img border="0" data-original-height="1200" data-original-width="1600" height="300" loading="lazy" src="../../../images/unknown/Computer-Aided-Designing-(aka-CAD)-for-easier-Diam_img_014.png" width="400"/></a></div>
<br/>
<div class="separator" style="clear: both; text-align: center;">
<a href="../../../images/unknown/Computer-Aided-Designing-(aka-CAD)-for-easier-Diam_img_015.png" imageanchor="1" style="margin-left: 1em; margin-right: 1em;"><img border="0" data-original-height="1200" data-original-width="1600" height="240" loading="lazy" src="../../../images/unknown/Computer-Aided-Designing-(aka-CAD)-for-easier-Diam_img_015.png" width="320"/></a></div>
<br/>
 <i><b>I lost count how many stones were being used...many! </b>I remember this took me over 3 hours of setting for all two pieces.</i><br/>
<div class="separator" style="clear: both; text-align: center;">
<a href="https://blogger.googleusercontent.com/img/b/R29vZ2xl/AVvXsEjE5kP1eYx-xdV9lR6Hbuoo6_HLhln75BCWO0FIXmfc43JMcpGzQR4BggLaoKa_szLYZQQWMNN87s0AZBDHEoSQK0EKooCSVDodSFaf7rI5pKPYvs-3a36ardHYnFKsdAP4m209mTth2hU/s1600/DSC00008.JPG" imageanchor="1" style="margin-left: 1em; margin-right: 1em;"><img border="0" data-original-height="1200" data-original-width="1600" height="300" loading="lazy" src="../../../images/unknown/Computer-Aided-Designing-(aka-CAD)-for-easier-Diam_img_016.png" width="400"/></a></div>
<br/>
<div class="separator" style="clear: both; text-align: center;">
<a href="https://blogger.googleusercontent.com/img/b/R29vZ2xl/AVvXsEgMTBUcxqpsdt02Be_mFrNEb4kYQOUod0HodhoDtINGxcGNZAS0dByZuPDn_SAp8Ky96IxDOFt1VTQ2LD6kjC8rmhOJu0PTmmfVoCe1vJ_JObtDoNd7IqcM8gg05ypbyMzDDQpKDOaM_JU/s1600/DSC00011.JPG" imageanchor="1" style="margin-left: 1em; margin-right: 1em;"><img border="0" data-original-height="480" data-original-width="640" height="300" loading="lazy" src="../../../images/unknown/Computer-Aided-Designing-(aka-CAD)-for-easier-Diam_img_017.png" width="400"/></a></div>
<br/>
<i><b> Here is my "Guinness World Record" poker-chip. </b>Although this is only a replica from the original, I keep this to show how a computer did all of the multi-piece designing<b>. This weighs in a 75 grams of silver, the original weighed only 136 grams, of 22 karat gold.</b></i><br/>
<i><b> There are 66, 1.25mm stones in the number "8". </b></i><br/>
<div class="separator" style="clear: both; text-align: center;">
<a href="https://blogger.googleusercontent.com/img/b/R29vZ2xl/AVvXsEg0s6_SQJ7EeUHX03YSd7mh446AwthykR3VBngGuGzwXLO-h9njKlB8G5jCj1CEq9fdRp1WvKKunor6u5cLPx934WMxo_qSwkRz5r4u__li-VWc7pyLRQNfjUB4VywDmC05iOcPd-Cj4Eo/s1600/DSC00025+%25282%2529.JPG" imageanchor="1" style="margin-left: 1em; margin-right: 1em;"><img border="0" data-original-height="1200" data-original-width="1600" height="300" loading="lazy" src="../../../images/unknown/Computer-Aided-Designing-(aka-CAD)-for-easier-Diam_img_018.png" width="400"/></a></div>
<div class="separator" style="clear: both; text-align: center;">
<br/></div>
<div class="separator" style="clear: both; text-align: justify;">
<i><b>The "number 7" has 49, 1.5 mm stones. </b>All of the stones had 2 'mini-claws' for each stone being set. If just one claw got broken...(let's not think of the problem!)<b>The setting used was a "Shared-Claw" technique.</b></i></div>
<div class="separator" style="clear: both; text-align: center;">
<a href="../../../images/unknown/Computer-Aided-Designing-(aka-CAD)-for-easier-Diam_img_019.png" imageanchor="1" style="margin-left: 1em; margin-right: 1em;"><img border="0" data-original-height="640" data-original-width="640" height="320" loading="lazy" src="../../../images/unknown/Computer-Aided-Designing-(aka-CAD)-for-easier-Diam_img_019.png" width="320"/></a></div>
 <b><i> </i></b><br/>
<b><i>This was a 3-piece construction..</i></b><i>(the main disk, plus the two numbers) and using a Laser soldering process. This disk would have to be heated up too much and many problems would then ensue.</i><br/>
<div class="separator" style="clear: both; text-align: center;">
<a href="../../../images/unknown/Computer-Aided-Designing-(aka-CAD)-for-easier-Diam_img_020.png" imageanchor="1" style="margin-left: 1em; margin-right: 1em;"><img border="0" data-original-height="640" data-original-width="516" height="640" loading="lazy" src="../../../images/unknown/Computer-Aided-Designing-(aka-CAD)-for-easier-Diam_img_020.png" width="515"/></a></div>
<br/>
<br/>
<i><b> </b></i><b><i>This is the side view of the Poker-Chip. Each of the 17 stones were 6.25 mm's in size.</i></b><br/>
<div class="separator" style="clear: both; text-align: center;">
<a href="../../../images/unknown/Computer-Aided-Designing-(aka-CAD)-for-easier-Diam_img_021.png" imageanchor="1" style="clear: left; float: left; margin-bottom: 1em; margin-right: 1em;"><img border="0" data-original-height="1200" data-original-width="1600" height="480" loading="lazy" src="../../../images/unknown/Computer-Aided-Designing-(aka-CAD)-for-easier-Diam_img_021.png" width="640"/></a><b style="text-align: left;"><i>This disk, is just but one of 7 pieces all created by C.A.D. and was made to worn as a necklace!</i></b><a href="../../../images/unknown/Computer-Aided-Designing-(aka-CAD)-for-easier-Diam_img_022.png" imageanchor="1" style="margin-left: 1em; margin-right: 1em;"><img border="0" data-original-height="702" data-original-width="978" height="457" loading="lazy" src="../../../images/unknown/Computer-Aided-Designing-(aka-CAD)-for-easier-Diam_img_022.png" width="640"/></a></div>
<i><b><br/></b></i>
<br/>
<div class="separator" style="clear: both; text-align: center;">
<i style="text-align: left;"></i></div>
<div style="text-align: left;">
<i style="text-align: left;"><i><b> This was a medallion for the "Miami Dolphin' owner! </b>I had to decide where all of the different stone sizes were to be set.</i><a href="../../../images/unknown/Computer-Aided-Designing-(aka-CAD)-for-easier-Diam_img_023.png" imageanchor="1" style="margin-left: 1em; margin-right: 1em; text-align: center;"><img border="0" data-original-height="480" data-original-width="640" height="480" loading="lazy" src="../../../images/unknown/Computer-Aided-Designing-(aka-CAD)-for-easier-Diam_img_023.png" width="640"/></a></i></div>
<br/>
<br/>
<b><i>Any comments please email me "gerrylewy18 (at) gmail.com"</i></b><br/>
<br/></div>
<div style="clear: both;"></div>
</div>
        </div>
    </div>
</body>
</html>