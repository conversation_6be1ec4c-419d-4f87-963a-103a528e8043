#!/usr/bin/env python3
"""
Download Missing Articles (Multi-Stream)

Download the 156 missing articles identified by the comparison analysis.
Uses parallel streams for faster downloading with the existing crawler infrastructure.
"""

import json
import sys
import time
import asyncio
import concurrent.futures
from pathlib import Path
from typing import List, Dict
import threading
from queue import Queue

# Add current directory to path
sys.path.insert(0, str(Path(__file__).parent))

from crawler import BlogCrawler
import config

class MissingArticleDownloader:
    def __init__(self, max_streams: int = 5):
        self.crawler = BlogCrawler()
        self.max_streams = max_streams
        self.download_lock = threading.Lock()
        self.results_lock = threading.Lock()
        self.index_update_lock = threading.Lock()
        
    def load_missing_articles(self) -> List[Dict]:
        """Load missing articles from the comparison report"""
        report_file = Path("output/metadata/correct_comparison_report.json")
        
        if not report_file.exists():
            print(f"❌ Missing articles report not found: {report_file}")
            print("   Please run correct_comparison.py first to generate the report")
            return []
        
        with open(report_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        missing_articles = data.get('missing_articles', [])
        print(f"📋 Loaded {len(missing_articles)} missing articles from report")
        
        return missing_articles
    
    def download_single_article(self, article: Dict, stream_id: int, article_index: int, total_articles: int) -> Dict:
        """Download a single article (thread-safe)"""
        result = {
            'article': article,
            'success': False,
            'skipped': False,
            'error': None,
            'stream_id': stream_id
        }

        try:
            title_preview = article['title'][:40] + "..." if len(article['title']) > 40 else article['title']
            print(f"   🔄 Stream-{stream_id} [{article_index:3d}/{total_articles}] - {title_preview}")

            # Convert to post format expected by crawler
            post = {
                'url': article['url'],
                'title': article['title'],
                'year': article.get('year', 'unknown'),
                'source': 'missing_articles_download'
            }

            # Thread-safe check if already downloaded
            with self.download_lock:
                if self.crawler.is_post_downloaded(post['url']):
                    print(f"      ⏭️  Stream-{stream_id} - Already downloaded (skipping)")
                    result['skipped'] = True
                    return result

            # Download the post (this is the time-consuming part)
            success = self.crawler.download_post_content(post)

            if success:
                print(f"      ✅ Stream-{stream_id} - Downloaded successfully")
                result['success'] = True
            else:
                print(f"      ❌ Stream-{stream_id} - Download failed")
                result['error'] = "Download failed"

            # Small delay between downloads per stream
            time.sleep(config.REQUEST_DELAY / self.max_streams)

        except Exception as e:
            print(f"      ❌ Stream-{stream_id} - Error: {e}")
            result['error'] = str(e)

        return result

    def download_missing_articles_parallel(self, missing_articles: List[Dict]) -> Dict:
        """Download all missing articles using parallel streams"""

        if not missing_articles:
            print("✅ No missing articles to download!")
            return {'success': True, 'downloaded': 0}

        # Group by year for organized processing
        articles_by_year = {}
        for article in missing_articles:
            year = article.get('year', 'unknown')
            if year not in articles_by_year:
                articles_by_year[year] = []
            articles_by_year[year].append(article)

        results = {
            'total_attempted': 0,
            'successful': 0,
            'failed': 0,
            'skipped': 0,
            'by_year': {}
        }

        print(f"\n🎯 Starting PARALLEL download of {len(missing_articles)} missing articles...")
        print(f"🚀 Using {self.max_streams} parallel streams for faster downloading")
        print(f"📊 Years to process: {sorted(articles_by_year.keys(), reverse=True)}")
        print("=" * 80)

        start_time = time.time()
        
        # Process years in reverse chronological order (latest first)
        for year in sorted(articles_by_year.keys(), reverse=True):
            articles = articles_by_year[year]

            year_results = {
                'attempted': 0,
                'successful': 0,
                'failed': 0,
                'skipped': 0
            }

            print(f"\n📅 Processing {year} ({len(articles)} missing articles)")
            print(f"🚀 Using {self.max_streams} parallel streams")
            print("=" * 60)

            try:
                # Process articles in batches using ThreadPoolExecutor
                with concurrent.futures.ThreadPoolExecutor(max_workers=self.max_streams) as executor:
                    # Create futures for all articles in this year
                    futures = []
                    for i, article in enumerate(articles, 1):
                        future = executor.submit(
                            self.download_single_article,
                            article,
                            (i % self.max_streams) + 1,  # Stream ID 1-5
                            i,
                            len(articles)
                        )
                        futures.append(future)

                    # Process completed downloads
                    completed_count = 0
                    for future in concurrent.futures.as_completed(futures):
                        try:
                            result = future.result()
                            completed_count += 1

                            # Update results thread-safely
                            with self.results_lock:
                                if result['skipped']:
                                    year_results['skipped'] += 1
                                    results['skipped'] += 1
                                elif result['success']:
                                    year_results['successful'] += 1
                                    results['successful'] += 1
                                    year_results['attempted'] += 1
                                    results['total_attempted'] += 1
                                else:
                                    year_results['failed'] += 1
                                    results['failed'] += 1
                                    year_results['attempted'] += 1
                                    results['total_attempted'] += 1

                                # Update index every 10 successful downloads
                                if results['successful'] % 10 == 0 and result['success']:
                                    with self.index_update_lock:
                                        print(f"      🎯 Updating index... ({results['successful']} completed)")
                                        self.crawler.generate_index()

                            # Show progress
                            if completed_count % 5 == 0 or completed_count == len(articles):
                                print(f"   📊 Progress: {completed_count}/{len(articles)} completed")

                        except Exception as e:
                            print(f"   ❌ Error processing future result: {e}")
                            with self.results_lock:
                                year_results['failed'] += 1
                                results['failed'] += 1

            except KeyboardInterrupt:
                print(f"\n⏹️  Download interrupted by user in {year}")
                print(f"📊 Progress so far: {results['successful']} successful, {results['failed']} failed")

                # Save progress
                self.save_progress_report(results, articles_by_year, year, 0)
                return results

            # Store year results
            results['by_year'][year] = year_results

            # Update index after each year
            print(f"\n🎯 Updating index after completing {year}...")
            self.crawler.generate_index()

            # Show year summary
            print(f"📊 {year} Summary: {year_results['successful']} successful, {year_results['failed']} failed, {year_results['skipped']} skipped")
        
        # Final summary
        elapsed_time = time.time() - start_time
        print(f"\n" + "=" * 80)
        print("🎉 DOWNLOAD COMPLETE!")
        print("=" * 80)
        print(f"📊 Final Results:")
        print(f"   ✅ Successful: {results['successful']}")
        print(f"   ❌ Failed: {results['failed']}")
        print(f"   ⏭️  Skipped: {results['skipped']}")
        print(f"   ⏱️  Time: {elapsed_time/60:.1f} minutes")
        
        if results['successful'] > 0:
            print(f"\n🎯 Generating final index...")
            self.crawler.generate_index()
            print(f"📁 Check {config.OUTPUT_DIR}/index.html to browse the complete archive")
        
        # Save final report
        self.save_final_report(results)
        
        return results
    
    def save_progress_report(self, results: Dict, articles_by_year: Dict, current_year: str, current_index: int):
        """Save progress report in case of interruption"""
        progress_report = {
            'interrupted_at': f"{current_year} - article {current_index}",
            'results': results,
            'remaining_articles': self.calculate_remaining(articles_by_year, current_year, current_index)
        }
        
        progress_file = Path("output/metadata/download_progress.json")
        with open(progress_file, 'w', encoding='utf-8') as f:
            json.dump(progress_report, f, indent=2, ensure_ascii=False)
        
        print(f"💾 Progress saved to: {progress_file}")
    
    def calculate_remaining(self, articles_by_year: Dict, current_year: str, current_index: int) -> int:
        """Calculate remaining articles to download"""
        remaining = 0
        
        # Count remaining in current year
        if current_year in articles_by_year:
            remaining += len(articles_by_year[current_year]) - current_index
        
        # Count all articles in earlier years (reverse chronological)
        years = sorted(articles_by_year.keys(), reverse=True)
        current_year_index = years.index(current_year) if current_year in years else -1
        
        for year in years[current_year_index + 1:]:
            remaining += len(articles_by_year[year])
        
        return remaining
    
    def save_final_report(self, results: Dict):
        """Save final download report"""
        final_report = {
            'download_date': '2025-07-06',
            'results': results,
            'success_rate': results['successful'] / max(results['total_attempted'], 1) * 100
        }
        
        report_file = Path("output/metadata/missing_download_report.json")
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(final_report, f, indent=2, ensure_ascii=False)
        
        print(f"📋 Final download report saved to: {report_file}")
    
    def run_download(self):
        """Run the complete missing articles download process"""
        print("=" * 80)
        print("🎯 MISSING ARTICLES DOWNLOADER")
        print("=" * 80)
        
        # Load missing articles
        missing_articles = self.load_missing_articles()
        
        if not missing_articles:
            print("❌ No missing articles found. Please run correct_comparison.py first.")
            return
        
        # Show summary
        articles_by_year = {}
        for article in missing_articles:
            year = article.get('year', 'unknown')
            articles_by_year[year] = articles_by_year.get(year, 0) + 1
        
        print(f"\n📊 Missing Articles Summary:")
        for year in sorted(articles_by_year.keys(), reverse=True):
            print(f"   📅 {year}: {articles_by_year[year]} articles")
        
        # Confirm download
        print(f"\n🤔 Ready to download {len(missing_articles)} missing articles.")
        response = input("Proceed with download? (y/N): ")
        
        if response.lower() != 'y':
            print("📋 Download cancelled.")
            return
        
        # Start download
        results = self.download_missing_articles(missing_articles)
        
        if results['successful'] > 0:
            print(f"\n🎉 Successfully downloaded {results['successful']} missing articles!")
            print(f"📈 Your archive is now more complete!")
        else:
            print(f"\n⚠️  No articles were successfully downloaded.")

def main():
    try:
        downloader = MissingArticleDownloader()
        downloader.run_download()
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
