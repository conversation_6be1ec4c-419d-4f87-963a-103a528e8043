#!/usr/bin/env python3
"""
Investigate Missing Dates

Focus on the dates with the most missing articles:
- 09/01: Found 1/20 (missing 19)
- 09/15: Found 5/16 (missing 11) 
- 09/22: Found 23/30 (missing 7)
- 07/07: Found 3/11 (missing 8)
- 07/21: Found 3/10 (missing 7)
- 08/11: Found 2/7 (missing 5)

Try different URL formats and pagination approaches for these dates.
"""

import requests
import json
import re
import time
from pathlib import Path
from typing import List, Dict, Tuple
from datetime import datetime
from bs4 import BeautifulSoup
from urllib.parse import urljoin

import config

class MissingDatesInvestigator:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        
        # Focus on the most problematic dates
        self.problem_dates = {
            '09/01': 20,  # missing 19
            '09/15': 16,  # missing 11
            '09/22': 30,  # missing 7
            '07/07': 11,  # missing 8
            '07/21': 10,  # missing 7
            '08/11': 7,   # missing 5
        }
    
    def try_multiple_url_formats(self, date_key: str, expected_count: int) -> List[Dict]:
        """Try multiple URL formats for a problematic date"""
        month, day = date_key.split('/')
        print(f"\n🔍 INVESTIGATING {date_key} - Expected: {expected_count} articles")
        
        # Multiple URL formats to try
        url_formats = [
            # Standard archive format
            f"https://gerrysdiamondsettingessays.blogspot.com/2024_{month}_{day}_archive.html",
            
            # Search by date range
            f"https://gerrysdiamondsettingessays.blogspot.com/search?updated-min=2024-{month}-{day}T00:00:00-05:00&updated-max=2024-{month}-{day}T23:59:59-05:00&max-results=50",
            
            # Alternative date formats
            f"https://gerrysdiamondsettingessays.blogspot.com/2024/{month}/{day}/",
            
            # Search with different time zones
            f"https://gerrysdiamondsettingessays.blogspot.com/search?updated-min=2024-{month}-{day}T00:00:00Z&updated-max=2024-{month}-{day}T23:59:59Z&max-results=50",
            
            # Broader search (day before to day after)
            f"https://gerrysdiamondsettingessays.blogspot.com/search?updated-min=2024-{month}-{int(day)-1:02d}T00:00:00-05:00&updated-max=2024-{month}-{int(day)+1:02d}T23:59:59-05:00&max-results=50",
        ]
        
        all_articles = []
        
        for i, url in enumerate(url_formats, 1):
            print(f"   🔗 Method {i}: {url[:80]}...")
            
            try:
                # Try with pagination
                articles = self.crawl_with_pagination(url, date_key)
                
                if articles:
                    print(f"      ✅ Found {len(articles)} articles")
                    
                    # Add unique articles
                    for article in articles:
                        if not any(a['url'] == article['url'] for a in all_articles):
                            all_articles.append(article)
                else:
                    print(f"      ❌ No articles found")
                    
            except Exception as e:
                print(f"      ❌ Error: {e}")
            
            time.sleep(0.5)  # Be respectful
        
        unique_count = len(all_articles)
        print(f"   📊 TOTAL UNIQUE: {unique_count}/{expected_count} articles")
        
        if unique_count >= expected_count:
            print(f"   🎉 SUCCESS: Found all expected articles!")
        else:
            print(f"   ⚠️  Still missing: {expected_count - unique_count} articles")
        
        return all_articles
    
    def crawl_with_pagination(self, base_url: str, date_key: str) -> List[Dict]:
        """Crawl with pagination support"""
        articles = []
        
        # Try pagination if it's a search URL
        if "search?" in base_url:
            start_index = 1
            max_results = 20
            consecutive_empty = 0
            
            while consecutive_empty < 2 and start_index <= 100:  # Safety limit
                if start_index == 1:
                    paginated_url = base_url
                else:
                    separator = "&" if "?" in base_url else "?"
                    paginated_url = f"{base_url}{separator}start-index={start_index}"
                
                try:
                    response = self.session.get(paginated_url, timeout=20)
                    
                    if response.status_code == 200:
                        soup = BeautifulSoup(response.content, 'html.parser')
                        post_containers = soup.select(config.SELECTORS["post_container"])
                        
                        page_articles = 0
                        for container in post_containers:
                            try:
                                title_elem = container.select_one(config.SELECTORS["post_title"])
                                if title_elem and title_elem.get('href'):
                                    article_url = title_elem.get('href')
                                    title = title_elem.get_text(strip=True)
                                    
                                    # Verify it's from 2024
                                    year_match = re.search(r'/(\d{4})/', article_url)
                                    if year_match and int(year_match.group(1)) == 2024:
                                        article = {
                                            'url': article_url,
                                            'title': title,
                                            'date': date_key,
                                            'source': 'pagination_search'
                                        }
                                        
                                        # Avoid duplicates
                                        if not any(a['url'] == article_url for a in articles):
                                            articles.append(article)
                                            page_articles += 1
                            except:
                                continue
                        
                        if page_articles == 0:
                            consecutive_empty += 1
                        else:
                            consecutive_empty = 0
                            
                        start_index += max_results
                    else:
                        consecutive_empty += 1
                        
                except Exception as e:
                    consecutive_empty += 1
                    break
        else:
            # Single page crawl
            try:
                response = self.session.get(base_url, timeout=20)
                
                if response.status_code == 200:
                    soup = BeautifulSoup(response.content, 'html.parser')
                    post_containers = soup.select(config.SELECTORS["post_container"])
                    
                    for container in post_containers:
                        try:
                            title_elem = container.select_one(config.SELECTORS["post_title"])
                            if title_elem and title_elem.get('href'):
                                article_url = title_elem.get('href')
                                title = title_elem.get_text(strip=True)
                                
                                # Verify it's from 2024
                                year_match = re.search(r'/(\d{4})/', article_url)
                                if year_match and int(year_match.group(1)) == 2024:
                                    article = {
                                        'url': article_url,
                                        'title': title,
                                        'date': date_key,
                                        'source': 'single_page'
                                    }
                                    articles.append(article)
                        except:
                            continue
            except:
                pass
        
        return articles
    
    def investigate_all_problem_dates(self) -> Dict:
        """Investigate all problematic dates"""
        print("=" * 80)
        print("🔍 INVESTIGATING MISSING DATES")
        print("=" * 80)
        print("🎯 Focus on dates with most missing articles")
        print()
        
        all_results = {}
        total_found = 0
        total_expected = 0
        
        for date_key, expected_count in self.problem_dates.items():
            total_expected += expected_count
            
            articles = self.try_multiple_url_formats(date_key, expected_count)
            total_found += len(articles)
            
            all_results[date_key] = {
                'expected': expected_count,
                'found': len(articles),
                'articles': articles
            }
        
        # Summary
        print("\n" + "=" * 80)
        print("📊 INVESTIGATION SUMMARY")
        print("=" * 80)
        
        for date_key, result in all_results.items():
            expected = result['expected']
            found = result['found']
            status = "✅ COMPLETE" if found >= expected else f"⚠️  MISSING {expected - found}"
            print(f"📅 {date_key}: {found}/{expected} articles - {status}")
        
        print(f"\n📊 TOTAL: {total_found}/{total_expected} articles")
        
        if total_found >= total_expected:
            print("🎉 SUCCESS: Found all articles for problem dates!")
        else:
            print(f"⚠️  Still missing: {total_expected - total_found} articles")
        
        # Save results
        results = {
            'investigation_date': datetime.now().isoformat(),
            'problem_dates': self.problem_dates,
            'results': all_results,
            'total_expected': total_expected,
            'total_found': total_found
        }
        
        output_path = Path(config.OUTPUT_DIR) / "metadata" / "missing_dates_investigation.json"
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        
        print(f"\n📋 Investigation results saved to: {output_path}")
        
        return results

def main():
    investigator = MissingDatesInvestigator()
    results = investigator.investigate_all_problem_dates()
    
    print(f"\n🎯 NEXT STEPS:")
    if results['total_found'] >= results['total_expected']:
        print("   ✅ All problem dates resolved!")
        print("   🔄 Combine with previous results for complete 2024 list")
    else:
        print("   🔍 Some articles still missing from these dates")
        print("   📊 May need to investigate Blogger's content delivery")

if __name__ == "__main__":
    main()
